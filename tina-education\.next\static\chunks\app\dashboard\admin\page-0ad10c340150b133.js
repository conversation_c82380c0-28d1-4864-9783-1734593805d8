(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1833],{3443:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(5155),n=r(2115),s=r(5695);function o(e){let{userId:t,currentRole:r,userName:o}=e,l=(0,s.useRouter)(),[u,c]=(0,n.useState)(!1),[i,d]=(0,n.useState)(""),p=async e=>{if(!u&&e!==r&&confirm("Are you sure you want to change ".concat(o,"'s role from ").concat(r," to ").concat(e,"?"))){c(!0),d("");try{let r=await fetch("/api/admin/users/".concat(t,"/role"),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({role:e})}),a=await r.json();if(!r.ok)throw Error(a.error||"Failed to update user role");l.refresh()}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{c(!1)}}};return(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[i&&(0,a.jsx)("span",{className:"text-xs text-red-600 mr-2",children:i}),"USER"!==r&&(0,a.jsx)("button",{onClick:()=>p("USER"),disabled:u,className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50",title:"Make User",children:u?"...":"\uD83D\uDC64"}),"REVIEWER"!==r&&(0,a.jsx)("button",{onClick:()=>p("REVIEWER"),disabled:u,className:"px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50",title:"Make Reviewer",children:u?"...":"\uD83D\uDCD6"}),"ADMIN"!==r&&(0,a.jsx)("button",{onClick:()=>p("ADMIN"),disabled:u,className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50",title:"Make Admin",children:u?"...":"\uD83D\uDC51"})]})}},4255:(e,t,r)=>{Promise.resolve().then(r.bind(r,8057)),Promise.resolve().then(r.bind(r,3443)),Promise.resolve().then(r.t.bind(r,3063,23))},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let a=r(2115);function n(e,t){let r=(0,a.useRef)(null),n=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=r.current;e&&(r.current=null,e());let t=n.current;t&&(n.current=null,t())}else e&&(r.current=s(e,a)),t&&(n.current=s(t,a))},[e,t])}function s(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8057:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(5155),n=r(2115),s=r(5695);function o(e){let{applicationId:t,currentStatus:r,applicantName:o}=e,l=(0,s.useRouter)(),[u,c]=(0,n.useState)(!1),[i,d]=(0,n.useState)(""),p=async e=>{if(!u&&confirm("APPROVE"===e?"Are you sure you want to approve ".concat(o,"'s reviewer application?"):"REJECT"===e?"Are you sure you want to reject ".concat(o,"'s reviewer application?"):"Mark ".concat(o,"'s application as under review?"))){c(!0),d("");try{let r=await fetch("/api/admin/reviewer-applications/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e})}),a=await r.json();if(!r.ok)throw Error(a.error||"Failed to update application");l.refresh()}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{c(!1)}}};return"APPROVED"===r||"REJECTED"===r?(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"APPROVED"===r?"✅ Approved":"❌ Rejected"}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[i&&(0,a.jsx)("span",{className:"text-xs text-red-600",children:i}),"PENDING"===r&&(0,a.jsx)("button",{onClick:()=>p("UNDER_REVIEW"),disabled:u,className:"px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50",children:u?"...":"Review"}),(0,a.jsx)("button",{onClick:()=>p("APPROVE"),disabled:u,className:"px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:opacity-50",children:u?"...":"Approve"}),(0,a.jsx)("button",{onClick:()=>p("REJECT"),disabled:u,className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50",children:u?"...":"Reject"})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3063,8441,1684,7358],()=>t(4255)),_N_E=e.O()}]);