(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},8030:(e,s,r)=>{"use strict";r.d(s,{default:()=>l});var t=r(5155),o=r(2115),a=r(5695);function l(e){let{user:s}=e,r=(0,a.useRouter)(),[l,i]=(0,o.useState)(!1),[n,d]=(0,o.useState)(""),[u,c]=(0,o.useState)({name:s.name||"",bio:s.bio||"",affiliation:s.affiliation||"",expertise:s.expertise||"",phone:s.phone||"",website:s.website||"",orcid:s.orcid||""}),m=async e=>{e.preventDefault(),i(!0),d("");try{let e=await fetch("/api/profile",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(u)}),s=await e.json();if(!e.ok)throw Error(s.error||"Failed to update profile");d("Profile updated successfully!"),r.refresh()}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{i(!1)}},b=e=>{c({...u,[e.target.name]:e.target.value})};return(0,t.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[n&&(0,t.jsx)("div",{className:"p-4 rounded-md ".concat(n.includes("successfully")?"bg-green-50 border border-green-200 text-green-700":"bg-red-50 border border-red-200 text-red-700"),children:n}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Basic Information"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",value:u.name,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your full name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,t.jsx)("input",{type:"email",id:"email",value:s.email,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Email cannot be changed. Contact support if needed."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,t.jsx)("input",{type:"tel",id:"phone",name:"phone",value:u.phone,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your phone number"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700",children:"Website"}),(0,t.jsx)("input",{type:"url",id:"website",name:"website",value:u.website,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"https://your-website.com"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Professional Information"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"affiliation",className:"block text-sm font-medium text-gray-700",children:"Affiliation"}),(0,t.jsx)("input",{type:"text",id:"affiliation",name:"affiliation",value:u.affiliation,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"University, Institution, or Organization"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"orcid",className:"block text-sm font-medium text-gray-700",children:"ORCID iD"}),(0,t.jsx)("input",{type:"text",id:"orcid",name:"orcid",value:u.orcid,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"0000-0000-0000-0000"}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Your ORCID identifier (optional)"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"expertise",className:"block text-sm font-medium text-gray-700",children:"Areas of Expertise"}),(0,t.jsx)("textarea",{id:"expertise",name:"expertise",rows:3,value:u.expertise,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"List your areas of expertise, research interests, or specializations"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700",children:"Biography"}),(0,t.jsx)("textarea",{id:"bio",name:"bio",rows:4,value:u.bio,onChange:b,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Tell us about yourself, your background, and your interests"})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:l,className:"px-6 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:l?"Updating...":"Update Profile"})})]})}},8489:(e,s,r)=>{Promise.resolve().then(r.bind(r,8030)),Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.t.bind(r,3063,23))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,3063,8441,1684,7358],()=>s(8489)),_N_E=e.O()}]);