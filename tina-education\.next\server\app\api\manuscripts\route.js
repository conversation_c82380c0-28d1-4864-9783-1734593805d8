"use strict";(()=>{var e={};e.id=8001,e.ids=[8001],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},13440:e=>{e.exports=require("util/types")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},36686:e=>{e.exports=require("diagnostics_channel")},41204:e=>{e.exports=require("string_decoder")},43470:(e,t,r)=>{r.d(t,{Q:()=>n});var o=r(88249),i=r(42335);async function n(e,t,r){try{console.log("\uD83D\uDCC4 Starting PDF generation...");let n=await o.PDFDocument.create(),p=n.addPage(),{width:c,height:u}=p.getSize(),d={regular:await n.embedFont(o.StandardFonts.Helvetica),bold:await n.embedFont(o.StandardFonts.HelveticaBold),italic:await n.embedFont(o.StandardFonts.HelveticaOblique),boldItalic:await n.embedFont(o.StandardFonts.HelveticaBoldOblique)},g={page:p,yPosition:u-50,fonts:d,margin:50,maxWidth:c-100,lineHeight:16,pdfDoc:n,pageWidth:c,pageHeight:u};for(let i of(t&&(g.yPosition=s(g,t,{font:d.bold,size:20,color:(0,o.rgb)(0,0,0)}),g.yPosition-=20),r&&(g.yPosition=s(g,`Author: ${r}`,{font:d.regular,size:12,color:(0,o.rgb)(.5,.5,.5)}),g.yPosition-=20),function(e){let t=[],r=e.replace(/&nbsp;/g," ").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/<(strong|b)([^>]*)>(.*?)<\/(strong|b)>/gi,"**BOLD_START**$3**BOLD_END**").replace(/<(em|i)([^>]*)>(.*?)<\/(em|i)>/gi,"**ITALIC_START**$3**ITALIC_END**").split(/(<\/?(?:h[1-6]|p|ul|ol|li|br)[^>]*>)/gi),o=null,i=0;for(let e=0;e<r.length;e++){let n=r[e].trim();if(n)if(n.startsWith("<")){let e=n.match(/<\/?([a-z0-9]+)/i);if(!e)continue;let r=e[1].toLowerCase(),s=n.startsWith("</");r.match(/^h[1-6]$/)?s?o&&(o.content=a(o.content),t.push(o),o=null):o={type:"heading",content:"",level:parseInt(r.charAt(1))}:"p"===r?s?o&&(o.content=a(o.content),t.push(o),o=null):o={type:"paragraph",content:""}:"br"===r?o?o.content+="\n":t.push({type:"text",content:"\n"}):"ul"===r||"ol"===r?s?i=Math.max(0,i-1):i++:"li"===r&&(s?o&&(o.content=a(o.content),t.push(o),o=null):o={type:"list-item",content:"",level:i})}else if(o)o.content+=n;else{let e=a(n);t.push({type:"paragraph",content:e})}}return o&&(o.content=a(o.content),t.push(o)),t.filter(e=>e.content.trim().length>0)}(e)))g.yPosition=function(e,t){switch(t.type){case"heading":var r,i,n=e,a=t;let p=a.level||1,c=[18,16,14,13,12,11][Math.min(p-1,5)];return n.yPosition<n.pageHeight-100&&(n.yPosition-=n.lineHeight),n.yPosition=s(n,a.content,{font:n.fonts.bold,size:c,color:(0,o.rgb)(0,0,0)}),n.yPosition-=.5*n.lineHeight,n.yPosition;case"paragraph":default:return r=e,i=t,r.yPosition-=.3*r.lineHeight,r.yPosition=s(r,i.content,{font:r.fonts.regular,size:12,color:(0,o.rgb)(0,0,0)}),r.yPosition-=.7*r.lineHeight,r.yPosition;case"list-item":return function(e,t){let r=20*(t.level||1),i="• ";e.page.drawText(i,{x:e.margin+r,y:e.yPosition,size:12,font:e.fonts.regular,color:(0,o.rgb)(0,0,0)});let n=e.fonts.regular.widthOfTextAtSize(i,12),a=e.margin+r+n,s=e.maxWidth-r-n;return e.yPosition=l(e,t.content,{font:e.fonts.regular,size:12,color:(0,o.rgb)(0,0,0),x:a,maxWidth:s}),e.yPosition-=.3*e.lineHeight,e.yPosition}(e,t)}}(g,i);p=g.page;let m=await n.save();console.log("✅ PDF generated successfully");let f=`manuscript-${Date.now()}-${Math.random().toString(36).substring(7)}.pdf`;console.log(`📤 Uploading PDF to Vercel Blob: ${f}`);let h=await (0,i.yJ)(`manuscripts/${f}`,Buffer.from(m),{access:"public",contentType:"application/pdf"});return console.log(`✅ PDF uploaded successfully: ${h.url}`),h.url}catch(e){return console.error("❌ PDF generation/upload failed:",e),null}}function a(e){return e.replace(/\*\*BOLD_START\*\*/g,"").replace(/\*\*BOLD_END\*\*/g,"").replace(/\*\*ITALIC_START\*\*/g,"").replace(/\*\*ITALIC_END\*\*/g,"")}function s(e,t,r){let o=r.x||e.margin,i=r.maxWidth||e.maxWidth;return l(e,t,{...r,x:o,maxWidth:i})}function l(e,t,r){let o=t.trim().split(/\s+/),i="",n=e.yPosition;for(let t of o){let o=i+t+" ",a=r.font.widthOfTextAtSize(o,r.size);if(r.font.widthOfTextAtSize(t,r.size)>r.maxWidth){i.trim()&&(e.page.drawText(i.trim(),{x:r.x,y:n,size:r.size,font:r.font,color:r.color}),n-=e.lineHeight,i="",n<e.margin&&(e.page=e.pdfDoc.addPage(),n=e.pageHeight-e.margin));let o=t;for(;o.length>0;){let t="";for(let e=0;e<o.length;e++){let i=t+o[e];if(r.font.widthOfTextAtSize(i,r.size)>r.maxWidth)break;t=i}0===t.length&&(t=o[0]),e.page.drawText(t,{x:r.x,y:n,size:r.size,font:r.font,color:r.color}),n-=e.lineHeight,o=o.substring(t.length),n<e.margin&&o.length>0&&(e.page=e.pdfDoc.addPage(),n=e.pageHeight-e.margin)}}else a>r.maxWidth&&""!==i?(e.page.drawText(i.trim(),{x:r.x,y:n,size:r.size,font:r.font,color:r.color}),n-=e.lineHeight,i=t+" ",n<e.margin&&(e.page=e.pdfDoc.addPage(),n=e.pageHeight-e.margin)):i=o}return i.trim()&&(e.page.drawText(i.trim(),{x:r.x,y:n,size:r.size,font:r.font,color:r.color}),(n-=e.lineHeight)<e.margin&&(e.page=e.pdfDoc.addPage(),n=e.pageHeight-e.margin)),n}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54287:e=>{e.exports=require("console")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},59949:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>h});var o={};r.r(o),r.d(o,{POST:()=>g});var i=r(96559),n=r(48088),a=r(37719),s=r(56814),l=r(5956),p=r(20549),c=r(32190),u=r(43470);let d=new p.u(process.env.RESEND_API_KEY);async function g(e){let t=await (0,s.j2)();if(!t?.user?.id)return console.log("Unauthorized"),c.NextResponse.json({error:"Unauthorized"},{status:401});try{let r,{title:o,abstract:i,content:n,keywords:a,uploadedFile:s,uploadedFileName:p,isDraft:g=!1}=await e.json();console.log(`📄 Starting manuscript ${g?"draft save":"submission"} process...`),console.log(`📝 Title: ${o}`),console.log(`👤 Author: ${t.user?.name}`),console.log(`📋 Is Draft: ${g}`);let m=null;if(!g&&n&&!(m=await (0,u.Q)(n,o,t.user?.name||"Unknown Author")))return console.log("❌ Failed to generate PDF"),c.NextResponse.json({error:"Failed to generate PDF"},{status:500});try{r=await l.z.manuscript.create({data:{author_id:t.user.id,title:o,abstract:i,content:n,keywords:a,pdfFile:m||"",uploadedFile:s||null,uploadedFileName:p||null,status:g?"DRAFT":"SUBMITTED"}})}catch(e){return console.error("Prisma manuscript.create failed:",e,{author_id:t.user.id,title:o,abstract:i,content:n,keywords:a,pdfFile:m,uploadedFile:s||null,uploadedFileName:p||null}),c.NextResponse.json({error:"Failed to create manuscript",details:e instanceof Error?e.message:String(e)},{status:500})}if(g)console.log("\uD83D\uDCCB Draft saved successfully. No notifications sent.");else{let e=await l.z.user.findMany({where:{role:"REVIEWER"}});console.log(`📧 Starting email notifications for ${e.length} reviewers`),console.log(`🔑 Resend API Key configured: ${process.env.RESEND_API_KEY?"Yes":"No"}`),Array.isArray(e)&&0!==e.length?await Promise.all(e.map(async(o,i)=>{try{console.log(`📝 Processing reviewer ${i+1}/${e.length}: ${o.email}`);let n=await l.z.review.create({data:{manuscript_id:r.id,reviewer_id:o.id,content:`Review assignment for manuscript: ${r.title}`,status:"PENDING"}});console.log(`✅ Review assignment created for ${o.email}:`,n.id);let a=await l.z.notification.create({data:{id:`notif_${Date.now()}_${Math.random().toString(36).substring(7)}`,userId:o.id,title:"New Manuscript Submitted",message:`New manuscript "${r.title}" from ${t.user?.name} requires your review`,type:"MANUSCRIPT_SUBMISSION",relatedId:r.id}});console.log(`✅ Dashboard notification created for ${o.email}:`,a.id);let s=await d.emails.send({from:"Tina Education <<EMAIL>>",to:o.email,subject:`New Manuscript Review Request: "${r.title}"`,html:`
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h2 style="color: #1e40af; margin: 0;">📝 New Manuscript Review Request</h2>
                  </div>

                  <p>Hello <strong>${o.name||"Reviewer"}</strong>,</p>

                  <p>A new manuscript has been submitted and requires your expert review:</p>

                  <div style="background-color: #f1f5f9; padding: 15px; border-radius: 6px; margin: 20px 0;">
                    <h3 style="margin: 0 0 10px 0; color: #334155;">📄 Manuscript Details</h3>
                    <p style="margin: 5px 0;"><strong>Title:</strong> ${r.title}</p>
                    <p style="margin: 5px 0;"><strong>Author:</strong> ${t.user?.name}</p>
                    <p style="margin: 5px 0;"><strong>Abstract:</strong> ${r.abstract}</p>
                    <p style="margin: 5px 0;"><strong>Keywords:</strong> ${r.keywords}</p>
                  </div>

                  <p>Please log in to the Tina Education system to access the full manuscript and begin your review.</p>

                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${process.env.NEXTAUTH_URL||"http://localhost:3000"}/dashboard"
                       style="background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                      Review Manuscript
                    </a>
                  </div>

                  <p>Thank you for your contribution to the academic review process.</p>

                  <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
                  <p style="color: #64748b; font-size: 14px;">
                    Best regards,<br>
                    Tina Education<br>
                    <small>This is an automated notification from the development environment.</small>
                  </p>
                </div>
              `});console.log(`✅ Email sent successfully to ${o.email}:`,s)}catch(e){console.error(`❌ Notification/email failed for reviewer ${o.id} (${o.email}):`,e),e instanceof Error&&(console.error(`Error message: ${e.message}`),console.error(`Error stack: ${e.stack}`))}})):console.log("⚠️ No reviewers found. Skipping notifications.")}return c.NextResponse.json({success:!0,manuscript:r})}catch(e){return console.error("Unexpected error in manuscript creation:",e),c.NextResponse.json({error:"Internal Server Error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/manuscripts/route",pathname:"/api/manuscripts",filename:"route",bundlePath:"app/api/manuscripts/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\manuscripts\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:f,workUnitAsyncStorage:h,serverHooks:x}=m;function y(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:h})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{e.exports=require("http2")},73566:e=>{e.exports=require("worker_threads")},74075:e=>{e.exports=require("zlib")},74998:e=>{e.exports=require("perf_hooks")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")},91645:e=>{e.exports=require("net")},94175:e=>{e.exports=require("stream/web")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,2190,5663,9404,3285,8249,9468],()=>r(59949));module.exports=o})();