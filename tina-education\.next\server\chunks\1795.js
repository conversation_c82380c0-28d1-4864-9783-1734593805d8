exports.id=1795,exports.ids=[1795],exports.modules={44919:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(60687),a=s(99208);function l({children:e}){return(0,r.jsx)(a.<PERSON>,{children:e})}},45587:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationToast.tsx","default")},55529:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f,metadata:()=>u});var r=s(37413),a=s(260),l=s.n(a),i=s(73298),n=s.n(i),o=s(62481),c=s(89638),d=s(4536),m=s.n(d);function x(){return(0,r.jsx)("footer",{className:"bg-gray-900 text-white py-8",children:(0,r.jsxs)("div",{className:"container mx-auto w-[90%] max-w-7xl",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold mb-4",children:"Quick Links"}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"/",className:"text-gray-300 hover:text-white",children:"Home"})}),(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"#",className:"text-gray-300 hover:text-white",children:"About"})}),(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"/books",className:"text-gray-300 hover:text-white",children:"Books"})}),(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"/journals",className:"text-gray-300 hover:text-white",children:"Journals"})}),(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"/articles",className:"text-gray-300 hover:text-white",children:"Articles"})}),(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"#",className:"text-gray-300 hover:text-white",children:"Privacy Policy"})})]})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold mb-4",children:"Services"}),(0,r.jsxs)("ul",{className:"space-y-1",children:[(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"#",className:"text-gray-300 hover:text-white",children:"Publishing"})}),(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"#",className:"text-gray-300 hover:text-white",children:"Editing"})}),(0,r.jsx)("li",{children:(0,r.jsx)(m(),{href:"#",className:"text-gray-300 hover:text-white",children:"Formatting"})})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-bold mb-4",children:"Newsletter Sign Up"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"email",placeholder:"Enter your Email",className:"bg-gray-600 px-4 py-2 w-full rounded"}),(0,r.jsx)("button",{className:"bg-white text-gray-900 ms-2 px-4 py-2 rounded",children:"Submit"})]})]})]}),(0,r.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-700 text-center",children:(0,r.jsx)("p",{className:"text-gray-400",children:"Copyright \xa9 2025 - Tina Education"})})]})})}var h=s(45587);s(82704);let u={title:"Tina Education",description:"Generated by create next app"};function f({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${l().variable} ${n().variable} antialiased min-h-screen flex flex-col`,children:(0,r.jsxs)(o.default,{children:[(0,r.jsx)(c.default,{}),(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)(x,{}),(0,r.jsx)(h.default,{})]})})})}},59285:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),a=s(43210),l=s(99208);function i(){let{data:e,status:t}=(0,l.wV)(),[s,i]=(0,a.useState)([]),n=(0,a.useCallback)(e=>{i(t=>t.filter(t=>t.id!==e))},[]);(0,a.useCallback)(e=>{let t=`toast_${Date.now()}_${Math.random().toString(36).substring(7)}`,s={...e,id:t};i(e=>[...e,s]),setTimeout(()=>{n(t)},5e3)},[n]);let o=e=>{let t="p-4 rounded-lg shadow-lg border-l-4 max-w-sm";switch(e){case"success":return`${t} bg-green-50 border-green-400 text-green-800`;case"error":return`${t} bg-red-50 border-red-400 text-red-800`;case"warning":return`${t} bg-yellow-50 border-yellow-400 text-yellow-800`;default:return`${t} bg-blue-50 border-blue-400 text-blue-800`}},c=e=>{switch(e){case"success":return"✅";case"error":return"❌";case"warning":return"⚠️";default:return"ℹ️"}};return 0===s.length?null:(0,r.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.map(e=>(0,r.jsx)("div",{className:`${o(e.type)} animate-slide-in-right`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-lg mr-3",children:c(e.type)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-medium",children:e.title}),(0,r.jsx)("p",{className:"text-sm mt-1",children:e.message})]}),(0,r.jsx)("button",{onClick:()=>n(e.id),className:"ml-3 text-gray-400 hover:text-gray-600",children:"\xd7"})]})},e.id))})}},62481:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\context\\\\AuthProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\context\\AuthProvider.tsx","default")},63612:(e,t,s)=>{Promise.resolve().then(s.bind(s,89638)),Promise.resolve().then(s.bind(s,45587)),Promise.resolve().then(s.bind(s,62481)),Promise.resolve().then(s.t.bind(s,4536,23))},71139:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},73340:(e,t,s)=>{Promise.resolve().then(s.bind(s,80360)),Promise.resolve().then(s.bind(s,59285)),Promise.resolve().then(s.bind(s,44919)),Promise.resolve().then(s.t.bind(s,85814,23))},80360:(e,t,s)=>{"use strict";s.d(t,{default:()=>u});var r=s(60687),a=s(85814),l=s.n(a),i=s(99208),n=s(16189),o=s(69587),c=s(43210);function d(){let{data:e,status:t}=(0,i.wV)(),[s,a]=(0,c.useState)(0);return"loading"===t?(0,r.jsx)("div",{className:"w-6 h-6 animate-pulse bg-gray-200 rounded"}):"unauthenticated"!==t&&e?.user?.id?(0,r.jsxs)(l(),{href:"/dashboard/notifications",className:"relative",children:[(0,r.jsx)(o.jNV,{size:24,className:"text-gray-600 hover:text-blue-600 transition-colors"}),s>0&&(0,r.jsx)("span",{className:"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse",children:s>99?"99+":s})]}):null}var m=s(30474);function x({user:e}){let[t,s]=(0,c.useState)(!1),a=(0,c.useRef)(null),n=e=>e?e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2):"U";return(0,r.jsxs)("div",{className:"relative",ref:a,children:[(0,r.jsxs)("button",{onClick:()=>s(!t),className:"flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2","aria-label":"User menu",children:[(0,r.jsx)("div",{className:"w-9 h-9 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center overflow-hidden border-2 border-white shadow-sm",children:e.image?(0,r.jsx)(m.default,{src:e.image,alt:e.name||"User",width:36,height:36,className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:n(e.name)})}),(0,r.jsx)(o.Vr3,{className:`w-3 h-3 text-gray-500 transition-transform duration-200 hidden sm:block ${t?"rotate-180":""}`})]}),t&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 sm:w-72 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 max-w-[calc(100vw-2rem)] sm:max-w-none animate-in fade-in-0 zoom-in-95 duration-100",children:[(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden",children:e.image?(0,r.jsx)(m.default,{src:e.image,alt:e.name||"User",width:40,height:40,className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-lg font-medium text-gray-600",children:n(e.name)})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.name||"User"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.email}),e.role&&(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1",children:e.role.toLowerCase()})]})]})}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)(l(),{href:"/dashboard",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>s(!1),children:[(0,r.jsx)(o.$BV,{className:"w-4 h-4 mr-3 text-gray-400"}),"Dashboard"]}),(0,r.jsxs)(l(),{href:"/profile",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>s(!1),children:[(0,r.jsx)(o.x$1,{className:"w-4 h-4 mr-3 text-gray-400"}),"Profile"]}),"REVIEWER"===e.role&&(0,r.jsxs)(l(),{href:"/dashboard/reviews",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>s(!1),children:[(0,r.jsx)(o.hko,{className:"w-4 h-4 mr-3 text-gray-400"}),"My Reviews"]}),"ADMIN"===e.role&&(0,r.jsxs)(l(),{href:"/dashboard/admin",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>s(!1),children:[(0,r.jsx)(o.e7y,{className:"w-4 h-4 mr-3 text-gray-400"}),"Admin Panel"]}),(0,r.jsxs)(l(),{href:"/dashboard/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>s(!1),children:[(0,r.jsx)(o.Pcn,{className:"w-4 h-4 mr-3 text-gray-400"}),"Settings"]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 my-1"}),(0,r.jsxs)("button",{onClick:()=>{(0,i.CI)({callbackUrl:"/"})},className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",children:[(0,r.jsx)(o.axc,{className:"w-4 h-4 mr-3"}),"Sign Out"]})]})]})}function h(){let{data:e}=(0,i.wV)(),t=(0,n.usePathname)(),s=(0,n.useSearchParams)(),a=()=>{let e=s.toString();return e?`${t}?${e}`:t};return(0,r.jsxs)("div",{className:"flex justify-center items-center gap-5 relative text-gray-400",children:[!e&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("button",{className:"px-5 py-2 border border-black rounded text-gray-800 hover:bg-black hover:text-white hover:bg-opacity-10 transition-colors",onClick:()=>(0,i.Jv)(void 0,{callbackUrl:a()}),children:"Sign In"})}),e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d,{}),(0,r.jsx)(x,{user:e.user})]})]})}function u(){let[e,t]=(0,c.useState)(!1),{data:s}=(0,i.wV)(),a=(0,n.usePathname)(),d=s&&"/"===a;return(0,r.jsx)("header",{className:"fixed top-0 w-full bg-white shadow-md z-50",children:(0,r.jsx)("div",{className:"container mx-auto w-[90%] max-w-7xl",children:(0,r.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,r.jsx)(l(),{href:"/",className:"text-2xl font-bold text-gray-800",children:"Tina Education"}),(0,r.jsx)("button",{className:"text-gray-800 text-2xl md:hidden",onClick:()=>{t(!e)},"aria-label":"Toggle Menu",children:e?(0,r.jsx)(o.QCr,{}):(0,r.jsx)(o.OXb,{})}),(0,r.jsx)("nav",{className:`${e?"block":"hidden"} md:block absolute md:static top-full left-0 w-full md:w-auto bg-white md:bg-transparent shadow-md md:shadow-none`,children:(0,r.jsxs)("ul",{className:"flex flex-col md:flex-row items-center",children:[(0,r.jsx)("li",{className:"ml-0 md:ml-8",children:(0,r.jsx)(l(),{href:d?"/dashboard":"/",className:"text-gray-800 font-medium",children:d?"Dashboard":"Home"})}),(0,r.jsx)("li",{className:"ml-0 md:ml-8",children:(0,r.jsx)(l(),{href:"/books",className:"text-gray-800 font-medium",children:"Books"})}),(0,r.jsx)("li",{className:"ml-0 md:ml-8",children:(0,r.jsx)(l(),{href:"/journals",className:"text-gray-800 font-medium",children:"Journals"})}),s&&(0,r.jsx)("li",{className:"ml-0 md:ml-8",children:(0,r.jsx)(l(),{href:"/wishlist",className:"text-gray-800 font-medium",children:"Wishlist"})}),s&&(0,r.jsx)("li",{className:"ml-0 md:ml-8",children:(0,r.jsx)(l(),{href:"/cart",className:"text-gray-800 font-medium",children:"Cart"})}),(0,r.jsx)("li",{className:"ml-0 md:ml-8",children:(0,r.jsx)(l(),{href:"#",className:"text-gray-800 font-medium",children:"Publisher with Us"})}),(0,r.jsx)("li",{className:"ml-0 md:ml-8",children:(0,r.jsx)("div",{className:"flex justify-center items-center gap-5 mb-8 md:mb-0 relative text-gray-500",children:(0,r.jsx)(c.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(h,{})})})})]})})]})})})}},82704:()=>{},85211:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},89638:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home_nav.tsx","default")}};