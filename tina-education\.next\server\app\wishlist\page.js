(()=>{var e={};e.id=1720,e.ids=[1720],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6467:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\wishlist\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\wishlist\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15865:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c={children:["",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6467)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\wishlist\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\wishlist\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/wishlist/page",pathname:"/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},17731:(e,t,s)=>{Promise.resolve().then(s.bind(s,6467))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31803:(e,t,s)=>{Promise.resolve().then(s.bind(s,54161))},33873:e=>{"use strict";e.exports=require("path")},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},54161:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(60687),a=s(85814),i=s.n(a),l=s(30474),n=s(43210),o=s(99208),c=s(19089),d=s(69587);function x(){let{data:e,status:t}=(0,o.wV)(),[s,a]=(0,n.useState)([]),[x,h]=(0,n.useState)(!0),u=async(e,t)=>{try{(await fetch(`/api/wishlist?publicationId=${e}&selectedType=${t}`,{method:"DELETE"})).ok&&a(s=>s.filter(s=>s.publication.id!==e||s.selectedType!==t))}catch(e){console.error("Failed to remove from wishlist:",e)}},p=e=>{switch(e){case"BOOK":return"Printed Copy";case"EBOOK":return"eBook";case"AUDIOBOOK":return"Audio book";case"JOURNAL":return"Journal";case"ARTICLE":return"Article";default:return e}};return x||"loading"===t?(0,r.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})}):e?.user?.id?(0,r.jsxs)("div",{className:"min-h-screen bg-white mt-18",children:[(0,r.jsx)("div",{className:"w-full h-18 flex items-center justify-center",style:{backgroundColor:"#f0eded"},children:(0,r.jsxs)("nav",{className:"flex space-x-8",children:[(0,r.jsx)(i(),{href:"/books",className:"text-black text-xl hover:underline",children:"Books"}),(0,r.jsx)(i(),{href:"/authors",className:"text-black text-xl hover:underline",children:"Authors"}),(0,r.jsx)(i(),{href:"/journals",className:"text-black text-xl hover:underline",children:"Journals"})]})}),(0,r.jsx)("div",{className:"px-10 py-4 border-b border-blue-500",children:(0,r.jsxs)("nav",{className:"text-s text-black",children:[(0,r.jsx)(i(),{href:"/",className:"hover:underline",children:"Home"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)("span",{children:"Wishlist"})]})}),(0,r.jsxs)("div",{className:"px-10 py-10",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-8",children:[(0,r.jsx)(d.Mbv,{className:"text-red-500 text-3xl"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-black",children:"My Wishlist"}),(0,r.jsxs)("span",{className:"text-gray-600 text-xl",children:["(",s.length," items)"]})]}),0===s.length?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC9D"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-black mb-4",children:"Your wishlist is empty"}),(0,r.jsx)("p",{className:"text-gray-700 mb-8",children:"Save books you're interested in for later!"}),(0,r.jsx)(i(),{href:"/books",className:"px-8 py-3 text-white rounded-lg hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:"Browse Books"})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:s.map(e=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,r.jsx)("div",{className:"w-full aspect-[3/4] mb-4",children:e.publication.cover?(0,r.jsx)(l.default,{src:e.publication.cover,alt:e.publication.title,width:200,height:267,className:"w-full h-full object-cover border border-gray-300 rounded"}):(0,r.jsx)("div",{className:"w-full h-full bg-gray-200 border border-gray-300 rounded flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500 text-sm",children:"No Cover"})})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-black line-clamp-2",children:(0,r.jsx)(i(),{href:`/repository/${e.publication.id}`,className:"hover:underline",children:e.publication.title})}),(0,r.jsxs)("p",{className:"text-gray-700 text-sm",children:["By ",e.publication.user.name]}),e.publication.genre&&(0,r.jsx)("p",{className:"text-gray-600 text-xs",children:e.publication.genre.name}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded",children:p(e.selectedType)})}),e.publication.abstract&&(0,r.jsx)("p",{className:"text-gray-600 text-sm line-clamp-3",children:e.publication.abstract})]}),(0,r.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,r.jsxs)(i(),{href:`/repository/${e.publication.id}`,className:"flex-1 px-3 py-2 text-white text-sm text-center rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:[(0,r.jsx)(c.Wxq,{className:"inline mr-1"}),"View Details"]}),(0,r.jsx)("button",{onClick:()=>u(e.publication.id,e.selectedType),className:"px-3 py-2 text-red-500 border border-red-300 rounded hover:bg-red-50 transition-colors",title:"Remove from wishlist",children:(0,r.jsx)(c.yRo,{})})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Added ",new Date(e.createdAt).toLocaleDateString()]})]},e.id))}),s.length>0&&(0,r.jsx)("div",{className:"mt-12 text-center",children:(0,r.jsx)(i(),{href:"/books",className:"text-blue-600 hover:underline text-lg",children:"← Continue Shopping"})})]})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-white mt-18",children:[(0,r.jsx)("div",{className:"w-full h-18 flex items-center justify-center",style:{backgroundColor:"#f0eded"},children:(0,r.jsxs)("nav",{className:"flex space-x-8",children:[(0,r.jsx)(i(),{href:"/books",className:"text-black text-xl hover:underline",children:"Books"}),(0,r.jsx)(i(),{href:"/authors",className:"text-black text-xl hover:underline",children:"Authors"}),(0,r.jsx)(i(),{href:"/journals",className:"text-black text-xl hover:underline",children:"Journals"})]})}),(0,r.jsx)("div",{className:"px-10 py-4 border-b border-blue-500",children:(0,r.jsxs)("nav",{className:"text-s text-black",children:[(0,r.jsx)(i(),{href:"/",className:"hover:underline",children:"Home"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)("span",{children:"Wishlist"})]})}),(0,r.jsx)("div",{className:"px-10 py-10",children:(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD12"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-black mb-4",children:"Sign in to view your wishlist"}),(0,r.jsx)("p",{className:"text-gray-700 mb-8",children:"You need to be signed in to access your wishlist."}),(0,r.jsx)(i(),{href:"/auth/signin?callbackUrl=/wishlist",className:"px-8 py-3 text-white rounded-lg hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:"Sign In"})]})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7945,1658,4755,1795],()=>s(15865));module.exports=r})();