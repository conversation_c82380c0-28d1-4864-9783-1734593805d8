(()=>{var e={};e.id=7136,e.ids=[7136],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},40612:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),c=r(5956);async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("search")||"",s=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"12"),a=t.get("sortBy")||"createdAt",n=t.get("sortOrder")||"desc",p={type:"ARTICLE"};r&&(p.OR=[{title:{contains:r,mode:"insensitive"}},{abstract:{contains:r,mode:"insensitive"}},{keywords:{contains:r,mode:"insensitive"}},{user:{name:{contains:r,mode:"insensitive"}}}]);let u=(s-1)*i,[d,l]=await Promise.all([c.z.publication.findMany({where:p,include:{user:{select:{name:!0,email:!0}}},orderBy:{[a]:n},skip:u,take:i}),c.z.publication.count({where:p})]),g=Math.ceil(l/i);return o.NextResponse.json({articles:d.map(e=>({...e,createdAt:e.createdAt.toISOString(),updatedAt:e.updatedAt.toISOString()})),pagination:{currentPage:s,totalPages:g,totalCount:l,hasNextPage:s<g,hasPrevPage:s>1,limit:i}})}catch(e){return console.error("Failed to fetch articles:",e),o.NextResponse.json({error:"Failed to fetch articles"},{status:500})}}let u=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/repository/articles/route",pathname:"/api/repository/articles",filename:"route",bundlePath:"app/api/repository/articles/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\articles\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=u;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190],()=>r(40612));module.exports=s})();