(()=>{var e={};e.id=2273,e.ids=[2273],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38071:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>u});var i=r(96559),o=r(48088),n=r(37719),a=r(32190),p=r(5956);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("search")||"",s=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"12"),o=t.get("sortBy")||"createdAt",n=t.get("sortOrder")||"desc",u=t.get("genre"),c={type:{in:["BOOK","EBOOK","AUDIOBOOK"]}};u&&(c.genre={OR:[{slug:u},{parent:{slug:u}}]}),r&&(c.OR=[{title:{contains:r,mode:"insensitive"}},{abstract:{contains:r,mode:"insensitive"}},{keywords:{contains:r,mode:"insensitive"}},{user:{name:{contains:r,mode:"insensitive"}}}]);let d=(s-1)*i,[l,g]=await Promise.all([p.z.publication.findMany({where:c,include:{user:{select:{name:!0,email:!0}},genre:{include:{parent:!0}}},orderBy:{[o]:n},skip:d,take:i}),p.z.publication.count({where:c})]),x=Math.ceil(g/i);return a.NextResponse.json({books:l.map(e=>({...e,createdAt:e.createdAt.toISOString(),updatedAt:e.updatedAt.toISOString()})),pagination:{currentPage:s,totalPages:x,totalCount:g,hasNextPage:s<x,hasPrevPage:s>1,limit:i}})}catch(e){return console.error("Failed to fetch books:",e),a.NextResponse.json({error:"Failed to fetch books"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/repository/books/route",pathname:"/api/repository/books",filename:"route",bundlePath:"app/api/repository/books/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\books\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=c;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190],()=>r(38071));module.exports=s})();