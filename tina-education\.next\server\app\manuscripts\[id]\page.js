(()=>{var e={};e.id=3962,e.ids=[3962],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40121:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ManuscriptViewer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ManuscriptViewer.tsx","default")},47806:(e,t,r)=>{Promise.resolve().then(r.bind(r,69199)),Promise.resolve().then(r.t.bind(r,85814,23))},52534:(e,t,r)=>{Promise.resolve().then(r.bind(r,40121)),Promise.resolve().then(r.t.bind(r,4536,23))},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=r(65239),i=r(48088),n=r(88170),a=r.n(n),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c={children:["",{children:["manuscripts",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76513)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/manuscripts/[id]/page",pathname:"/manuscripts/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76513:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(37413),i=r(56814),n=r(5956),a=r(39916),o=r(40121),d=r(4536),c=r.n(d);async function l(e,t){try{let r=await n.z.manuscript.findUnique({where:{id:e},include:{user:{select:{name:!0,email:!0}},reviews:{include:{user:{select:{name:!0,email:!0}}}}}});if(!r)return null;let s=await (0,i.j2)(),a=r.author_id===t,o=r.reviews.some(e=>e.reviewer_id===t),d=s?.user?.role==="ADMIN";if(!a&&!o&&!d)return null;return r}catch(e){return console.error("Error fetching manuscript:",e),null}}async function u({params:e}){let t=await (0,i.j2)();t?.user?.id||(0,a.redirect)("/auth/signin");let{id:r}=await e,n=await l(r,t.user.id);return n||(0,a.notFound)(),(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(c(),{href:"/dashboard/manuscripts",className:"text-gray-500 hover:text-gray-700 flex items-center space-x-2",children:[(0,s.jsx)("span",{children:"←"}),(0,s.jsx)("span",{children:"Back to Manuscripts"})]}),(0,s.jsx)("div",{className:"h-6 border-l border-gray-300"}),(0,s.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:"Manuscript Details"})]}),(0,s.jsx)("div",{className:"flex items-center space-x-3",children:n.author_id===t.user.id&&(0,s.jsx)(c(),{href:`/manuscripts/${n.id}/edit`,className:"inline-flex items-center px-4 py-2 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors font-medium text-sm",children:"Edit Manuscript"})})]})})}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)(o.default,{manuscript:n})})]})}},79551:e=>{"use strict";e.exports=require("url")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190,5663,9404,7945,1658,1795,7972],()=>r(67745));module.exports=s})();