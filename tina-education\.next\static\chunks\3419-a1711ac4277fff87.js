(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3419],{98:(e,t,r)=>{Promise.resolve().then(r.bind(r,5230))},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return i},getImageProps:function(){return c}});let s=r(8229),a=r(8883),l=r(3063),n=s._(r(1193));function c(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let i=l.Image},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=s.createContext&&s.createContext(a),n=["attr","size","title"];function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var s,a,l;s=e,a=t,l=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):s[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(u,c({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,o({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:a,size:l,title:i}=e,d=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(s=0;s<l.length;s++)r=l[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,n),u=l||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",c({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:r,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),i&&s.createElement("title",null,i),e.children)};return void 0!==l?s.createElement(l.Consumer,null,e=>t(e)):t(a)}},5230:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(5155),a=r(2115),l=r(6874),n=r.n(l),c=r(6766);function i(e){let{publication:t}=e,r=function(e){switch(e){case"BOOK":return"from-blue-500 to-blue-700";case"EBOOK":case"JOURNAL":return"from-blue-600 to-blue-800";case"AUDIOBOOK":case"ARTICLE":return"from-gray-600 to-gray-800";default:return"from-gray-500 to-gray-700"}}(t.type);return(0,s.jsx)(n(),{href:"/repository/".concat(t.id),className:"group block",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 group-hover:-translate-y-1",children:[(0,s.jsx)("div",{className:"aspect-[3/4] bg-gradient-to-br ".concat(r," relative"),children:t.cover?(0,s.jsx)(c.default,{src:t.cover,alt:t.title,fill:!0,className:"object-cover"}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("span",{className:"text-6xl text-white opacity-80",children:function(e){switch(e){case"BOOK":return"\uD83D\uDCDA";case"EBOOK":return"\uD83D\uDCBB";case"AUDIOBOOK":return"\uD83C\uDFA7";case"JOURNAL":return"\uD83D\uDCF0";default:return"\uD83D\uDCC4"}}(t.type)})})}),(0,s.jsxs)("div",{className:"p-4 space-y-2",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 text-sm leading-tight line-clamp-2 group-hover:text-blue-900 transition-colors",children:t.title}),(0,s.jsx)("p",{className:"text-blue-600 text-sm font-medium",children:t.user.name||"Unknown Author"}),t.genre&&(0,s.jsxs)("p",{className:"text-gray-500 text-xs",children:[t.genre.parent?"".concat(t.genre.parent.name," > "):"",t.genre.name]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("span",{className:"text-lg font-bold text-gray-900",children:"Free"})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(function(e){switch(e){case"BOOK":case"EBOOK":case"JOURNAL":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}}(t.type)),children:"EBOOK"===t.type?"Digital":t.type.charAt(0)+t.type.slice(1).toLowerCase()}),t.content&&(0,s.jsx)("span",{className:"text-blue-600 text-xs",children:"+1 other format"})]})]})]})})}var o=r(9911);function d(e){let{filters:t,onFilterChange:r,loading:l}=e,[n,c]=(0,a.useState)(!1),[i,d]=(0,a.useState)(t.search),u=(e,t)=>{r({sortBy:e,sortOrder:t})},x=()=>{d(""),r({search:"",sortBy:"createdAt",sortOrder:"desc",genre:""})};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),r({search:i})},className:"mb-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(o.KSO,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",value:i,onChange:e=>d(e.target.value),placeholder:"Search publications, authors, keywords...",className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-gray-700 placeholder-gray-500",disabled:l}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center",children:(0,s.jsx)("button",{type:"submit",disabled:l,className:"mr-3 px-4 py-2 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors disabled:opacity-50",children:"Search"})})]})}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{onClick:()=>c(!n),className:"flex items-center space-x-2 px-3 py-2 border border-gray-300 text-gray-500 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,s.jsx)(o.YsJ,{className:"h-4 w-4 "}),(0,s.jsx)("span",{children:"Filters"})]}),(t.search||"createdAt"!==t.sortBy||"desc"!==t.sortOrder)&&(0,s.jsx)("button",{onClick:x,className:"px-3 py-2 text-sm text-gray-400 hover:text-gray-800 transition-colors",children:"Clear all filters"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-400",children:"Sort by:"}),(0,s.jsxs)("select",{value:"".concat(t.sortBy,"-").concat(t.sortOrder),onChange:e=>{let[t,r]=e.target.value.split("-");u(t,r)},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-gray-700",disabled:l,children:[(0,s.jsx)("option",{value:"createdAt-desc",children:"Newest First"}),(0,s.jsx)("option",{value:"createdAt-asc",children:"Oldest First"}),(0,s.jsx)("option",{value:"title-asc",children:"Title A-Z"}),(0,s.jsx)("option",{value:"title-desc",children:"Title Z-A"})]})]})]}),n&&(0,s.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort Field"}),(0,s.jsxs)("select",{value:t.sortBy,onChange:e=>u(e.target.value,t.sortOrder),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-gray-700",disabled:l,children:[(0,s.jsx)("option",{value:"createdAt",children:"Date Created"}),(0,s.jsx)("option",{value:"updatedAt",children:"Date Updated"}),(0,s.jsx)("option",{value:"title",children:"Title"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort Order"}),(0,s.jsxs)("select",{value:t.sortOrder,onChange:e=>u(t.sortBy,e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-gray-700",disabled:l,children:[(0,s.jsx)("option",{value:"desc",children:"Descending"}),(0,s.jsx)("option",{value:"asc",children:"Ascending"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:x,className:"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",disabled:l,children:"Reset Filters"})})]})}),(t.search||"createdAt"!==t.sortBy||"desc"!==t.sortOrder)&&(0,s.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-400",children:"Active filters:"}),t.search&&(0,s.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800",children:['Search: "',t.search,'"',(0,s.jsx)("button",{onClick:()=>{d(""),r({search:""})},className:"ml-2 text-blue-600 hover:text-blue-800",children:"\xd7"})]}),("createdAt"!==t.sortBy||"desc"!==t.sortOrder)&&(0,s.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800",children:["Sort: ",t.sortBy," (",t.sortOrder,")",(0,s.jsx)("button",{onClick:()=>u("createdAt","desc"),className:"ml-2 text-gray-400 hover:text-gray-800",children:"\xd7"})]})]})})]})}function u(e){let{apiEndpoint:t,title:r,description:l,emptyStateMessage:n,emptyStateIcon:c}=e,[o,u]=(0,a.useState)([]),[x,m]=(0,a.useState)(null),[g,b]=(0,a.useState)(!0),[h,p]=(0,a.useState)(null),[y,f]=(0,a.useState)({search:"",sortBy:"createdAt",sortOrder:"desc",page:1,genre:""}),j=(0,a.useCallback)(async()=>{try{b(!0);let e=new URLSearchParams({search:y.search,sortBy:y.sortBy,sortOrder:y.sortOrder,page:y.page.toString(),limit:"12"});y.genre&&e.append("genre",y.genre);let r=await fetch("".concat(t,"?").concat(e));if(!r.ok)throw Error("Failed to fetch publications");let s=await r.json(),a=t.includes("books")?"books":t.includes("journals")?"journals":t.includes("articles")?"articles":"publications";u(s[a]||[]),m(s.pagination),p(null)}catch(e){p(e instanceof Error?e.message:"An error occurred"),u([])}finally{b(!1)}},[t,y]);(0,a.useEffect)(()=>{j()},[y,j]);let v=e=>{f(t=>({...t,page:e})),window.scrollTo({top:0,behavior:"smooth"})};return g&&0===o.length?(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 pt-20",children:(0,s.jsx)("div",{className:"container mx-auto w-[90%] max-w-7xl py-8",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading publications..."})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto w-[90%] max-w-7xl py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:r}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:l}),x&&(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:["Showing ",x.totalCount," ",1===x.totalCount?"publication":"publications"]})]}),(0,s.jsx)(d,{filters:y,onFilterChange:e=>{f(t=>({...t,...e,page:1}))},loading:g}),h&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"❌"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Publications"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:h}),(0,s.jsx)("button",{onClick:j,className:"px-6 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors",children:"Try Again"})]}),!g&&!h&&0===o.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:c}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Publications Found"}),(0,s.jsx)("p",{className:"text-gray-400",children:n})]}),!h&&o.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8",children:o.map(e=>(0,s.jsx)(i,{publication:e},e.id))}),x&&x.totalPages>1&&(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>v(x.currentPage-1),disabled:!x.hasPrevPage,className:"px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Previous"}),Array.from({length:x.totalPages},(e,t)=>t+1).map(e=>(0,s.jsx)("button",{onClick:()=>v(e),className:"px-4 py-2 border rounded-lg ".concat(e===x.currentPage?"bg-blue-900 text-white border-blue-900":"border-gray-300 hover:bg-gray-50"),children:e},e)),(0,s.jsx)("button",{onClick:()=>v(x.currentPage+1),disabled:!x.hasNextPage,className:"px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Next"})]})]})]})})}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(1469),a=r.n(s)}}]);