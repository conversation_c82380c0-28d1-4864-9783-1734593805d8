(()=>{var e={};e.id=5413,e.ids=[5413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20145:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>p});var i=t(96559),o=t(48088),a=t(37719),u=t(56814),n=t(42335),l=t(32190);async function p(e){let r=await (0,u.j2)();if(!r?.user?.id)return l.NextResponse.json({error:"Unauthorized"},{status:401});try{let r=(await e.formData()).get("file");if(!r)return l.NextResponse.json({error:"No file provided"},{status:400});if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(r.type))return l.NextResponse.json({error:"Invalid file type. Only PDF and Word documents are allowed."},{status:400});if(r.size>0xa00000)return l.NextResponse.json({error:"File size too large. Maximum size is 10MB."},{status:400});let t=Date.now(),s=Math.random().toString(36).substring(7),i=r.name.split(".").pop(),o=`manuscript-${t}-${s}.${i}`,a=await (0,n.yJ)(`manuscripts/${o}`,r,{access:"public",contentType:r.type});return console.log(`✅ File uploaded successfully: ${a.url}`),l.NextResponse.json({success:!0,url:a.url,filename:r.name,size:r.size,type:r.type})}catch(e){return console.error("❌ File upload failed:",e),l.NextResponse.json({error:"File upload failed"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:x}=c;function g(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},41204:e=>{"use strict";e.exports=require("string_decoder")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>l,j2:()=>d});var s=t(19443),i=t(16467),o=t(5956),a=t(10189),u=t(56056),n=t(85663);let{handlers:l,signIn:p,signOut:c,auth:d}=(0,s.Ay)({adapter:(0,i.y)(o.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[u.A,(0,a.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await o.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!r||!r.password||!await n.Ay.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,image:r.image,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,s=r.pathname.startsWith("/dashboard"),i=r.pathname.startsWith("/manuscripts"),o=r.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",t),console.log("\uD83D\uDD10 Authorized callback - pathname:",r.pathname),!s&&!i&&!o||t},session:async({session:e,token:r})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",r),r?.sub&&(e.user.id=r.sub,r.name&&(e.user.name=r.name),r.email&&(e.user.email=r.email),r.picture&&(e.user.image=r.picture),r.role&&(e.user.role=r.role)),e),jwt:async({token:e,user:r,account:t})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",r),console.log("\uD83D\uDD10 JWT callback - account:",t),r&&(e.sub=r.id,e.name=r.name,e.email=r.email,e.picture=r.image,e.role=r.role),e)}})},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,2190,5663,9404,3285],()=>t(20145));module.exports=s})();