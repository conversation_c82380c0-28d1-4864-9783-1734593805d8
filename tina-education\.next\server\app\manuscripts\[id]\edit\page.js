(()=>{var e={};e.id=9963,e.ids=[9963],e.modules={1959:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(60687),s=r(7203),o=r(76928),i=r(70297),n=r(82221);r(43210);var l=r(38958),c=r.n(l);let d=({content:e,onChange:t,placeholder:r="Enter manuscript content...",className:l=""})=>{let d=(0,s.hG)({extensions:[o.A.configure({heading:{levels:[1,2,3,4]}}),i.A.configure({placeholder:r}),n.A],content:e,onUpdate:({editor:e})=>{t(e.getHTML())},editorProps:{attributes:{class:"focus:outline-none"}}});return d?(0,a.jsxs)("div",{className:`${c().editor} ${l}`,children:[(0,a.jsxs)("div",{className:c().toolbar,children:[(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleBold().run(),className:`${c().toolbarButton} ${d.isActive("bold")?c().active:""}`,children:(0,a.jsx)("strong",{children:"B"})}),(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleItalic().run(),className:`${c().toolbarButton} ${d.isActive("italic")?c().active:""}`,children:(0,a.jsx)("em",{children:"I"})}),(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleStrike().run(),className:`${c().toolbarButton} ${d.isActive("strike")?c().active:""}`,children:(0,a.jsx)("s",{children:"S"})})]}),(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleHeading({level:1}).run(),className:`${c().toolbarButton} ${d.isActive("heading",{level:1})?c().active:""}`,children:"H1"}),(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleHeading({level:2}).run(),className:`${c().toolbarButton} ${d.isActive("heading",{level:2})?c().active:""}`,children:"H2"}),(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleHeading({level:3}).run(),className:`${c().toolbarButton} ${d.isActive("heading",{level:3})?c().active:""}`,children:"H3"})]}),(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleBulletList().run(),className:`${c().toolbarButton} ${d.isActive("bulletList")?c().active:""}`,children:"• List"}),(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleOrderedList().run(),className:`${c().toolbarButton} ${d.isActive("orderedList")?c().active:""}`,children:"1. List"})]}),(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleBlockquote().run(),className:`${c().toolbarButton} ${d.isActive("blockquote")?c().active:""}`,children:"Quote"}),(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleCode().run(),className:`${c().toolbarButton} ${d.isActive("code")?c().active:""}`,children:"Code"})]}),(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().setHorizontalRule().run(),className:c().toolbarButton,children:"HR"}),(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().undo().run(),disabled:!d.can().undo(),className:c().toolbarButton,children:"Undo"}),(0,a.jsx)("button",{type:"button",onClick:()=>d.chain().focus().redo().run(),disabled:!d.can().redo(),className:c().toolbarButton,children:"Redo"})]})]}),(0,a.jsx)("div",{className:c().editorContent,children:(0,a.jsx)(s.$Z,{editor:d})}),(0,a.jsxs)("div",{className:c().characterCount,children:[d.storage.characterCount.characters()," characters"]})]}):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var a=r(96330);let s=globalThis.prisma||new a.PrismaClient},8660:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(37413),s=r(56814),o=r(5956),i=r(39916),n=r(75931);async function l(e,t){try{let r=await o.z.manuscript.findUnique({where:{id:e},include:{user:{select:{name:!0,email:!0}}}});if(!r||r.author_id!==t)return null;return r}catch(e){return console.error("Error fetching manuscript:",e),null}}async function c({params:e}){let t=await (0,s.j2)();t?.user?.id||(0,i.redirect)("/auth/signin");let{id:r}=await e,o=await l(r,t.user.id);return o||(0,i.notFound)(),(0,a.jsx)("div",{className:"min-h-screen bg-gray-100",children:(0,a.jsx)(n.default,{manuscript:o})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38302:(e,t,r)=>{Promise.resolve().then(r.bind(r,64917))},38958:e=>{e.exports={editor:"RichTextEditor_editor__f90n2",toolbar:"RichTextEditor_toolbar__072Cb",toolbarGroup:"RichTextEditor_toolbarGroup__Pkt5f",toolbarButton:"RichTextEditor_toolbarButton__frh0r",active:"RichTextEditor_active__ir8Mh",editorContent:"RichTextEditor_editorContent__A1QHg",ProseMirror:"RichTextEditor_ProseMirror___JNXh","is-editor-empty":"RichTextEditor_is-editor-empty__OyISz",characterCount:"RichTextEditor_characterCount__c9x5l"}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>c,j2:()=>m});var a=r(19443),s=r(16467),o=r(5956),i=r(10189),n=r(56056),l=r(85663);let{handlers:c,signIn:d,signOut:u,auth:m}=(0,a.Ay)({adapter:(0,s.y)(o.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[n.A,(0,i.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await o.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!t||!t.password||!await l.Ay.compare(e.password,t.password))return null;return{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,a=t.pathname.startsWith("/dashboard"),s=t.pathname.startsWith("/manuscripts"),o=t.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",t.pathname),!a&&!s&&!o||r},session:async({session:e,token:t})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",t),t?.sub&&(e.user.id=t.sub,t.name&&(e.user.name=t.name),t.email&&(e.user.email=t.email),t.picture&&(e.user.image=t.picture),t.role&&(e.user.role=t.role)),e),jwt:async({token:e,user:t,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",t),console.log("\uD83D\uDD10 JWT callback - account:",r),t&&(e.sub=t.id,e.name=t.name,e.email=t.email,e.picture=t.image,e.role=t.role),e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64917:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(60687),s=r(43210),o=r(99208),i=r(85814),n=r.n(i),l=r(16189),c=r(1959),d=r(90706);function u({manuscript:e}){let{data:t}=(0,o.wV)(),r=(0,l.useRouter)(),[i,u]=(0,s.useState)(e.title),[m,p]=(0,s.useState)(e.abstract),[h,x]=(0,s.useState)(e.content),[b,g]=(0,s.useState)(e.keywords),[f,v]=(0,s.useState)(e.uploadedFile&&e.uploadedFileName?{url:e.uploadedFile,name:e.uploadedFileName}:null),[y,j]=(0,s.useState)(!1),[N,w]=(0,s.useState)(""),[k,C]=(0,s.useState)("");if(!t)return(0,a.jsx)("p",{children:"You must be logged in to edit a manuscript."});let _=async t=>{t.preventDefault(),j(!0),w(""),C("");try{let t=await fetch(`/api/manuscripts/${e.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:i,abstract:m,content:h,keywords:b,uploadedFile:f?.url,uploadedFileName:f?.name})});if(!t.ok){let e=await t.json();throw Error(e.error||"Update failed")}C("Manuscript updated successfully!"),setTimeout(()=>{r.push(`/manuscripts/${e.id}`)},1500)}catch(e){w(e instanceof Error?e.message:"Update error")}finally{j(!1)}},A={hidden_tag:()=>(0,a.jsx)(a.Fragment,{}),title:(0,a.jsx)("input",{type:"text",id:"title",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter Title",value:i,onChange:e=>u(e.target.value),required:!0}),abstract:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("label",{htmlFor:"abstract",className:"block text-gray-700 text-sm font-bold mb-2",children:"Abstract"}),(0,a.jsx)("textarea",{id:"abstract",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter Abstract",value:m,onChange:e=>p(e.target.value),required:!0})]}),content:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("label",{htmlFor:"content",className:"block text-gray-700 text-sm font-bold mb-2",children:"Manuscript Content"}),(0,a.jsx)(c.A,{content:h,onChange:x,placeholder:"Enter your manuscript content here. Use the toolbar above to format your text with headings, lists, quotes, and more...",className:"w-full"})]}),keywords:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("label",{htmlFor:"keywords",className:"block text-gray-700 text-sm font-bold mb-2",children:"Keywords"}),(0,a.jsx)("input",{type:"text",id:"keywords",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter keywords (comma separated)",value:b,onChange:e=>g(e.target.value),required:!0})]})};return(0,a.jsx)("div",{className:"bg-gray-100 min-h-screen py-6 mt-8",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-3xl bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mt-8 mb-4",children:[(0,a.jsxs)(n(),{href:`/manuscripts/${e.id}`,className:"text-indigo-600 hover:text-indigo-800 mb-4 inline-block",children:[(0,a.jsx)("i",{className:"mr-2",children:"<="}),"Back to Manuscript"]}),(0,a.jsxs)("form",{className:"mb-4",id:"manuscriptForm",method:"POST",onSubmit:_,children:[A.hidden_tag(),(0,a.jsx)("h1",{className:"text-center text-2xl font-bold text-gray-800 mb-6",children:"Edit Manuscript"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{htmlFor:"title",className:"block text-gray-700 text-sm font-bold mb-2",children:"Title"}),A.title]}),(0,a.jsx)("div",{className:"mb-4",children:A.abstract}),(0,a.jsx)("div",{className:"mb-4",children:A.content}),(0,a.jsx)("div",{className:"mb-4",children:A.keywords}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Upload Manuscript File (Optional)"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"You can upload a PDF or Word document instead of or in addition to typing your content above."}),(0,a.jsx)(d.A,{onFileUpload:(e,t)=>v({url:e,name:t}),onFileRemove:()=>v(null),uploadedFile:f,disabled:y})]}),N&&(0,a.jsx)("div",{className:"text-red-600 mb-2",children:N}),k&&(0,a.jsx)("div",{className:"text-green-600 mb-2",children:k}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(n(),{href:`/manuscripts/${e.id}`,className:"bg-transparent hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 border border-gray-300 rounded shadow-sm hover:border-transparent",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",disabled:y,children:y?"Updating...":"Update Manuscript"})]})]})]})})}},66841:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=r(65239),s=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["manuscripts",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8660)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\[id]\\edit\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/manuscripts/[id]/edit/page",pathname:"/manuscripts/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},75931:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\ManuscriptEditForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\ManuscriptEditForm.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},90706:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687),s=r(43210),o=r(69587);let i=({onFileUpload:e,onFileRemove:t,uploadedFile:r,disabled:i=!1})=>{let[n,l]=(0,s.useState)(!1),[c,d]=(0,s.useState)(!1),[u,m]=(0,s.useState)(""),p=(0,s.useRef)(null),h=async t=>{if(t){if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(t.type))return void m("Invalid file type. Only PDF and Word documents are allowed.");if(t.size>0xa00000)return void m("File size too large. Maximum size is 10MB.");m(""),l(!0);try{let r=new FormData;r.append("file",t);let a=await fetch("/api/upload",{method:"POST",body:r}),s=await a.json();if(!a.ok)throw Error(s.error||"Upload failed");e(s.url,s.filename)}catch(e){m(e instanceof Error?e.message:"Upload failed")}finally{l(!1)}}};return r?(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-4 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:(e=>{let t=e.split(".").pop()?.toLowerCase();return"pdf"===t?(0,a.jsx)(o.kl1,{className:"text-red-500"}):"doc"===t||"docx"===t?(0,a.jsx)(o.WLb,{className:"text-blue-500"}):(0,a.jsx)(o.EHs,{className:"text-gray-500"})})(r.name)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:r.name}),(0,a.jsx)("a",{href:r.url,target:"_blank",rel:"noopener noreferrer",className:"text-xs text-blue-600 hover:text-blue-800 underline",children:"View file"})]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>{t(),p.current&&(p.current.value="")},disabled:i,className:"text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${c?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"} ${i?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onDrop:e=>{e.preventDefault(),d(!1);let t=e.dataTransfer.files[0];t&&h(t)},onDragOver:e=>{e.preventDefault(),d(!0)},onDragLeave:e=>{e.preventDefault(),d(!1)},onClick:()=>!i&&p.current?.click(),children:[(0,a.jsx)("input",{ref:p,type:"file",accept:".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",onChange:e=>{let t=e.target.files?.[0];t&&h(t)},className:"hidden",disabled:i}),n?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-4xl",children:"\uD83D\uDCCE"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Drop your file here or click to browse"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"PDF or Word documents only (max 10MB)"})]})]})]}),u&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:u})]})}},91446:(e,t,r)=>{Promise.resolve().then(r.bind(r,75931))},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,2190,5663,9404,7945,1658,606,1795],()=>r(66841));module.exports=a})();