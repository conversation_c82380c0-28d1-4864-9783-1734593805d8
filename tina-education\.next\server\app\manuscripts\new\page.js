(()=>{var e={};e.id=3333,e.ids=[3333],e.modules={1959:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(60687),o=r(7203),s=r(76928),i=r(70297),a=r(82221);r(43210);var l=r(38958),c=r.n(l);let d=({content:e,onChange:t,placeholder:r="Enter manuscript content...",className:l=""})=>{let d=(0,o.hG)({extensions:[s.A.configure({heading:{levels:[1,2,3,4]}}),i.A.configure({placeholder:r}),a.A],content:e,onUpdate:({editor:e})=>{t(e.getHTML())},editorProps:{attributes:{class:"focus:outline-none"}}});return d?(0,n.jsxs)("div",{className:`${c().editor} ${l}`,children:[(0,n.jsxs)("div",{className:c().toolbar,children:[(0,n.jsxs)("div",{className:c().toolbarGroup,children:[(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleBold().run(),className:`${c().toolbarButton} ${d.isActive("bold")?c().active:""}`,children:(0,n.jsx)("strong",{children:"B"})}),(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleItalic().run(),className:`${c().toolbarButton} ${d.isActive("italic")?c().active:""}`,children:(0,n.jsx)("em",{children:"I"})}),(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleStrike().run(),className:`${c().toolbarButton} ${d.isActive("strike")?c().active:""}`,children:(0,n.jsx)("s",{children:"S"})})]}),(0,n.jsxs)("div",{className:c().toolbarGroup,children:[(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleHeading({level:1}).run(),className:`${c().toolbarButton} ${d.isActive("heading",{level:1})?c().active:""}`,children:"H1"}),(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleHeading({level:2}).run(),className:`${c().toolbarButton} ${d.isActive("heading",{level:2})?c().active:""}`,children:"H2"}),(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleHeading({level:3}).run(),className:`${c().toolbarButton} ${d.isActive("heading",{level:3})?c().active:""}`,children:"H3"})]}),(0,n.jsxs)("div",{className:c().toolbarGroup,children:[(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleBulletList().run(),className:`${c().toolbarButton} ${d.isActive("bulletList")?c().active:""}`,children:"• List"}),(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleOrderedList().run(),className:`${c().toolbarButton} ${d.isActive("orderedList")?c().active:""}`,children:"1. List"})]}),(0,n.jsxs)("div",{className:c().toolbarGroup,children:[(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleBlockquote().run(),className:`${c().toolbarButton} ${d.isActive("blockquote")?c().active:""}`,children:"Quote"}),(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().toggleCode().run(),className:`${c().toolbarButton} ${d.isActive("code")?c().active:""}`,children:"Code"})]}),(0,n.jsxs)("div",{className:c().toolbarGroup,children:[(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().setHorizontalRule().run(),className:c().toolbarButton,children:"HR"}),(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().undo().run(),disabled:!d.can().undo(),className:c().toolbarButton,children:"Undo"}),(0,n.jsx)("button",{type:"button",onClick:()=>d.chain().focus().redo().run(),disabled:!d.can().redo(),className:c().toolbarButton,children:"Redo"})]})]}),(0,n.jsx)("div",{className:c().editorContent,children:(0,n.jsx)(o.$Z,{editor:d})}),(0,n.jsxs)("div",{className:c().characterCount,children:[d.storage.characterCount.characters()," characters"]})]}):null}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8704:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return o},getAccessFallbackErrorTypeByStatus:function(){return a},getAccessFallbackHTTPStatus:function(){return i},isHTTPAccessFallbackError:function(){return s}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),o="NEXT_HTTP_ERROR_FALLBACK";function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===o&&n.has(Number(r))}function i(e){return Number(e.digest.split(";")[1])}function a(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22175:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>o});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call __NEXTAUTH() from the server but __NEXTAUTH is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","__NEXTAUTH"),(0,n.registerClientReference)(function(){throw Error("Attempted to call SessionContext() from the server but SessionContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","SessionContext"),(0,n.registerClientReference)(function(){throw Error("Attempted to call useSession() from the server but useSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","useSession"),(0,n.registerClientReference)(function(){throw Error("Attempted to call getSession() from the server but getSession is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","getSession"),(0,n.registerClientReference)(function(){throw Error("Attempted to call getCsrfToken() from the server but getCsrfToken is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","getCsrfToken"),(0,n.registerClientReference)(function(){throw Error("Attempted to call getProviders() from the server but getProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","getProviders"),(0,n.registerClientReference)(function(){throw Error("Attempted to call signIn() from the server but signIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","signIn"),(0,n.registerClientReference)(function(){throw Error("Attempted to call signOut() from the server but signOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","signOut");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\node_modules\\next-auth\\react.js","SessionProvider")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31161:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\submit.tsx","default")},31162:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return s}});let n=r(8704),o=r(49026);function s(e){return(0,o.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31213:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var n=r(65239),o=r(48088),s=r(88170),i=r.n(s),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c={children:["",{children:["manuscripts",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95418)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\new\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\new\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/manuscripts/new/page",pathname:"/manuscripts/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33873:e=>{"use strict";e.exports=require("path")},38958:e=>{e.exports={editor:"RichTextEditor_editor__f90n2",toolbar:"RichTextEditor_toolbar__072Cb",toolbarGroup:"RichTextEditor_toolbarGroup__Pkt5f",toolbarButton:"RichTextEditor_toolbarButton__frh0r",active:"RichTextEditor_active__ir8Mh",editorContent:"RichTextEditor_editorContent__A1QHg",ProseMirror:"RichTextEditor_ProseMirror___JNXh","is-editor-empty":"RichTextEditor_is-editor-empty__OyISz",characterCount:"RichTextEditor_characterCount__c9x5l"}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},46295:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(60687),o=r(43210),s=r(99208),i=r(85814),a=r.n(i),l=r(1959),c=r(90706);function d(){let[e,t]=(0,o.useState)(""),[r,i]=(0,o.useState)(""),[d,u]=(0,o.useState)(""),[p,f]=(0,o.useState)(""),[m,b]=(0,o.useState)(null),[h,x]=(0,o.useState)(!1),[v,g]=(0,o.useState)(""),[y,j]=(0,o.useState)(""),{data:R}=(0,s.wV)();if(!R)return(0,n.jsx)("div",{className:"h-40",children:(0,n.jsx)("p",{className:"text-red-600 mb-8",children:"You must be logged in to submit a manuscript."})});let _=async(n,o=!1)=>{n.preventDefault(),x(!0),g(""),j("");try{if(!(await fetch("/api/manuscripts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:e,abstract:r,content:d,keywords:p,uploadedFile:m?.url,uploadedFileName:m?.name,isDraft:o})})).ok)throw Error(o?"Failed to save draft":"Submission failed");j(o?"Draft saved successfully!":"Manuscript submitted successfully!"),o||setTimeout(()=>{window.location.href="/dashboard/manuscripts"},1500),t(""),i(""),u(""),f(""),b(null)}catch(e){g(e instanceof Error?e.message:o?"Failed to save draft":"Submission error")}finally{x(!1)}},C={hidden_tag:()=>(0,n.jsx)("input",{type:"hidden",name:"csrf_token",value:"dummy_token"}),title:(0,n.jsx)("input",{type:"text",id:"title",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter manuscript title",value:e,onChange:e=>t(e.target.value),required:!0}),abstract:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("label",{htmlFor:"abstract",className:"block text-gray-700 text-sm font-bold mb-2",children:"Abstract"}),(0,n.jsx)("textarea",{id:"abstract",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter Abstract",value:r,onChange:e=>i(e.target.value),required:!0})]}),content:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("label",{htmlFor:"content",className:"block text-gray-700 text-sm font-bold mb-2",children:"Manuscript Content"}),(0,n.jsx)(l.A,{content:d,onChange:u,placeholder:"Enter your manuscript content here. Use the toolbar above to format your text with headings, lists, quotes, and more...",className:"w-full"})]}),keywords:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("label",{htmlFor:"keywords",className:"block text-gray-700 text-sm font-bold mb-2",children:"Keywords"}),(0,n.jsx)("input",{type:"text",id:"keywords",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter keywords (comma separated)",value:p,onChange:e=>f(e.target.value),required:!0})]}),submit:()=>(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsx)("button",{type:"button",onClick:e=>_(e,!0),disabled:h,className:"bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50",children:h?"Saving...":"Save as Draft"}),(0,n.jsx)("button",{type:"submit",onClick:e=>_(e,!1),disabled:h,className:"bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50",children:h?"Submitting...":"Submit Manuscript"})]})};return(0,n.jsx)("div",{className:"bg-gray-100 min-h-screen py-6 mt-8",children:(0,n.jsxs)("div",{className:"container mx-auto max-w-3xl bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mt-8 mb-4",children:[(0,n.jsxs)(a(),{href:"/dashboard",className:"text-indigo-600 hover:text-indigo-800 mb-4 inline-block",children:[(0,n.jsx)("i",{className:"mr-2",children:"<="}),"Back to Dashboard"]}),(0,n.jsxs)("form",{className:"mb-4",id:"manuscriptForm",method:"POST",onSubmit:e=>_(e,!1),children:[C.hidden_tag(),(0,n.jsx)("h1",{className:"text-center text-2xl font-bold text-gray-800 mb-6",children:"Manuscript Submission"}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{htmlFor:"title",className:"block text-gray-700 text-sm font-bold mb-2",children:"Title"}),C.title]}),(0,n.jsx)("div",{className:"mb-4",children:C.abstract}),(0,n.jsx)("div",{className:"mb-4",children:C.content}),(0,n.jsx)("div",{className:"mb-4",children:C.keywords}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Upload Manuscript File (Optional)"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"You can upload a PDF or Word document instead of or in addition to typing your content above."}),(0,n.jsx)(c.A,{onFileUpload:(e,t)=>b({url:e,name:t}),onFileRemove:()=>b(null),uploadedFile:m,disabled:h})]}),v&&(0,n.jsx)("div",{className:"text-red-600 mb-2",children:v}),y&&(0,n.jsx)("div",{className:"text-green-600 mb-2",children:y}),(0,n.jsxs)("div",{className:"flex items-center justify-end",children:[(0,n.jsx)(a(),{href:"/guidelines",className:"bg-transparent hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 border border-gray-300 rounded shadow-sm hover:border-transparent mr-2",children:"Submission Guidelines"}),C.submit()]})]})]})})}},49026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return o},RedirectType:function(){return s},isRedirectError:function(){return i}});let n=r(52836),o="NEXT_REDIRECT";var s=function(e){return e.push="push",e.replace="replace",e}({});function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,s]=t,i=t.slice(2,-2).join(";"),a=Number(t.at(-2));return r===o&&("replace"===s||"push"===s)&&"string"==typeof i&&!isNaN(a)&&a in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51846:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return o}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},52637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},52836:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var a=s?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(n,i,a):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(61120));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}let s={current:null},i="function"==typeof n.cache?n.cache:e=>e,a=console.warn;function l(e){return function(...t){a(e(...t))}}i(e=>{try{a(s.current)}finally{s.current=null}})},79551:e=>{"use strict";e.exports=require("url")},81539:(e,t,r)=>{Promise.resolve().then(r.bind(r,31161)),Promise.resolve().then(r.bind(r,22175))},90706:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(60687),o=r(43210),s=r(69587);let i=({onFileUpload:e,onFileRemove:t,uploadedFile:r,disabled:i=!1})=>{let[a,l]=(0,o.useState)(!1),[c,d]=(0,o.useState)(!1),[u,p]=(0,o.useState)(""),f=(0,o.useRef)(null),m=async t=>{if(t){if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(t.type))return void p("Invalid file type. Only PDF and Word documents are allowed.");if(t.size>0xa00000)return void p("File size too large. Maximum size is 10MB.");p(""),l(!0);try{let r=new FormData;r.append("file",t);let n=await fetch("/api/upload",{method:"POST",body:r}),o=await n.json();if(!n.ok)throw Error(o.error||"Upload failed");e(o.url,o.filename)}catch(e){p(e instanceof Error?e.message:"Upload failed")}finally{l(!1)}}};return r?(0,n.jsx)("div",{className:"border border-gray-300 rounded-lg p-4 bg-gray-50",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("span",{className:"text-2xl",children:(e=>{let t=e.split(".").pop()?.toLowerCase();return"pdf"===t?(0,n.jsx)(s.kl1,{className:"text-red-500"}):"doc"===t||"docx"===t?(0,n.jsx)(s.WLb,{className:"text-blue-500"}):(0,n.jsx)(s.EHs,{className:"text-gray-500"})})(r.name)}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-700",children:r.name}),(0,n.jsx)("a",{href:r.url,target:"_blank",rel:"noopener noreferrer",className:"text-xs text-blue-600 hover:text-blue-800 underline",children:"View file"})]})]}),(0,n.jsx)("button",{type:"button",onClick:()=>{t(),f.current&&(f.current.value="")},disabled:i,className:"text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}):(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${c?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"} ${i?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onDrop:e=>{e.preventDefault(),d(!1);let t=e.dataTransfer.files[0];t&&m(t)},onDragOver:e=>{e.preventDefault(),d(!0)},onDragLeave:e=>{e.preventDefault(),d(!1)},onClick:()=>!i&&f.current?.click(),children:[(0,n.jsx)("input",{ref:f,type:"file",accept:".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",onChange:e=>{let t=e.target.files?.[0];t&&m(t)},className:"hidden",disabled:i}),a?(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("div",{className:"text-4xl",children:"\uD83D\uDCCE"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Drop your file here or click to browse"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"PDF or Word documents only (max 10MB)"})]})]})]}),u&&(0,n.jsx)("p",{className:"text-sm text-red-600",children:u})]})}},95418:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(37413),o=r(22175),s=r(31161);function i(){return(0,n.jsx)(o.SessionProvider,{children:(0,n.jsx)(s.default,{})})}},95611:(e,t,r)=>{Promise.resolve().then(r.bind(r,46295)),Promise.resolve().then(r.bind(r,99208))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7945,1658,606,1795],()=>r(31213));module.exports=n})();