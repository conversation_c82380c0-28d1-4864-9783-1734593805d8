{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/_not-found/page": "app/_not-found/page.js", "/page": "app/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/manuscripts/page": "app/dashboard/manuscripts/page.js", "/dashboard/notifications/page": "app/dashboard/notifications/page.js", "/dashboard/reviews/page": "app/dashboard/reviews/page.js", "/api/admin/reviewer-applications/[id]/route": "app/api/admin/reviewer-applications/[id]/route.js", "/api/admin/users/[id]/role/route": "app/api/admin/users/[id]/role/route.js", "/api/auth/notifications/[id]/route": "app/api/auth/notifications/[id]/route.js", "/api/auth/notifications/route": "app/api/auth/notifications/route.js", "/api/auth/signup/route": "app/api/auth/signup/route.js", "/api/manuscripts/route": "app/api/manuscripts/route.js", "/api/genres/route": "app/api/genres/route.js", "/api/manuscripts/[id]/route": "app/api/manuscripts/[id]/route.js", "/api/profile/route": "app/api/profile/route.js", "/api/repository/articles/route": "app/api/repository/articles/route.js", "/api/repository/publication/[id]/route": "app/api/repository/publication/[id]/route.js", "/api/repository/books/route": "app/api/repository/books/route.js", "/api/repository/journals/route": "app/api/repository/journals/route.js", "/api/repository/route": "app/api/repository/route.js", "/api/reviewer-application/route": "app/api/reviewer-application/route.js", "/api/reviews/[id]/submit/route": "app/api/reviews/[id]/submit/route.js", "/api/reviews/[id]/messages/route": "app/api/reviews/[id]/messages/route.js", "/api/reviews/[id]/route": "app/api/reviews/[id]/route.js", "/api/publications/route": "app/api/publications/route.js", "/api/reviews/[id]/update/route": "app/api/reviews/[id]/update/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/wishlist/check/route": "app/api/wishlist/check/route.js", "/api/wishlist/route": "app/api/wishlist/route.js", "/articles/page": "app/articles/page.js", "/books/page": "app/books/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/cart/page": "app/cart/page.js", "/journals/page": "app/journals/page.js", "/manuscripts/[id]/edit/page": "app/manuscripts/[id]/edit/page.js", "/manuscripts/[id]/page": "app/manuscripts/[id]/page.js", "/profile/page": "app/profile/page.js", "/manuscripts/new/page": "app/manuscripts/new/page.js", "/repository/page": "app/repository/page.js", "/reviewer-application/page": "app/reviewer-application/page.js", "/repository/[id]/page": "app/repository/[id]/page.js", "/publications/new/page": "app/publications/new/page.js", "/review/page": "app/review/page.js", "/reviews/[id]/review/page": "app/reviews/[id]/review/page.js", "/wishlist/page": "app/wishlist/page.js", "/dashboard/library/page": "app/dashboard/library/page.js", "/dashboard/admin/page": "app/dashboard/admin/page.js", "/dashboard/analytics/page": "app/dashboard/analytics/page.js", "/dashboard/messages/page": "app/dashboard/messages/page.js", "/dashboard/calendar/page": "app/dashboard/calendar/page.js", "/dashboard/collaborators/page": "app/dashboard/collaborators/page.js", "/dashboard/tools/page": "app/dashboard/tools/page.js", "/dashboard/publications/page": "app/dashboard/publications/page.js", "/dashboard/settings/page": "app/dashboard/settings/page.js"}