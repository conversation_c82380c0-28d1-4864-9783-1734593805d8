(()=>{var e={};e.id=4705,e.ids=[4705],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},58394:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(5956);async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("search")||"",s=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"12"),i=t.get("sortBy")||"createdAt",n=t.get("sortOrder")||"desc",p={type:"JOURNAL"};r&&(p.OR=[{title:{contains:r,mode:"insensitive"}},{abstract:{contains:r,mode:"insensitive"}},{keywords:{contains:r,mode:"insensitive"}},{user:{name:{contains:r,mode:"insensitive"}}}]);let c=(s-1)*a,[d,l]=await Promise.all([u.z.publication.findMany({where:p,include:{user:{select:{name:!0,email:!0}}},orderBy:{[i]:n},skip:c,take:a}),u.z.publication.count({where:p})]),g=Math.ceil(l/a);return o.NextResponse.json({journals:d.map(e=>({...e,createdAt:e.createdAt.toISOString(),updatedAt:e.updatedAt.toISOString()})),pagination:{currentPage:s,totalPages:g,totalCount:l,hasNextPage:s<g,hasPrevPage:s>1,limit:a}})}catch(e){return console.error("Failed to fetch journals:",e),o.NextResponse.json({error:"Failed to fetch journals"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/repository/journals/route",pathname:"/api/repository/journals",filename:"route",bundlePath:"app/api/repository/journals/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\journals\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=c;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190],()=>r(58394));module.exports=s})();