"use strict";(()=>{var e={};e.id=9929,e.ids=[9929],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8087:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{PATCH:()=>l});var i=t(96559),n=t(48088),a=t(37719),o=t(56814),p=t(5956),d=t(32190);let u=new(t(20549)).u(process.env.RESEND_API_KEY);async function l(e,{params:r}){let t=await (0,o.j2)();if(!t?.user?.id)return d.NextResponse.json({error:"Unauthorized"},{status:401});try{let{action:s,reason:i}=await e.json(),{id:n}=await r;if(!["ACCEPT_ASSIGNMENT","DECLINE_ASSIGNMENT"].includes(s))return d.NextResponse.json({error:"Invalid action"},{status:400});let a=await p.z.review.findUnique({where:{id:n},include:{manuscript:{include:{user:{select:{id:!0,name:!0,email:!0}}}},user:{select:{name:!0,email:!0}}}});if(!a)return d.NextResponse.json({error:"Review not found"},{status:404});if(a.reviewer_id!==t.user.id)return d.NextResponse.json({error:"Forbidden"},{status:403});if("PENDING"!==a.status)return d.NextResponse.json({error:"Review assignment has already been responded to"},{status:400});let o=await p.z.review.update({where:{id:n},data:{status:"ACCEPT_ASSIGNMENT"===s?"ACCEPTED":"DECLINED",feedback:i||null,updatedAt:new Date}}),l="ACCEPT_ASSIGNMENT"===s?"accepted":"declined";console.log(`📝 Review assignment ${l} by ${t.user.name}: ${a.manuscript.title}`),await p.z.notification.create({data:{userId:a.manuscript.user.id,title:`Reviewer ${"ACCEPT_ASSIGNMENT"===s?"Accepted":"Declined"} Assignment`,message:`${a.user.name} has ${l} the review assignment for "${a.manuscript.title}"`,type:`ASSIGNMENT_${"ACCEPT_ASSIGNMENT"===s?"ACCEPTED":"DECLINED"}`,relatedId:a.manuscript.id}}),console.log(`✅ Notification created for author: ${a.manuscript.user.email}`);try{let e="ACCEPT_ASSIGNMENT"===s,r=await u.emails.send({from:"Tina Education <<EMAIL>>",to:a.manuscript.user.email,subject:`Reviewer ${e?"Accepted":"Declined"} Assignment: "${a.manuscript.title}"`,html:`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: ${e?"#f0f9ff":"#fef2f2"}; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: ${e?"#1e40af":"#dc2626"}; margin: 0;">
                ${e?"✅":"❌"} Review Assignment ${e?"Accepted":"Declined"}
              </h2>
            </div>

            <p>Hello <strong>${a.manuscript.user.name}</strong>,</p>

            <p>We have an update regarding the review assignment for your manuscript:</p>

            <div style="background-color: #f1f5f9; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <h3 style="margin: 0 0 10px 0; color: #334155;">📄 Assignment Details</h3>
              <p style="margin: 5px 0;"><strong>Title:</strong> ${a.manuscript.title}</p>
              <p style="margin: 5px 0;"><strong>Reviewer:</strong> ${a.user.name}</p>
              <p style="margin: 5px 0;"><strong>Status:</strong> <span style="color: ${e?"#16a34a":"#dc2626"}; font-weight: bold;">${e?"ACCEPTED":"DECLINED"}</span></p>
            </div>

            ${i?`
              <div style="background-color: #f8fafc; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #334155;">💬 ${e?"Reviewer Note":"Decline Reason"}</h3>
                <p style="margin: 0; line-height: 1.6;">${i}</p>
              </div>
            `:""}

            ${e?"<p><strong>Next Steps:</strong> The reviewer will now begin the review process. You will be notified when the review is completed.</p>":"<p><strong>Next Steps:</strong> We will assign another reviewer to your manuscript. You will be notified when a new reviewer accepts the assignment.</p>"}

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXTAUTH_URL||"http://localhost:3000"}/dashboard/manuscripts"
                 style="background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                View My Manuscripts
              </a>
            </div>

            <p>Thank you for using Tina Education.</p>

            <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
            <p style="color: #64748b; font-size: 14px;">
              Best regards,<br>
              Tina Education<br>
              <small>This is an automated notification.</small>
            </p>
          </div>
        `});console.log(`✅ Email sent to author: ${a.manuscript.user.email}`,r)}catch(e){console.error(`❌ Failed to send email to author:`,e)}return d.NextResponse.json({success:!0,review:o,message:`Assignment ${l} successfully`})}catch(e){return console.error("Failed to update review:",e),d.NextResponse.json({error:"Internal Server Error"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reviews/[id]/route",pathname:"/api/reviews/[id]",filename:"route",bundlePath:"app/api/reviews/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:x}=c;function v(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")},96330:e=>{e.exports=require("@prisma/client")},96559:(e,r,t)=>{e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,2190,5663,9404,9468],()=>t(8087));module.exports=s})();