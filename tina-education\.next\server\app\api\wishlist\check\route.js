(()=>{var e={};e.id=7766,e.ids=[7766],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11046:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>u});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),l=r(56814),c=r(5956);async function u(e){let t=await (0,l.j2)();if(!t?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});try{let{searchParams:r}=new URL(e.url),s=r.get("publicationId"),a=r.get("selectedType");if(!s||!a)return n.NextResponse.json({error:"Publication ID and selected type are required"},{status:400});let i=await c.z.wishlistItem.findUnique({where:{userId_publicationId_selectedType:{userId:t.user.id,publicationId:s,selectedType:a}}});return n.NextResponse.json({isInWishlist:!!i,wishlistItem:i?{...i,createdAt:i.createdAt.toISOString(),updatedAt:i.updatedAt.toISOString()}:null})}catch(e){return console.error("Failed to check wishlist:",e),n.NextResponse.json({error:"Failed to check wishlist"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wishlist/check/route",pathname:"/api/wishlist/check",filename:"route",bundlePath:"app/api/wishlist/check/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\wishlist\\check\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:h}=p;function g(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>c,j2:()=>d});var s=r(19443),a=r(16467),i=r(5956),o=r(10189),n=r(56056),l=r(85663);let{handlers:c,signIn:u,signOut:p,auth:d}=(0,s.Ay)({adapter:(0,a.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[n.A,(0,o.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!t||!t.password||!await l.Ay.compare(e.password,t.password))return null;return{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,s=t.pathname.startsWith("/dashboard"),a=t.pathname.startsWith("/manuscripts"),i=t.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",t.pathname),!s&&!a&&!i||r},session:async({session:e,token:t})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",t),t?.sub&&(e.user.id=t.sub,t.name&&(e.user.name=t.name),t.email&&(e.user.email=t.email),t.picture&&(e.user.image=t.picture),t.role&&(e.user.role=t.role)),e),jwt:async({token:e,user:t,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",t),console.log("\uD83D\uDD10 JWT callback - account:",r),t&&(e.sub=t.id,e.name=t.name,e.email=t.email,e.picture=t.image,e.role=t.role),e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190,5663,9404],()=>r(11046));module.exports=s})();