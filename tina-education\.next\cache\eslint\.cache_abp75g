[{"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\notifications\\route.ts": "1", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\notifications\\[id]\\route.ts": "2", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\[...nextauth]\\route.ts": "3", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\manuscripts\\route.ts": "4", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\messages\\route.ts": "5", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\submit\\route.ts": "7", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\update\\route.ts": "8", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\authSect.tsx": "9", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\DashboardStats.tsx": "10", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\RecentActivities.tsx": "11", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\Sidebar.tsx": "12", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\StatsCard.tsx": "13", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\footer.tsx": "14", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\greeting.tsx": "15", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home_nav.tsx": "16", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\manuscripts\\ManuscriptStats.tsx": "17", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationBell.tsx": "18", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationItem.tsx": "19", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationToast.tsx": "20", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\publications\\PublicationStats.tsx": "21", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\CommunicationPanel.tsx": "22", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ManuscriptViewer.tsx": "23", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewActions.tsx": "24", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewForm.tsx": "25", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewInterface.tsx": "26", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewProgress.tsx": "27", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewStats.tsx": "28", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\SessionNavWrapper.tsx": "29", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\submit.tsx": "30", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\context\\AuthProvider.tsx": "31", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\layout.tsx": "32", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\manuscripts\\page.tsx": "33", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\notifications\\page.tsx": "34", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\page.tsx": "35", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\publications\\page.tsx": "36", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\reviews\\page.tsx": "37", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx": "38", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\new\\page.tsx": "39", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\page.tsx": "40", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\review\\page.tsx": "41", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\reviews\\[id]\\review\\page.tsx": "42", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\lib\\pdf-service.ts": "43", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\analytics\\page.tsx": "44", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\calendar\\page.tsx": "45", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\collaborators\\page.tsx": "46", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\library\\page.tsx": "47", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\messages\\page.tsx": "48", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\settings\\page.tsx": "49", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\tools\\page.tsx": "50", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\articles\\route.ts": "51", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\books\\route.ts": "52", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\journals\\route.ts": "53", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\route.ts": "54", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\articles\\page.tsx": "55", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\books\\page.tsx": "56", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryCard.tsx": "57", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryFilters.tsx": "58", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryGrid.tsx": "59", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryStats.tsx": "60", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\journals\\page.tsx": "61", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\repository\\page.tsx": "62", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\repository\\[id]\\page.tsx": "63", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home\\FeaturedPublications.tsx": "64", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home\\PublicationCarousel.tsx": "65", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\upload\\route.ts": "66", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\FileUpload.tsx": "67", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\RichTextEditor.tsx": "68", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\genres\\route.ts": "69", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\manuscripts\\[id]\\route.ts": "70", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\publications\\route.ts": "71", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\publication\\[id]\\route.ts": "72", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\wishlist\\check\\route.ts": "73", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\wishlist\\route.ts": "74", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\auth\\signin\\page.tsx": "75", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\cart\\page.tsx": "76", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\AuthRequiredButton.tsx": "77", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\AuthRequiredLink.tsx": "78", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\RecentManuscripts.tsx": "79", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\ManuscriptEditForm.tsx": "80", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\quantitycounter.tsx": "81", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\GenreBreadcrumb.tsx": "82", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\hooks\\useAuthAction.ts": "83", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\[id]\\edit\\page.tsx": "84", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\[id]\\page.tsx": "85", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\publications\\new\\page.tsx": "86", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\wishlist\\page.tsx": "87", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\admin\\reviewer-applications\\[id]\\route.ts": "88", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\admin\\users\\[id]\\role\\route.ts": "89", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\signup\\route.ts": "90", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\profile\\route.ts": "91", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviewer-application\\route.ts": "92", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\auth\\signup\\page.tsx": "93", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\AdminStats.tsx": "94", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\ReviewerApplicationActions.tsx": "95", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\ReviewerApplications.tsx": "96", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\UserManagement.tsx": "97", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\UserRoleActions.tsx": "98", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\profile\\ProfileForm.tsx": "99", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviewer\\ReviewerApplicationForm.tsx": "100", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\UserDropdown.tsx": "101", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\admin\\page.tsx": "102", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\profile\\page.tsx": "103", "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\reviewer-application\\page.tsx": "104"}, {"size": 1103, "mtime": 1748180959328, "results": "105", "hashOfConfig": "106"}, {"size": 619, "mtime": 1748283076850, "results": "107", "hashOfConfig": "106"}, {"size": 120, "mtime": 1747603603883, "results": "108", "hashOfConfig": "106"}, {"size": 8348, "mtime": 1749858744491, "results": "109", "hashOfConfig": "106"}, {"size": 5278, "mtime": 1748203651740, "results": "110", "hashOfConfig": "106"}, {"size": 6707, "mtime": 1748282202172, "results": "111", "hashOfConfig": "106"}, {"size": 4169, "mtime": 1748203609558, "results": "112", "hashOfConfig": "106"}, {"size": 2232, "mtime": 1748203593682, "results": "113", "hashOfConfig": "106"}, {"size": 1152, "mtime": 1749858357458, "results": "114", "hashOfConfig": "106"}, {"size": 3163, "mtime": 1749980672697, "results": "115", "hashOfConfig": "106"}, {"size": 5788, "mtime": 1749981035138, "results": "116", "hashOfConfig": "106"}, {"size": 7023, "mtime": 1749980629915, "results": "117", "hashOfConfig": "106"}, {"size": 1817, "mtime": 1749980685621, "results": "118", "hashOfConfig": "106"}, {"size": 3149, "mtime": 1749829402724, "results": "119", "hashOfConfig": "106"}, {"size": 332, "mtime": 1746794280006, "results": "120", "hashOfConfig": "106"}, {"size": 3333, "mtime": 1749832559505, "results": "121", "hashOfConfig": "106"}, {"size": 3299, "mtime": 1748186800254, "results": "122", "hashOfConfig": "106"}, {"size": 2648, "mtime": 1748538806880, "results": "123", "hashOfConfig": "106"}, {"size": 3078, "mtime": 1749980810570, "results": "124", "hashOfConfig": "106"}, {"size": 4933, "mtime": 1748538848888, "results": "125", "hashOfConfig": "106"}, {"size": 2596, "mtime": 1748463351914, "results": "126", "hashOfConfig": "106"}, {"size": 6768, "mtime": 1748537471685, "results": "127", "hashOfConfig": "106"}, {"size": 11178, "mtime": 1749981126053, "results": "128", "hashOfConfig": "106"}, {"size": 5952, "mtime": 1748282348758, "results": "129", "hashOfConfig": "106"}, {"size": 14273, "mtime": 1749826672285, "results": "130", "hashOfConfig": "106"}, {"size": 7329, "mtime": 1749981574873, "results": "131", "hashOfConfig": "106"}, {"size": 9878, "mtime": 1749981110967, "results": "132", "hashOfConfig": "106"}, {"size": 3179, "mtime": 1748201497451, "results": "133", "hashOfConfig": "106"}, {"size": 127, "mtime": 1748282459797, "results": "134", "hashOfConfig": "106"}, {"size": 7933, "mtime": 1749858391003, "results": "135", "hashOfConfig": "106"}, {"size": 240, "mtime": 1746446871375, "results": "136", "hashOfConfig": "106"}, {"size": 431, "mtime": 1749979491897, "results": "137", "hashOfConfig": "106"}, {"size": 14783, "mtime": 1749981620259, "results": "138", "hashOfConfig": "106"}, {"size": 2914, "mtime": 1748282515392, "results": "139", "hashOfConfig": "106"}, {"size": 8296, "mtime": 1749979555758, "results": "140", "hashOfConfig": "106"}, {"size": 9876, "mtime": 1749697865213, "results": "141", "hashOfConfig": "106"}, {"size": 17707, "mtime": 1749981361169, "results": "142", "hashOfConfig": "106"}, {"size": 1075, "mtime": 1748184954283, "results": "143", "hashOfConfig": "106"}, {"size": 230, "mtime": 1748186053762, "results": "144", "hashOfConfig": "106"}, {"size": 9975, "mtime": 1748498710856, "results": "145", "hashOfConfig": "106"}, {"size": 40246, "mtime": 1748280532383, "results": "146", "hashOfConfig": "106"}, {"size": 2002, "mtime": 1748203693420, "results": "147", "hashOfConfig": "106"}, {"size": 14103, "mtime": 1749829275780, "results": "148", "hashOfConfig": "106"}, {"size": 9987, "mtime": 1748287254453, "results": "149", "hashOfConfig": "106"}, {"size": 13197, "mtime": 1748287395909, "results": "150", "hashOfConfig": "106"}, {"size": 10094, "mtime": 1748288088246, "results": "151", "hashOfConfig": "106"}, {"size": 12117, "mtime": 1749981683041, "results": "152", "hashOfConfig": "106"}, {"size": 8596, "mtime": 1749980994655, "results": "153", "hashOfConfig": "106"}, {"size": 13108, "mtime": 1748288161993, "results": "154", "hashOfConfig": "106"}, {"size": 11185, "mtime": 1749980934237, "results": "155", "hashOfConfig": "106"}, {"size": 2147, "mtime": 1748464710780, "results": "156", "hashOfConfig": "106"}, {"size": 2478, "mtime": 1749645342919, "results": "157", "hashOfConfig": "106"}, {"size": 2147, "mtime": 1748464736256, "results": "158", "hashOfConfig": "106"}, {"size": 2250, "mtime": 1748464787672, "results": "159", "hashOfConfig": "106"}, {"size": 482, "mtime": 1748460905271, "results": "160", "hashOfConfig": "106"}, {"size": 489, "mtime": 1748460894481, "results": "161", "hashOfConfig": "106"}, {"size": 4422, "mtime": 1749645385009, "results": "162", "hashOfConfig": "106"}, {"size": 7409, "mtime": 1749645622461, "results": "163", "hashOfConfig": "106"}, {"size": 7097, "mtime": 1749645650412, "results": "164", "hashOfConfig": "106"}, {"size": 5386, "mtime": 1749981535150, "results": "165", "hashOfConfig": "106"}, {"size": 490, "mtime": 1748460899776, "results": "166", "hashOfConfig": "106"}, {"size": 496, "mtime": 1748460910691, "results": "167", "hashOfConfig": "106"}, {"size": 16884, "mtime": 1749831130102, "results": "168", "hashOfConfig": "106"}, {"size": 3513, "mtime": 1748498330704, "results": "169", "hashOfConfig": "106"}, {"size": 10596, "mtime": 1749832410589, "results": "170", "hashOfConfig": "106"}, {"size": 1971, "mtime": 1748538069697, "results": "171", "hashOfConfig": "106"}, {"size": 5981, "mtime": 1749980844917, "results": "172", "hashOfConfig": "106"}, {"size": 5752, "mtime": 1748536670748, "results": "173", "hashOfConfig": "106"}, {"size": 993, "mtime": 1749645702122, "results": "174", "hashOfConfig": "106"}, {"size": 4607, "mtime": 1749830693182, "results": "175", "hashOfConfig": "106"}, {"size": 6989, "mtime": 1749702243569, "results": "176", "hashOfConfig": "106"}, {"size": 1015, "mtime": 1749832304324, "results": "177", "hashOfConfig": "106"}, {"size": 1465, "mtime": 1749687855821, "results": "178", "hashOfConfig": "106"}, {"size": 4898, "mtime": 1749830706851, "results": "179", "hashOfConfig": "106"}, {"size": 8049, "mtime": 1749858345053, "results": "180", "hashOfConfig": "106"}, {"size": 13848, "mtime": 1749692394747, "results": "181", "hashOfConfig": "106"}, {"size": 980, "mtime": 1749690089653, "results": "182", "hashOfConfig": "106"}, {"size": 1272, "mtime": 1749690168923, "results": "183", "hashOfConfig": "106"}, {"size": 5899, "mtime": 1749979674878, "results": "184", "hashOfConfig": "106"}, {"size": 7269, "mtime": 1749598102449, "results": "185", "hashOfConfig": "106"}, {"size": 873, "mtime": 1749684122931, "results": "186", "hashOfConfig": "106"}, {"size": 4001, "mtime": 1749829420933, "results": "187", "hashOfConfig": "106"}, {"size": 1252, "mtime": 1749690053748, "results": "188", "hashOfConfig": "106"}, {"size": 1312, "mtime": 1749598074751, "results": "189", "hashOfConfig": "106"}, {"size": 3273, "mtime": 1749598043772, "results": "190", "hashOfConfig": "106"}, {"size": 16113, "mtime": 1749831118444, "results": "191", "hashOfConfig": "106"}, {"size": 10468, "mtime": 1749829444114, "results": "192", "hashOfConfig": "106"}, {"size": 8917, "mtime": 1749854971259, "results": "193", "hashOfConfig": "106"}, {"size": 6431, "mtime": 1749858325619, "results": "194", "hashOfConfig": "106"}, {"size": 1589, "mtime": 1749854513690, "results": "195", "hashOfConfig": "106"}, {"size": 2944, "mtime": 1749854731694, "results": "196", "hashOfConfig": "106"}, {"size": 6245, "mtime": 1749854846670, "results": "197", "hashOfConfig": "106"}, {"size": 7367, "mtime": 1749854503082, "results": "198", "hashOfConfig": "106"}, {"size": 5724, "mtime": 1749980783423, "results": "199", "hashOfConfig": "106"}, {"size": 2912, "mtime": 1749854936927, "results": "200", "hashOfConfig": "106"}, {"size": 5471, "mtime": 1749980874391, "results": "201", "hashOfConfig": "106"}, {"size": 4026, "mtime": 1749854990091, "results": "202", "hashOfConfig": "106"}, {"size": 2668, "mtime": 1749855004080, "results": "203", "hashOfConfig": "106"}, {"size": 8010, "mtime": 1749854716897, "results": "204", "hashOfConfig": "106"}, {"size": 10620, "mtime": 1749858555840, "results": "205", "hashOfConfig": "106"}, {"size": 6485, "mtime": 1749857134111, "results": "206", "hashOfConfig": "106"}, {"size": 7689, "mtime": 1749858419119, "results": "207", "hashOfConfig": "106"}, {"size": 6768, "mtime": 1749979393434, "results": "208", "hashOfConfig": "106"}, {"size": 7528, "mtime": 1749858500292, "results": "209", "hashOfConfig": "106"}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ylx5vc", {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\notifications\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\manuscripts\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\messages\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\submit\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\update\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\authSect.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\DashboardStats.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\RecentActivities.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\StatsCard.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\greeting.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home_nav.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\manuscripts\\ManuscriptStats.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationBell.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationItem.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationToast.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\publications\\PublicationStats.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\CommunicationPanel.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ManuscriptViewer.tsx", ["522"], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewActions.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewForm.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewInterface.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewProgress.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviews\\ReviewStats.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\SessionNavWrapper.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\submit.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\context\\AuthProvider.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\manuscripts\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\notifications\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\publications\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\reviews\\page.tsx", ["523", "524"], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\review\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\reviews\\[id]\\review\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\lib\\pdf-service.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\calendar\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\collaborators\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\library\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\messages\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\tools\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\articles\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\books\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\journals\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\articles\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\books\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryCard.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryFilters.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryGrid.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryStats.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\journals\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\repository\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\repository\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home\\FeaturedPublications.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home\\PublicationCarousel.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\FileUpload.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\RichTextEditor.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\genres\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\manuscripts\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\publications\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\publication\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\wishlist\\check\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\wishlist\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\auth\\signin\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\cart\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\AuthRequiredButton.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\AuthRequiredLink.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\RecentManuscripts.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\ManuscriptEditForm.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\quantitycounter.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\GenreBreadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\hooks\\useAuthAction.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\[id]\\edit\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\manuscripts\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\publications\\new\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\wishlist\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\admin\\reviewer-applications\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\admin\\users\\[id]\\role\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\signup\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\profile\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviewer-application\\route.ts", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\auth\\signup\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\AdminStats.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\ReviewerApplicationActions.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\ReviewerApplications.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\UserManagement.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\UserRoleActions.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\profile\\ProfileForm.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\reviewer\\ReviewerApplicationForm.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\UserDropdown.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\reviewer-application\\page.tsx", [], [], {"ruleId": "525", "severity": 2, "message": "526", "line": 23, "column": 46, "nodeType": null, "messageId": "527", "endLine": 23, "endColumn": 56}, {"ruleId": "525", "severity": 2, "message": "528", "line": 19, "column": 3, "nodeType": null, "messageId": "527", "endLine": 19, "endColumn": 16}, {"ruleId": "525", "severity": 2, "message": "529", "line": 20, "column": 3, "nodeType": null, "messageId": "527", "endLine": 20, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'FaDownload' is defined but never used.", "unusedVar", "'FaCalendarAlt' is defined but never used.", "'FaEnvelope' is defined but never used."]