exports.id=7980,exports.ids=[7980],exports.modules={8704:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return a},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return n},isHTTPAccessFallbackError:function(){return l}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},s=new Set(Object.values(r)),a="NEXT_HTTP_ERROR_FALLBACK";function l(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===a&&s.has(Number(r))}function n(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31162:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return l}});let s=r(8704),a=r(49026);function l(e){return(0,a.isRedirectError)(e)||(0,s.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34638:(e,t,r)=>{Promise.resolve().then(r.bind(r,89454))},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return a},RedirectType:function(){return l},isRedirectError:function(){return n}});let s=r(52836),a="NEXT_REDIRECT";var l=function(e){return e.push="push",e.replace="replace",e}({});function n(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,l]=t,n=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===a&&("replace"===l||"push"===l)&&"string"==typeof n&&!isNaN(o)&&o in s.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51647:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(60687),a=r(43210),l=r(85814),n=r.n(l),o=r(30474);function i({publication:e}){let t=function(e){switch(e){case"BOOK":return"from-blue-500 to-blue-700";case"EBOOK":case"JOURNAL":return"from-blue-600 to-blue-800";case"AUDIOBOOK":case"ARTICLE":return"from-gray-600 to-gray-800";default:return"from-gray-500 to-gray-700"}}(e.type);return(0,s.jsx)(n(),{href:`/repository/${e.id}`,className:"group block",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 group-hover:-translate-y-1",children:[(0,s.jsx)("div",{className:`aspect-[3/4] bg-gradient-to-br ${t} relative`,children:e.cover?(0,s.jsx)(o.default,{src:e.cover,alt:e.title,fill:!0,className:"object-cover"}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("span",{className:"text-6xl text-white opacity-80",children:function(e){switch(e){case"BOOK":return"\uD83D\uDCDA";case"EBOOK":return"\uD83D\uDCBB";case"AUDIOBOOK":return"\uD83C\uDFA7";case"JOURNAL":return"\uD83D\uDCF0";default:return"\uD83D\uDCC4"}}(e.type)})})}),(0,s.jsxs)("div",{className:"p-4 space-y-2",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 text-sm leading-tight line-clamp-2 group-hover:text-blue-900 transition-colors",children:e.title}),(0,s.jsx)("p",{className:"text-blue-600 text-sm font-medium",children:e.user.name||"Unknown Author"}),e.genre&&(0,s.jsxs)("p",{className:"text-gray-500 text-xs",children:[e.genre.parent?`${e.genre.parent.name} > `:"",e.genre.name]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("span",{className:"text-lg font-bold text-gray-900",children:"Free"})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium ${function(e){switch(e){case"BOOK":case"EBOOK":case"JOURNAL":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}}(e.type)}`,children:"EBOOK"===e.type?"Digital":e.type.charAt(0)+e.type.slice(1).toLowerCase()}),e.content&&(0,s.jsx)("span",{className:"text-blue-600 text-xs",children:"+1 other format"})]})]})]})})}var c=r(69587);function d({filters:e,onFilterChange:t,loading:r}){let[l,n]=(0,a.useState)(!1),[o,i]=(0,a.useState)(e.search),d=(e,r)=>{t({sortBy:e,sortOrder:r})},u=()=>{i(""),t({search:"",sortBy:"createdAt",sortOrder:"desc",genre:""})};return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),t({search:o})},className:"mb-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(c.KSO,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",value:o,onChange:e=>i(e.target.value),placeholder:"Search publications, authors, keywords...",className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-gray-700 placeholder-gray-500",disabled:r}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center",children:(0,s.jsx)("button",{type:"submit",disabled:r,className:"mr-3 px-4 py-2 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors disabled:opacity-50",children:"Search"})})]})}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{onClick:()=>n(!l),className:"flex items-center space-x-2 px-3 py-2 border border-gray-300 text-gray-500 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,s.jsx)(c.YsJ,{className:"h-4 w-4 "}),(0,s.jsx)("span",{children:"Filters"})]}),(e.search||"createdAt"!==e.sortBy||"desc"!==e.sortOrder)&&(0,s.jsx)("button",{onClick:u,className:"px-3 py-2 text-sm text-gray-400 hover:text-gray-800 transition-colors",children:"Clear all filters"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-400",children:"Sort by:"}),(0,s.jsxs)("select",{value:`${e.sortBy}-${e.sortOrder}`,onChange:e=>{let[t,r]=e.target.value.split("-");d(t,r)},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-gray-700",disabled:r,children:[(0,s.jsx)("option",{value:"createdAt-desc",children:"Newest First"}),(0,s.jsx)("option",{value:"createdAt-asc",children:"Oldest First"}),(0,s.jsx)("option",{value:"title-asc",children:"Title A-Z"}),(0,s.jsx)("option",{value:"title-desc",children:"Title Z-A"})]})]})]}),l&&(0,s.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort Field"}),(0,s.jsxs)("select",{value:e.sortBy,onChange:t=>d(t.target.value,e.sortOrder),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-gray-700",disabled:r,children:[(0,s.jsx)("option",{value:"createdAt",children:"Date Created"}),(0,s.jsx)("option",{value:"updatedAt",children:"Date Updated"}),(0,s.jsx)("option",{value:"title",children:"Title"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Sort Order"}),(0,s.jsxs)("select",{value:e.sortOrder,onChange:t=>d(e.sortBy,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-gray-700",disabled:r,children:[(0,s.jsx)("option",{value:"desc",children:"Descending"}),(0,s.jsx)("option",{value:"asc",children:"Ascending"})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)("button",{onClick:u,className:"w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",disabled:r,children:"Reset Filters"})})]})}),(e.search||"createdAt"!==e.sortBy||"desc"!==e.sortOrder)&&(0,s.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-400",children:"Active filters:"}),e.search&&(0,s.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800",children:['Search: "',e.search,'"',(0,s.jsx)("button",{onClick:()=>{i(""),t({search:""})},className:"ml-2 text-blue-600 hover:text-blue-800",children:"\xd7"})]}),("createdAt"!==e.sortBy||"desc"!==e.sortOrder)&&(0,s.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800",children:["Sort: ",e.sortBy," (",e.sortOrder,")",(0,s.jsx)("button",{onClick:()=>d("createdAt","desc"),className:"ml-2 text-gray-400 hover:text-gray-800",children:"\xd7"})]})]})})]})}function u({apiEndpoint:e,title:t,description:r,emptyStateMessage:l,emptyStateIcon:n}){let[o,c]=(0,a.useState)([]),[u,p]=(0,a.useState)(null),[x,f]=(0,a.useState)(!0),[b,g]=(0,a.useState)(null),[m,h]=(0,a.useState)({search:"",sortBy:"createdAt",sortOrder:"desc",page:1,genre:""}),y=(0,a.useCallback)(async()=>{try{f(!0);let t=new URLSearchParams({search:m.search,sortBy:m.sortBy,sortOrder:m.sortOrder,page:m.page.toString(),limit:"12"});m.genre&&t.append("genre",m.genre);let r=await fetch(`${e}?${t}`);if(!r.ok)throw Error("Failed to fetch publications");let s=await r.json(),a=e.includes("books")?"books":e.includes("journals")?"journals":e.includes("articles")?"articles":"publications";c(s[a]||[]),p(s.pagination),g(null)}catch(e){g(e instanceof Error?e.message:"An error occurred"),c([])}finally{f(!1)}},[e,m]),j=e=>{h(t=>({...t,page:e})),window.scrollTo({top:0,behavior:"smooth"})};return x&&0===o.length?(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 pt-20",children:(0,s.jsx)("div",{className:"container mx-auto w-[90%] max-w-7xl py-8",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading publications..."})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 pt-20",children:(0,s.jsxs)("div",{className:"container mx-auto w-[90%] max-w-7xl py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:t}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:r}),u&&(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:["Showing ",u.totalCount," ",1===u.totalCount?"publication":"publications"]})]}),(0,s.jsx)(d,{filters:m,onFilterChange:e=>{h(t=>({...t,...e,page:1}))},loading:x}),b&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"❌"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Error Loading Publications"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:b}),(0,s.jsx)("button",{onClick:y,className:"px-6 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors",children:"Try Again"})]}),!x&&!b&&0===o.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:n}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Publications Found"}),(0,s.jsx)("p",{className:"text-gray-400",children:l})]}),!b&&o.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8",children:o.map(e=>(0,s.jsx)(i,{publication:e},e.id))}),u&&u.totalPages>1&&(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>j(u.currentPage-1),disabled:!u.hasPrevPage,className:"px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Previous"}),Array.from({length:u.totalPages},(e,t)=>t+1).map(e=>(0,s.jsx)("button",{onClick:()=>j(e),className:`px-4 py-2 border rounded-lg ${e===u.currentPage?"bg-blue-900 text-white border-blue-900":"border-gray-300 hover:bg-gray-50"}`,children:e},e)),(0,s.jsx)("button",{onClick:()=>j(u.currentPage+1),disabled:!u.hasNextPage,className:"px-4 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Next"})]})]})]})})}},51846:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return s},isBailoutToCSRError:function(){return a}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class s extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},52637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return s}});let r=Symbol.for("react.postpone");function s(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},52836:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&Object.prototype.hasOwnProperty.call(e,n)){var o=l?Object.getOwnPropertyDescriptor(e,n):null;o&&(o.get||o.set)?Object.defineProperty(s,n,o):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(61120));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let l={current:null},n="function"==typeof s.cache?s.cache:e=>e,o=console.warn;function i(e){return function(...t){o(e(...t))}}n(e=>{try{o(l.current)}finally{l.current=null}})},89454:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\repository\\\\RepositoryGrid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\repository\\RepositoryGrid.tsx","default")},97686:(e,t,r)=>{Promise.resolve().then(r.bind(r,51647))}};