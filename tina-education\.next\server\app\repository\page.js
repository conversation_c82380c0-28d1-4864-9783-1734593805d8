"use strict";(()=>{var e={};e.id=8955,e.ids=[8955],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33729:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>c,tree:()=>p});var o=t(65239),n=t(48088),i=t(88170),a=t.n(i),s=t(30893),d={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);t.d(r,d);let p={children:["",{children:["repository",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77012)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\repository\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\repository\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/repository/page",pathname:"/repository",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},33873:e=>{e.exports=require("path")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77012:(e,r,t)=>{t.r(r),t.d(r,{default:()=>i});var o=t(37413),n=t(89454);function i(){return(0,o.jsx)(n.default,{apiEndpoint:"/api/repository",title:"Academic Repository",description:"Explore our complete collection of academic publications including books, journals, and articles.",emptyStateMessage:"No publications found. Try adjusting your search criteria or check back later for new content.",emptyStateIcon:"\uD83D\uDCDA"})}},79551:e=>{e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4447,7945,1658,1795,7980],()=>t(33729));module.exports=o})();