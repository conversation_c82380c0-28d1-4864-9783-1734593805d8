(()=>{var e={};e.id=7314,e.ids=[7314],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma||new s.PrismaClient},6205:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>u});var n=t(96559),a=t(48088),i=t(37719),o=t(32190),p=t(5956);async function u(){try{let e=await p.z.genre.findMany({include:{parent:!0,children:!0},orderBy:[{parentId:"asc"},{name:"asc"}]}),r=e.filter(e=>!e.parentId),t=e.filter(e=>e.parentId),s=r.map(e=>({...e,children:t.filter(r=>r.parentId===e.id)}));return o.NextResponse.json({genres:s,allGenres:e})}catch(e){return console.error("Failed to fetch genres:",e),o.NextResponse.json({error:"Failed to fetch genres"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/genres/route",pathname:"/api/genres",filename:"route",bundlePath:"app/api/genres/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\genres\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:x}=d;function g(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,2190],()=>t(6205));module.exports=s})();