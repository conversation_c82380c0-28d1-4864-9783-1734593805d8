(()=>{var e={};e.id=4282,e.ids=[4282],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18995:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>u});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),p=r(5956);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("type"),s=t.get("search")||"",i=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"12"),n=t.get("sortBy")||"createdAt",u=t.get("sortOrder")||"desc",c={};r&&(c.type=r),s&&(c.OR=[{title:{contains:s,mode:"insensitive"}},{abstract:{contains:s,mode:"insensitive"}},{keywords:{contains:s,mode:"insensitive"}},{user:{name:{contains:s,mode:"insensitive"}}}]);let d=(i-1)*a,[l,g]=await Promise.all([p.z.publication.findMany({where:c,include:{user:{select:{name:!0,email:!0}}},orderBy:{[n]:u},skip:d,take:a}),p.z.publication.count({where:c})]),x=Math.ceil(g/a);return o.NextResponse.json({publications:l.map(e=>({...e,createdAt:e.createdAt.toISOString(),updatedAt:e.updatedAt.toISOString()})),pagination:{currentPage:i,totalPages:x,totalCount:g,hasNextPage:i<x,hasPrevPage:i>1,limit:a}})}catch(e){return console.error("Failed to fetch repository publications:",e),o.NextResponse.json({error:"Failed to fetch publications"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/repository/route",pathname:"/api/repository",filename:"route",bundlePath:"app/api/repository/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=c;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190],()=>r(18995));module.exports=s})();