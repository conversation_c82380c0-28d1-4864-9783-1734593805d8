(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3333],{739:(e,t,o)=>{"use strict";o.d(t,{A:()=>d});var a=o(5155),s=o(5109),r=o(8292),n=o(7360),l=o(3083);o(2115);var i=o(5323),c=o.n(i);let d=e=>{let{content:t,onChange:o,placeholder:i="Enter manuscript content...",className:d=""}=e,u=(0,s.hG)({extensions:[r.A.configure({heading:{levels:[1,2,3,4]}}),n.A.configure({placeholder:i}),l.A],content:t,onUpdate:e=>{let{editor:t}=e;o(t.getHTML())},editorProps:{attributes:{class:"focus:outline-none"}}});return u?(0,a.jsxs)("div",{className:"".concat(c().editor," ").concat(d),children:[(0,a.jsxs)("div",{className:c().toolbar,children:[(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleBold().run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("bold")?c().active:""),children:(0,a.jsx)("strong",{children:"B"})}),(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleItalic().run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("italic")?c().active:""),children:(0,a.jsx)("em",{children:"I"})}),(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleStrike().run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("strike")?c().active:""),children:(0,a.jsx)("s",{children:"S"})})]}),(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleHeading({level:1}).run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("heading",{level:1})?c().active:""),children:"H1"}),(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleHeading({level:2}).run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("heading",{level:2})?c().active:""),children:"H2"}),(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleHeading({level:3}).run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("heading",{level:3})?c().active:""),children:"H3"})]}),(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleBulletList().run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("bulletList")?c().active:""),children:"• List"}),(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleOrderedList().run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("orderedList")?c().active:""),children:"1. List"})]}),(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleBlockquote().run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("blockquote")?c().active:""),children:"Quote"}),(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().toggleCode().run(),className:"".concat(c().toolbarButton," ").concat(u.isActive("code")?c().active:""),children:"Code"})]}),(0,a.jsxs)("div",{className:c().toolbarGroup,children:[(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().setHorizontalRule().run(),className:c().toolbarButton,children:"HR"}),(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().undo().run(),disabled:!u.can().undo(),className:c().toolbarButton,children:"Undo"}),(0,a.jsx)("button",{type:"button",onClick:()=>u.chain().focus().redo().run(),disabled:!u.can().redo(),className:c().toolbarButton,children:"Redo"})]})]}),(0,a.jsx)("div",{className:c().editorContent,children:(0,a.jsx)(s.$Z,{editor:u})}),(0,a.jsxs)("div",{className:c().characterCount,children:[u.storage.characterCount.characters()," characters"]})]}):null}},3190:(e,t,o)=>{"use strict";o.d(t,{A:()=>n});var a=o(5155),s=o(2115),r=o(9911);let n=e=>{let{onFileUpload:t,onFileRemove:o,uploadedFile:n,disabled:l=!1}=e,[i,c]=(0,s.useState)(!1),[d,u]=(0,s.useState)(!1),[m,b]=(0,s.useState)(""),h=(0,s.useRef)(null),x=async e=>{if(e){if(!["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(e.type))return void b("Invalid file type. Only PDF and Word documents are allowed.");if(e.size>0xa00000)return void b("File size too large. Maximum size is 10MB.");b(""),c(!0);try{let o=new FormData;o.append("file",e);let a=await fetch("/api/upload",{method:"POST",body:o}),s=await a.json();if(!a.ok)throw Error(s.error||"Upload failed");t(s.url,s.filename)}catch(e){b(e instanceof Error?e.message:"Upload failed")}finally{c(!1)}}};return n?(0,a.jsx)("div",{className:"border border-gray-300 rounded-lg p-4 bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:(e=>{var t;let o=null==(t=e.split(".").pop())?void 0:t.toLowerCase();return"pdf"===o?(0,a.jsx)(r.kl1,{className:"text-red-500"}):"doc"===o||"docx"===o?(0,a.jsx)(r.WLb,{className:"text-blue-500"}):(0,a.jsx)(r.EHs,{className:"text-gray-500"})})(n.name)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:n.name}),(0,a.jsx)("a",{href:n.url,target:"_blank",rel:"noopener noreferrer",className:"text-xs text-blue-600 hover:text-blue-800 underline",children:"View file"})]})]}),(0,a.jsx)("button",{type:"button",onClick:()=>{o(),h.current&&(h.current.value="")},disabled:l,className:"text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center transition-colors ".concat(d?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"," ").concat(l?"opacity-50 cursor-not-allowed":"cursor-pointer"),onDrop:e=>{e.preventDefault(),u(!1);let t=e.dataTransfer.files[0];t&&x(t)},onDragOver:e=>{e.preventDefault(),u(!0)},onDragLeave:e=>{e.preventDefault(),u(!1)},onClick:()=>{var e;return!l&&(null==(e=h.current)?void 0:e.click())},children:[(0,a.jsx)("input",{ref:h,type:"file",accept:".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",onChange:e=>{var t;let o=null==(t=e.target.files)?void 0:t[0];o&&x(o)},className:"hidden",disabled:l}),i?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Uploading..."})]}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-4xl",children:"\uD83D\uDCCE"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Drop your file here or click to browse"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"PDF or Word documents only (max 10MB)"})]})]})]}),m&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:m})]})}},3301:(e,t,o)=>{Promise.resolve().then(o.bind(o,9079)),Promise.resolve().then(o.bind(o,5493))},5323:e=>{e.exports={editor:"RichTextEditor_editor__f90n2",toolbar:"RichTextEditor_toolbar__072Cb",toolbarGroup:"RichTextEditor_toolbarGroup__Pkt5f",toolbarButton:"RichTextEditor_toolbarButton__frh0r",active:"RichTextEditor_active__ir8Mh",editorContent:"RichTextEditor_editorContent__A1QHg",ProseMirror:"RichTextEditor_ProseMirror___JNXh","is-editor-empty":"RichTextEditor_is-editor-empty__OyISz",characterCount:"RichTextEditor_characterCount__c9x5l"}},9079:(e,t,o)=>{"use strict";o.d(t,{default:()=>d});var a=o(5155),s=o(2115),r=o(5493),n=o(6874),l=o.n(n),i=o(739),c=o(3190);function d(){let[e,t]=(0,s.useState)(""),[o,n]=(0,s.useState)(""),[d,u]=(0,s.useState)(""),[m,b]=(0,s.useState)(""),[h,x]=(0,s.useState)(null),[p,g]=(0,s.useState)(!1),[v,f]=(0,s.useState)(""),[y,j]=(0,s.useState)(""),{data:N}=(0,r.wV)();if(!N)return(0,a.jsx)("div",{className:"h-40",children:(0,a.jsx)("p",{className:"text-red-600 mb-8",children:"You must be logged in to submit a manuscript."})});let k=async function(a){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];a.preventDefault(),g(!0),f(""),j("");try{if(!(await fetch("/api/manuscripts",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:e,abstract:o,content:d,keywords:m,uploadedFile:null==h?void 0:h.url,uploadedFileName:null==h?void 0:h.name,isDraft:s})})).ok)throw Error(s?"Failed to save draft":"Submission failed");j(s?"Draft saved successfully!":"Manuscript submitted successfully!"),s||setTimeout(()=>{window.location.href="/dashboard/manuscripts"},1500),t(""),n(""),u(""),b(""),x(null)}catch(e){f(e instanceof Error?e.message:s?"Failed to save draft":"Submission error")}finally{g(!1)}},w={hidden_tag:()=>(0,a.jsx)("input",{type:"hidden",name:"csrf_token",value:"dummy_token"}),title:(0,a.jsx)("input",{type:"text",id:"title",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter manuscript title",value:e,onChange:e=>t(e.target.value),required:!0}),abstract:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("label",{htmlFor:"abstract",className:"block text-gray-700 text-sm font-bold mb-2",children:"Abstract"}),(0,a.jsx)("textarea",{id:"abstract",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter Abstract",value:o,onChange:e=>n(e.target.value),required:!0})]}),content:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("label",{htmlFor:"content",className:"block text-gray-700 text-sm font-bold mb-2",children:"Manuscript Content"}),(0,a.jsx)(i.A,{content:d,onChange:u,placeholder:"Enter your manuscript content here. Use the toolbar above to format your text with headings, lists, quotes, and more...",className:"w-full"})]}),keywords:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("label",{htmlFor:"keywords",className:"block text-gray-700 text-sm font-bold mb-2",children:"Keywords"}),(0,a.jsx)("input",{type:"text",id:"keywords",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500",placeholder:"Enter keywords (comma separated)",value:m,onChange:e=>b(e.target.value),required:!0})]}),submit:()=>(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:e=>k(e,!0),disabled:p,className:"bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50",children:p?"Saving...":"Save as Draft"}),(0,a.jsx)("button",{type:"submit",onClick:e=>k(e,!1),disabled:p,className:"bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50",children:p?"Submitting...":"Submit Manuscript"})]})};return(0,a.jsx)("div",{className:"bg-gray-100 min-h-screen py-6 mt-8",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-3xl bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mt-8 mb-4",children:[(0,a.jsxs)(l(),{href:"/dashboard",className:"text-indigo-600 hover:text-indigo-800 mb-4 inline-block",children:[(0,a.jsx)("i",{className:"mr-2",children:"<="}),"Back to Dashboard"]}),(0,a.jsxs)("form",{className:"mb-4",id:"manuscriptForm",method:"POST",onSubmit:e=>k(e,!1),children:[w.hidden_tag(),(0,a.jsx)("h1",{className:"text-center text-2xl font-bold text-gray-800 mb-6",children:"Manuscript Submission"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{htmlFor:"title",className:"block text-gray-700 text-sm font-bold mb-2",children:"Title"}),w.title]}),(0,a.jsx)("div",{className:"mb-4",children:w.abstract}),(0,a.jsx)("div",{className:"mb-4",children:w.content}),(0,a.jsx)("div",{className:"mb-4",children:w.keywords}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 text-sm font-bold mb-2",children:"Upload Manuscript File (Optional)"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"You can upload a PDF or Word document instead of or in addition to typing your content above."}),(0,a.jsx)(c.A,{onFileUpload:(e,t)=>x({url:e,name:t}),onFileRemove:()=>x(null),uploadedFile:h,disabled:p})]}),v&&(0,a.jsx)("div",{className:"text-red-600 mb-2",children:v}),y&&(0,a.jsx)("div",{className:"text-green-600 mb-2",children:y}),(0,a.jsxs)("div",{className:"flex items-center justify-end",children:[(0,a.jsx)(l(),{href:"/guidelines",className:"bg-transparent hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 border border-gray-300 rounded shadow-sm hover:border-transparent mr-2",children:"Submission Guidelines"}),w.submit()]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8060,6711,5004,277,6874,5493,1161,8441,1684,7358],()=>t(3301)),_N_E=e.O()}]);