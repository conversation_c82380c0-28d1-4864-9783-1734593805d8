(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{52:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(5155),l=r(6874),a=r.n(l),i=r(6766),n=r(2115),c=r(5493),o=r(4717);let d=[{id:"1",publicationId:"pub1",title:"The Silent Patient",author:"<PERSON>",type:"EBOOK",price:2500,quantity:1,cover:"/images/book-placeholder.jpg"},{id:"2",publicationId:"pub2",title:"Gone Girl",author:"<PERSON>",type:"BOOK",price:3e3,quantity:2,cover:"/images/book-placeholder.jpg"},{id:"3",publicationId:"pub3",title:"The Girl with the Dragon Tattoo",author:"<PERSON><PERSON><PERSON>",type:"AUDIOBOOK",price:3500,quantity:1,cover:"/images/book-placeholder.jpg"}];function x(){var e;let{data:t,status:r}=(0,c.wV)(),[l,x]=(0,n.useState)(d),h=(e,t)=>{t<1||x(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},u=e=>{x(t=>t.filter(t=>t.id!==e))},m=e=>{u(e)},p=e=>{switch(e){case"BOOK":return"Printed Copy";case"EBOOK":return"eBook";case"AUDIOBOOK":return"Audio book";default:return e}},b=l.reduce((e,t)=>e+t.price*t.quantity,0),j=500*!!l.some(e=>"BOOK"===e.type),f=b+j;return"loading"===r?(0,s.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-xl",children:"Loading..."})}):(null==t||null==(e=t.user)?void 0:e.id)?(0,s.jsxs)("div",{className:"min-h-screen bg-white mt-18",children:[(0,s.jsx)("div",{className:"w-full h-18 flex items-center justify-center",style:{backgroundColor:"#f0eded"},children:(0,s.jsxs)("nav",{className:"flex space-x-8",children:[(0,s.jsx)(a(),{href:"/books",className:"text-black text-xl hover:underline",children:"Books"}),(0,s.jsx)(a(),{href:"/authors",className:"text-black text-xl hover:underline",children:"Authors"}),(0,s.jsx)(a(),{href:"/journals",className:"text-black text-xl hover:underline",children:"Journals"})]})}),(0,s.jsx)("div",{className:"px-10 py-4 border-b border-blue-500",children:(0,s.jsxs)("nav",{className:"text-s text-black",children:[(0,s.jsx)(a(),{href:"/",className:"hover:underline",children:"Home"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)("span",{children:"Shopping Cart"})]})}),(0,s.jsxs)("div",{className:"px-10 py-10",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-black mb-8",children:"Shopping Cart"}),0===l.length?(0,s.jsxs)("div",{className:"text-center py-16",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDED2"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-black mb-4",children:"Your cart is empty"}),(0,s.jsx)("p",{className:"text-gray-700 mb-8",children:"Add some books to get started!"}),(0,s.jsx)(a(),{href:"/books",className:"px-8 py-3 text-white rounded-lg hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:"Continue Shopping"})]}):(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsx)("div",{className:"space-y-6",children:l.map(e=>(0,s.jsx)("div",{className:"border border-gray-200 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"flex gap-6",children:[(0,s.jsx)("div",{className:"w-24 h-36 flex-shrink-0",children:e.cover?(0,s.jsx)(i.default,{src:e.cover,alt:e.title,width:96,height:144,className:"w-full h-full object-cover border border-gray-300"}):(0,s.jsx)("div",{className:"w-full h-full bg-gray-200 border border-gray-300 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-500 text-xs",children:"No Cover"})})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-black mb-1",children:(0,s.jsx)(a(),{href:"/repository/".concat(e.publicationId),className:"hover:underline",children:e.title})}),(0,s.jsxs)("p",{className:"text-gray-700 mb-2",children:["By ",e.author]}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:p(e.type)})]}),(0,s.jsx)("button",{onClick:()=>u(e.id),className:"text-gray-400 hover:text-red-500 transition-colors",children:(0,s.jsx)(o.yRo,{size:20})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("span",{className:"text-gray-700",children:"Quantity:"}),(0,s.jsxs)("div",{className:"flex items-center bg-gray-300 border border-blue-300",children:[(0,s.jsx)("button",{onClick:()=>h(e.id,e.quantity-1),className:"w-8 h-6 flex items-center justify-center border-r border-blue-300 text-gray-800 hover:bg-gray-400",children:"-"}),(0,s.jsx)("div",{className:"w-10 h-6 text-sm flex items-center justify-center border-r border-blue-300 text-gray-800",children:e.quantity}),(0,s.jsx)("button",{onClick:()=>h(e.id,e.quantity+1),className:"w-8 h-6 text-sm flex items-center justify-center text-gray-800 hover:bg-gray-400",children:"+"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("button",{onClick:()=>m(e.id),className:"flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,s.jsx)(o.uoh,{size:16}),(0,s.jsx)("span",{className:"text-sm",children:"Move to Wishlist"})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-lg font-bold text-black",children:[(e.price*e.quantity).toLocaleString(),"frs"]}),e.quantity>1&&(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[e.price.toLocaleString(),"frs each"]})]})]})]})]})]})},e.id))}),(0,s.jsx)("div",{className:"mt-8",children:(0,s.jsx)(a(),{href:"/books",className:"text-blue-600 hover:underline text-lg",children:"← Continue Shopping"})})]}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6 sticky top-24",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-black mb-6",children:"Order Summary"}),(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between text-gray-700",children:[(0,s.jsxs)("span",{children:["Subtotal (",l.length," items)"]}),(0,s.jsxs)("span",{children:[b.toLocaleString(),"frs"]})]}),j>0&&(0,s.jsxs)("div",{className:"flex justify-between text-gray-700",children:[(0,s.jsx)("span",{children:"Shipping"}),(0,s.jsxs)("span",{children:[j.toLocaleString(),"frs"]})]}),(0,s.jsx)("hr",{className:"border-gray-300"}),(0,s.jsxs)("div",{className:"flex justify-between text-lg font-bold text-black",children:[(0,s.jsx)("span",{children:"Total"}),(0,s.jsxs)("span",{children:[f.toLocaleString(),"frs"]})]})]}),(0,s.jsx)("button",{className:"w-full py-3 text-white text-lg font-medium rounded-lg hover:opacity-90 transition-opacity mb-4",style:{backgroundColor:"#0c0a46"},children:"Proceed to Checkout"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Secure checkout with"}),(0,s.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,s.jsx)("span",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:"SSL"}),(0,s.jsx)("span",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:"256-bit"})]})]})]})})]})]})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-white mt-18",children:[(0,s.jsx)("div",{className:"w-full h-18 flex items-center justify-center",style:{backgroundColor:"#f0eded"},children:(0,s.jsxs)("nav",{className:"flex space-x-8",children:[(0,s.jsx)(a(),{href:"/books",className:"text-black text-xl hover:underline",children:"Books"}),(0,s.jsx)(a(),{href:"/authors",className:"text-black text-xl hover:underline",children:"Authors"}),(0,s.jsx)(a(),{href:"/journals",className:"text-black text-xl hover:underline",children:"Journals"})]})}),(0,s.jsx)("div",{className:"px-10 py-4 border-b border-blue-500",children:(0,s.jsxs)("nav",{className:"text-s text-black",children:[(0,s.jsx)(a(),{href:"/",className:"hover:underline",children:"Home"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)("span",{children:"Shopping Cart"})]})}),(0,s.jsx)("div",{className:"px-10 py-10",children:(0,s.jsxs)("div",{className:"text-center py-16",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD12"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-black mb-4",children:"Sign in to view your cart"}),(0,s.jsx)("p",{className:"text-gray-700 mb-8",children:"You need to be signed in to access your shopping cart."}),(0,s.jsx)(a(),{href:"/auth/signin?callbackUrl=/cart",className:"px-8 py-3 text-white rounded-lg hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:"Sign In"})]})})]})}},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return n}});let s=r(8229),l=r(8883),a=r(3063),i=s._(r(1193));function n(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=a.Image},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(2115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=s.createContext&&s.createContext(l),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var s,l,a;s=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(l))in s?Object.defineProperty(s,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):s[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(x,n({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,o({key:r},t.attr),e(t.child)))}(e.child))}function x(e){var t=t=>{var r,{attr:l,size:a,title:c}=e,d=function(e,t){if(null==e)return{};var r,s,l=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)r=a[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(l[r]=e[r])}return l}(e,i),x=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,l,d,{className:r,style:o(o({color:e.color||t.color},t.style),e.style),height:x,width:x,xmlns:"http://www.w3.org/2000/svg"}),c&&s.createElement("title",null,c),e.children)};return void 0!==a?s.createElement(a.Consumer,null,e=>t(e)):t(l)}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>l.a});var s=r(1469),l=r.n(s)},9456:(e,t,r)=>{Promise.resolve().then(r.bind(r,52))}},e=>{var t=t=>e(e.s=t);e.O(0,[4777,6874,3063,5493,8441,1684,7358],()=>t(9456)),_N_E=e.O()}]);