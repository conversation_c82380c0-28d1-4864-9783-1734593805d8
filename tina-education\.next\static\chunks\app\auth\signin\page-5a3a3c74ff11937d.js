(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{1568:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var r=a(5155),l=a(5493),t=a(5695),n=a(2115),i=a(6874),c=a.n(i);function d(){let e=(0,t.useSearchParams)().get("callbackUrl")||"/dashboard",[s,a]=(0,n.useState)(null),[i,d]=(0,n.useState)(!1),[o,m]=(0,n.useState)({email:"",password:""});(0,n.useEffect)(()=>{(async()=>{a(await (0,l.Z3)())})()},[]);let u=async s=>{s.preventDefault(),d(!0);try{await (0,l.Jv)("credentials",{email:o.email,password:o.password,callbackUrl:e})}catch(e){console.error("Sign in error:",e)}finally{d(!1)}},x=async()=>{d(!0);try{await (0,l.Jv)("google",{callbackUrl:e})}catch(e){console.error("Google sign in error:",e)}finally{d(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),"/dashboard"!==e&&(0,r.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"You need to sign in to continue with your action"})]}),(0,r.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,r.jsxs)("form",{className:"space-y-6",onSubmit:u,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:o.email,onChange:e=>m({...o,email:e.target.value}),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your email address"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:o.password,onChange:e=>m({...o,password:e.target.value}),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your password"})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:i,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:i?"Signing in...":"Sign in"})})]}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]})}),(null==s?void 0:s.google)&&(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("button",{onClick:x,disabled:i,className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,r.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,r.jsx)("span",{className:"ml-2",children:"Sign in with Google"})]})}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Don't have an account?"})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)(c(),{href:"/auth/signup",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Create a new account"})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)(c(),{href:"/",className:"text-sm text-blue-600 hover:text-blue-500",children:"← Back to home"})})]})})]})}function o(){return(0,r.jsx)(n.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:"Loading..."}),children:(0,r.jsx)(d,{})})}},5695:(e,s,a)=>{"use strict";var r=a(8999);a.o(r,"usePathname")&&a.d(s,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})},6708:(e,s,a)=>{Promise.resolve().then(a.bind(a,1568))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,5493,8441,1684,7358],()=>s(6708)),_N_E=e.O()}]);