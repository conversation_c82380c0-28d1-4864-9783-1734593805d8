(()=>{var e={};e.id=7123,e.ids=[7123],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52040:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{DELETE:()=>p,GET:()=>d,POST:()=>c});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),l=r(56814),u=r(5956);async function d(){let e=await (0,l.j2)();if(!e?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});try{let t=await u.z.wishlistItem.findMany({where:{userId:e.user.id},include:{publication:{include:{user:{select:{name:!0,email:!0}},genre:{include:{parent:!0}}}}},orderBy:{createdAt:"desc"}});return n.NextResponse.json({wishlistItems:t.map(e=>({...e,createdAt:e.createdAt.toISOString(),updatedAt:e.updatedAt.toISOString(),publication:{...e.publication,createdAt:e.publication.createdAt.toISOString(),updatedAt:e.publication.updatedAt.toISOString()}}))})}catch(e){return console.error("Failed to fetch wishlist:",e),n.NextResponse.json({error:"Failed to fetch wishlist"},{status:500})}}async function c(e){let t=await (0,l.j2)();if(!t?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});try{let{publicationId:r,selectedType:s}=await e.json();if(!r||!s)return n.NextResponse.json({error:"Publication ID and selected type are required"},{status:400});if(!await u.z.publication.findUnique({where:{id:r}}))return n.NextResponse.json({error:"Publication not found"},{status:404});if(await u.z.wishlistItem.findUnique({where:{userId_publicationId_selectedType:{userId:t.user.id,publicationId:r,selectedType:s}}}))return n.NextResponse.json({error:"Item already in wishlist"},{status:409});let a=await u.z.wishlistItem.create({data:{userId:t.user.id,publicationId:r,selectedType:s},include:{publication:{include:{user:{select:{name:!0,email:!0}}}}}});return n.NextResponse.json({...a,createdAt:a.createdAt.toISOString(),updatedAt:a.updatedAt.toISOString(),publication:{...a.publication,createdAt:a.publication.createdAt.toISOString(),updatedAt:a.publication.updatedAt.toISOString()}})}catch(e){return console.error("Failed to add to wishlist:",e),n.NextResponse.json({error:"Failed to add to wishlist"},{status:500})}}async function p(e){let t=await (0,l.j2)();if(!t?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});try{let{searchParams:r}=new URL(e.url),s=r.get("publicationId"),a=r.get("selectedType");if(!s||!a)return n.NextResponse.json({error:"Publication ID and selected type are required"},{status:400});let i=await u.z.wishlistItem.delete({where:{userId_publicationId_selectedType:{userId:t.user.id,publicationId:s,selectedType:a}}});return n.NextResponse.json({message:"Item removed from wishlist",deletedItem:{...i,createdAt:i.createdAt.toISOString(),updatedAt:i.updatedAt.toISOString()}})}catch(e){return console.error("Failed to remove from wishlist:",e),n.NextResponse.json({error:"Failed to remove from wishlist"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/wishlist/route",pathname:"/api/wishlist",filename:"route",bundlePath:"app/api/wishlist/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\wishlist\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:w,serverHooks:g}=m;function b(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:w})}},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>u,j2:()=>p});var s=r(19443),a=r(16467),i=r(5956),o=r(10189),n=r(56056),l=r(85663);let{handlers:u,signIn:d,signOut:c,auth:p}=(0,s.Ay)({adapter:(0,a.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[n.A,(0,o.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!t||!t.password||!await l.Ay.compare(e.password,t.password))return null;return{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,s=t.pathname.startsWith("/dashboard"),a=t.pathname.startsWith("/manuscripts"),i=t.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",t.pathname),!s&&!a&&!i||r},session:async({session:e,token:t})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",t),t?.sub&&(e.user.id=t.sub,t.name&&(e.user.name=t.name),t.email&&(e.user.email=t.email),t.picture&&(e.user.image=t.picture),t.role&&(e.user.role=t.role)),e),jwt:async({token:e,user:t,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",t),console.log("\uD83D\uDD10 JWT callback - account:",r),t&&(e.sub=t.id,e.name=t.name,e.email=t.email,e.picture=t.image,e.role=t.role),e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190,5663,9404],()=>r(52040));module.exports=s})();