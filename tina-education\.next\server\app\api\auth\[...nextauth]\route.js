(()=>{var e={};e.id=1014,e.ids=[1014],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,r,t)=>{"use strict";t.d(r,{z:()=>s});var a=t(96330);let s=globalThis.prisma||new a.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23712:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>d,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{GET:()=>n,POST:()=>l});var s=t(96559),i=t(48088),o=t(37719);let{GET:n,POST:l}=t(56814).Y9,u=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:c,workUnitAsyncStorage:p,serverHooks:d}=u;function m(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:p})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>u,j2:()=>d});var a=t(19443),s=t(16467),i=t(5956),o=t(10189),n=t(56056),l=t(85663);let{handlers:u,signIn:c,signOut:p,auth:d}=(0,a.Ay)({adapter:(0,s.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[n.A,(0,o.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!r||!r.password||!await l.Ay.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,image:r.image,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,a=r.pathname.startsWith("/dashboard"),s=r.pathname.startsWith("/manuscripts"),i=r.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",t),console.log("\uD83D\uDD10 Authorized callback - pathname:",r.pathname),!a&&!s&&!i||t},session:async({session:e,token:r})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",r),r?.sub&&(e.user.id=r.sub,r.name&&(e.user.name=r.name),r.email&&(e.user.email=r.email),r.picture&&(e.user.image=r.picture),r.role&&(e.user.role=r.role)),e),jwt:async({token:e,user:r,account:t})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",r),console.log("\uD83D\uDD10 JWT callback - account:",t),r&&(e.sub=r.id,e.name=r.name,e.email=r.email,e.picture=r.image,e.role=r.role),e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,2190,5663,9404],()=>t(23712));module.exports=a})();