"use strict";(()=>{var e={};e.id=5292,e.ids=[5292],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5087:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});var a=t(37413),i=t(89454);function n(){return(0,a.jsx)(i.default,{apiEndpoint:"/api/repository/articles",title:"Articles Repository",description:"Discover our extensive collection of academic articles and research papers.",emptyStateMessage:"No articles found. Try adjusting your search criteria or check back later for new publications.",emptyStateIcon:"\uD83D\uDCC4"})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},41105:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>p});var a=t(65239),i=t(48088),n=t(88170),o=t.n(n),s=t(30893),d={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);t.d(r,d);let p={children:["",{children:["articles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5087)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\articles\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\articles\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/articles/page",pathname:"/articles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,7945,1658,1795,7980],()=>t(41105));module.exports=a})();