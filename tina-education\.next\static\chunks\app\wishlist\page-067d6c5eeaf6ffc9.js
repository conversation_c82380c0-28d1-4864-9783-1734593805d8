(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1720],{1090:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(5155),l=r(6874),a=r.n(l),i=r(6766),n=r(2115),c=r(5493),o=r(4717),d=r(9911);function u(){var e;let{data:t,status:r}=(0,c.wV)(),[l,u]=(0,n.useState)([]),[x,h]=(0,n.useState)(!0);(0,n.useEffect)(()=>{!async function(){var e;if("loading"!==r){if(!(null==t||null==(e=t.user)?void 0:e.id))return h(!1);try{let e=await fetch("/api/wishlist");if(e.ok){let t=await e.j<PERSON>();u(t.wishlistItems)}}catch(e){console.error("Failed to fetch wishlist:",e)}finally{h(!1)}}}()},[t,r]);let m=async(e,t)=>{try{(await fetch("/api/wishlist?publicationId=".concat(e,"&selectedType=").concat(t),{method:"DELETE"})).ok&&u(r=>r.filter(r=>r.publication.id!==e||r.selectedType!==t))}catch(e){console.error("Failed to remove from wishlist:",e)}},p=e=>{switch(e){case"BOOK":return"Printed Copy";case"EBOOK":return"eBook";case"AUDIOBOOK":return"Audio book";case"JOURNAL":return"Journal";case"ARTICLE":return"Article";default:return e}};return x||"loading"===r?(0,s.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-xl",children:"Loading..."})}):(null==t||null==(e=t.user)?void 0:e.id)?(0,s.jsxs)("div",{className:"min-h-screen bg-white mt-18",children:[(0,s.jsx)("div",{className:"w-full h-18 flex items-center justify-center",style:{backgroundColor:"#f0eded"},children:(0,s.jsxs)("nav",{className:"flex space-x-8",children:[(0,s.jsx)(a(),{href:"/books",className:"text-black text-xl hover:underline",children:"Books"}),(0,s.jsx)(a(),{href:"/authors",className:"text-black text-xl hover:underline",children:"Authors"}),(0,s.jsx)(a(),{href:"/journals",className:"text-black text-xl hover:underline",children:"Journals"})]})}),(0,s.jsx)("div",{className:"px-10 py-4 border-b border-blue-500",children:(0,s.jsxs)("nav",{className:"text-s text-black",children:[(0,s.jsx)(a(),{href:"/",className:"hover:underline",children:"Home"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)("span",{children:"Wishlist"})]})}),(0,s.jsxs)("div",{className:"px-10 py-10",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-8",children:[(0,s.jsx)(d.Mbv,{className:"text-red-500 text-3xl"}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-black",children:"My Wishlist"}),(0,s.jsxs)("span",{className:"text-gray-600 text-xl",children:["(",l.length," items)"]})]}),0===l.length?(0,s.jsxs)("div",{className:"text-center py-16",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC9D"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-black mb-4",children:"Your wishlist is empty"}),(0,s.jsx)("p",{className:"text-gray-700 mb-8",children:"Save books you're interested in for later!"}),(0,s.jsx)(a(),{href:"/books",className:"px-8 py-3 text-white rounded-lg hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:"Browse Books"})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:l.map(e=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,s.jsx)("div",{className:"w-full aspect-[3/4] mb-4",children:e.publication.cover?(0,s.jsx)(i.default,{src:e.publication.cover,alt:e.publication.title,width:200,height:267,className:"w-full h-full object-cover border border-gray-300 rounded"}):(0,s.jsx)("div",{className:"w-full h-full bg-gray-200 border border-gray-300 rounded flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-gray-500 text-sm",children:"No Cover"})})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-black line-clamp-2",children:(0,s.jsx)(a(),{href:"/repository/".concat(e.publication.id),className:"hover:underline",children:e.publication.title})}),(0,s.jsxs)("p",{className:"text-gray-700 text-sm",children:["By ",e.publication.user.name]}),e.publication.genre&&(0,s.jsx)("p",{className:"text-gray-600 text-xs",children:e.publication.genre.name}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded",children:p(e.selectedType)})}),e.publication.abstract&&(0,s.jsx)("p",{className:"text-gray-600 text-sm line-clamp-3",children:e.publication.abstract})]}),(0,s.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,s.jsxs)(a(),{href:"/repository/".concat(e.publication.id),className:"flex-1 px-3 py-2 text-white text-sm text-center rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:[(0,s.jsx)(o.Wxq,{className:"inline mr-1"}),"View Details"]}),(0,s.jsx)("button",{onClick:()=>m(e.publication.id,e.selectedType),className:"px-3 py-2 text-red-500 border border-red-300 rounded hover:bg-red-50 transition-colors",title:"Remove from wishlist",children:(0,s.jsx)(o.yRo,{})})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Added ",new Date(e.createdAt).toLocaleDateString()]})]},e.id))}),l.length>0&&(0,s.jsx)("div",{className:"mt-12 text-center",children:(0,s.jsx)(a(),{href:"/books",className:"text-blue-600 hover:underline text-lg",children:"← Continue Shopping"})})]})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-white mt-18",children:[(0,s.jsx)("div",{className:"w-full h-18 flex items-center justify-center",style:{backgroundColor:"#f0eded"},children:(0,s.jsxs)("nav",{className:"flex space-x-8",children:[(0,s.jsx)(a(),{href:"/books",className:"text-black text-xl hover:underline",children:"Books"}),(0,s.jsx)(a(),{href:"/authors",className:"text-black text-xl hover:underline",children:"Authors"}),(0,s.jsx)(a(),{href:"/journals",className:"text-black text-xl hover:underline",children:"Journals"})]})}),(0,s.jsx)("div",{className:"px-10 py-4 border-b border-blue-500",children:(0,s.jsxs)("nav",{className:"text-s text-black",children:[(0,s.jsx)(a(),{href:"/",className:"hover:underline",children:"Home"}),(0,s.jsx)("span",{className:"mx-2",children:"/"}),(0,s.jsx)("span",{children:"Wishlist"})]})}),(0,s.jsx)("div",{className:"px-10 py-10",children:(0,s.jsxs)("div",{className:"text-center py-16",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD12"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-black mb-4",children:"Sign in to view your wishlist"}),(0,s.jsx)("p",{className:"text-gray-700 mb-8",children:"You need to be signed in to access your wishlist."}),(0,s.jsx)(a(),{href:"/auth/signin?callbackUrl=/wishlist",className:"px-8 py-3 text-white rounded-lg hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:"Sign In"})]})})]})}},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return n}});let s=r(8229),l=r(8883),a=r(3063),i=s._(r(1193));function n(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=a.Image},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(2115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=s.createContext&&s.createContext(l),i=["attr","size","title"];function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var s,l,a;s=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(l))in s?Object.defineProperty(s,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):s[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(u,n({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,o({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:l,size:a,title:c}=e,d=function(e,t){if(null==e)return{};var r,s,l=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)r=a[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(l[r]=e[r])}return l}(e,i),u=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,l,d,{className:r,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&s.createElement("title",null,c),e.children)};return void 0!==a?s.createElement(a.Consumer,null,e=>t(e)):t(l)}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>l.a});var s=r(1469),l=r.n(s)},9909:(e,t,r)=>{Promise.resolve().then(r.bind(r,1090))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,4777,6874,3063,5493,8441,1684,7358],()=>t(9909)),_N_E=e.O()}]);