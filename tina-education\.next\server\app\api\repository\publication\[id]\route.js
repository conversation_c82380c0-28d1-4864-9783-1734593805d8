(()=>{var e={};e.id=9221,e.ids=[9221],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var i=r(96330);let s=globalThis.prisma||new i.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13417:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var i={};r.r(i),r.d(i,{GET:()=>p});var s=r(96559),o=r(48088),n=r(37719),a=r(32190),u=r(5956);async function p(e,{params:t}){try{let{id:e}=await t,r=await u.z.publication.findUnique({where:{id:e},include:{user:{select:{name:!0,email:!0}},genre:{include:{parent:!0}}}});if(!r)return a.NextResponse.json({error:"Publication not found"},{status:404});return a.NextResponse.json({...r,createdAt:r.createdAt.toISOString(),updatedAt:r.updatedAt.toISOString()})}catch(e){return console.error("Failed to fetch publication:",e),a.NextResponse.json({error:"Failed to fetch publication"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/repository/publication/[id]/route",pathname:"/api/repository/publication/[id]",filename:"route",bundlePath:"app/api/repository/publication/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\repository\\publication\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function g(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,2190],()=>r(13417));module.exports=i})();