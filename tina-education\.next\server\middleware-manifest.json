{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "r5k4pXVgkH-KQLv4O3mVT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eOUI6eFkLi7awXKkaWJNDCWgS4nX9bSTnNOmyf/aaa8=", "__NEXT_PREVIEW_MODE_ID": "f39ba3106f3c5a6c8546eb8be659a572", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c2c133b8e0dfed154dea043d1e1fd2edb411c29fb59faeeec128d94916cc6b38", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "aebc705f8f537d48d6d3b280f09826229f1cb22ae75a98105b7911a0d2c5b718"}}}, "functions": {}, "sortedMiddleware": ["/"]}