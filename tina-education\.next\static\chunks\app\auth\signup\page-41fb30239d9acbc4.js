(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2778],{1990:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var r=a(5155),t=a(2115),n=a(5695),l=a(6874),o=a.n(l);function d(){let e=(0,n.useRouter)(),[s,a]=(0,t.useState)(!1),[l,d]=(0,t.useState)(""),[c,i]=(0,t.useState)({name:"",email:"",password:"",confirmPassword:""}),m=async s=>{if(s.preventDefault(),a(!0),d(""),c.password!==c.confirmPassword){d("Passwords do not match"),a(!1);return}if(c.password.length<6){d("Password must be at least 6 characters long"),a(!1);return}try{let s=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:c.name,email:c.email,password:c.password})}),a=await s.json();if(!s.ok)throw Error(a.error||"Failed to create account");e.push("/auth/signin?message=Account created successfully. Please sign in.")}catch(e){d(e instanceof Error?e.message:"An error occurred")}finally{a(!1)}},u=e=>{i({...c,[e.target.name]:e.target.value})};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),(0,r.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Join Tina Education to submit manuscripts and participate in peer review"})]}),(0,r.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,r.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,r.jsxs)("form",{className:"space-y-6",onSubmit:m,children:[l&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:l}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"name",name:"name",type:"text",required:!0,value:c.name,onChange:u,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your full name"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:c.email,onChange:u,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your email address"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:c.password,onChange:u,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your password"})}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Password must be at least 6 characters long"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:c.confirmPassword,onChange:u,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Confirm your password"})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:s,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-900 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Creating Account...":"Create Account"})})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Already have an account?"})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)(o(),{href:"/auth/signin",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Sign in to your account"})})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)(o(),{href:"/",className:"text-sm text-blue-600 hover:text-blue-500",children:"← Back to home"})})]})})]})}},5695:(e,s,a)=>{"use strict";var r=a(8999);a.o(r,"usePathname")&&a.d(s,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})},6390:(e,s,a)=>{Promise.resolve().then(a.bind(a,1990))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,8441,1684,7358],()=>s(6390)),_N_E=e.O()}]);