"use strict";(()=>{var e={};e.id=2323,e.ids=[2323],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65168:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var a={};t.r(a),t.d(a,{PATCH:()=>u});var s=t(96559),o=t(48088),i=t(37719),n=t(32190),d=t(56814),l=t(5956);let p=new(t(20549)).u(process.env.RESEND_API_KEY);async function u(e,{params:r}){try{let t=await (0,d.j2)();if(!t?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let a=await l.z.user.findUnique({where:{id:t.user.id},select:{role:!0,name:!0}});if(a?.role!=="ADMIN")return n.NextResponse.json({error:"Admin access required"},{status:403});let{id:s}=await r,{role:o}=await e.json();if(!["USER","REVIEWER","ADMIN"].includes(o))return n.NextResponse.json({error:"Invalid role"},{status:400});let i=await l.z.user.findUnique({where:{id:s},select:{id:!0,name:!0,email:!0,role:!0}});if(!i)return n.NextResponse.json({error:"User not found"},{status:404});if(i.id===t.user.id)return n.NextResponse.json({error:"Cannot change your own role"},{status:400});let u=await l.z.user.update({where:{id:s},data:{role:o},select:{id:!0,name:!0,email:!0,role:!0}});if(await l.z.notification.create({data:{userId:i.id,title:`Role Updated to ${o}`,message:`Your account role has been updated to ${o.toLowerCase()} by an administrator.`,type:"ROLE_UPDATE",relatedId:i.id}}),process.env.RESEND_API_KEY)try{let e=`
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1e40af; margin: 0;">🔄 Account Role Updated</h2>
            </div>
            
            <p>Dear ${i.name||"User"},</p>
            
            <p>Your account role has been updated by an administrator.</p>
            
            <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <h3 style="margin: 0 0 10px 0; color: #374151;">Role Change Details:</h3>
              <p style="margin: 5px 0;"><strong>Previous Role:</strong> ${i.role}</p>
              <p style="margin: 5px 0;"><strong>New Role:</strong> ${o}</p>
              <p style="margin: 5px 0;"><strong>Updated By:</strong> ${a.name||"Administrator"}</p>
            </div>
            
            ${"REVIEWER"===o?`
              <div style="background-color: #f0f9ff; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #1e40af;">Reviewer Access Granted:</h3>
                <ul style="margin: 0; padding-left: 20px; color: #1e40af;">
                  <li>You can now review manuscripts</li>
                  <li>Access the reviewer dashboard</li>
                  <li>Receive review assignments</li>
                </ul>
              </div>
            `:"ADMIN"===o?`
              <div style="background-color: #fef2f2; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #dc2626;">Administrator Access Granted:</h3>
                <ul style="margin: 0; padding-left: 20px; color: #dc2626;">
                  <li>Full system administration access</li>
                  <li>User and role management</li>
                  <li>Review application approval</li>
                  <li>System statistics and monitoring</li>
                </ul>
              </div>
            `:`
              <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #374151;">Standard User Access:</h3>
                <ul style="margin: 0; padding-left: 20px; color: #374151;">
                  <li>Submit manuscripts for review</li>
                  <li>Access personal dashboard</li>
                  <li>Manage publications</li>
                </ul>
              </div>
            `}
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXTAUTH_URL||"http://localhost:3000"}/dashboard"
                 style="background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Access Dashboard
              </a>
            </div>
            
            <p>If you have any questions about this change, please contact the administrator.</p>
            
            <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
            <p style="color: #64748b; font-size: 14px;">
              Best regards,<br>
              Tina Education Team<br>
              <small>This is an automated notification.</small>
            </p>
          </div>
        `;await p.emails.send({from:"Tina Education <<EMAIL>>",to:i.email,subject:`Account Role Updated to ${o} - Tina Education`,html:e}),console.log(`✅ Role change email sent to: ${i.email}`)}catch(e){console.error("Failed to send role change email:",e)}return n.NextResponse.json({message:`User role updated to ${o} successfully`,user:u})}catch(e){return console.error("Failed to update user role:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/users/[id]/role/route",pathname:"/api/admin/users/[id]/role",filename:"route",bundlePath:"app/api/admin/users/[id]/role/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\admin\\users\\[id]\\role\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:g}=c;function f(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")},96330:e=>{e.exports=require("@prisma/client")},96559:(e,r,t)=>{e.exports=t(44870)}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,2190,5663,9404,9468],()=>t(65168));module.exports=a})();