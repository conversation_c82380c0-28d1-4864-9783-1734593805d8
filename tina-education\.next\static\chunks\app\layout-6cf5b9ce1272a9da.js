(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{1:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(5155),a=r(5493);function n(e){let{children:t}=e;return(0,s.jsx)(a.<PERSON>,{children:t})}},344:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var s=r(5155),a=r(6874),n=r.n(a),l=r(5493),i=r(5695),o=r(9911),c=r(2115);function d(){var e,t;let{data:r,status:a}=(0,l.wV)(),[i,d]=(0,c.useState)(0);return((0,c.useEffect)(()=>{var e;if("loading"===a||!(null==r||null==(e=r.user)?void 0:e.id))return;let t=async()=>{try{let e=await fetch("/api/auth/notifications",{headers:{"Cache-Control":"no-cache"}});if(e.ok){let t=await e.json();d(t.unreadCount||0)}else 401===e.status?d(0):console.warn("Failed to fetch notification count:",e.status,e.statusText)}catch(e){if(e instanceof TypeError&&e.message.includes("fetch"))return;console.error("Failed to fetch notification count:",e)}},s=setTimeout(t,100),n=setInterval(t,3e4);return()=>{clearTimeout(s),clearInterval(n)}},[null==r||null==(e=r.user)?void 0:e.id,a]),"loading"===a)?(0,s.jsx)("div",{className:"w-6 h-6 animate-pulse bg-gray-200 rounded"}):"unauthenticated"!==a&&(null==r||null==(t=r.user)?void 0:t.id)?(0,s.jsxs)(n(),{href:"/dashboard/notifications",className:"relative",children:[(0,s.jsx)(o.jNV,{size:24,className:"text-gray-600 hover:text-blue-600 transition-colors"}),i>0&&(0,s.jsx)("span",{className:"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse",children:i>99?"99+":i})]}):null}var u=r(6766);function m(e){let{user:t}=e,[r,a]=(0,c.useState)(!1),i=(0,c.useRef)(null);(0,c.useEffect)(()=>{function e(e){i.current&&!i.current.contains(e.target)&&a(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let d=e=>e?e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2):"U";return(0,s.jsxs)("div",{className:"relative",ref:i,children:[(0,s.jsxs)("button",{onClick:()=>a(!r),className:"flex items-center space-x-2 p-1 rounded-full hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2","aria-label":"User menu",children:[(0,s.jsx)("div",{className:"w-9 h-9 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center overflow-hidden border-2 border-white shadow-sm",children:t.image?(0,s.jsx)(u.default,{src:t.image,alt:t.name||"User",width:36,height:36,className:"w-full h-full object-cover"}):(0,s.jsx)("span",{className:"text-sm font-medium text-white",children:d(t.name)})}),(0,s.jsx)(o.Vr3,{className:"w-3 h-3 text-gray-500 transition-transform duration-200 hidden sm:block ".concat(r?"rotate-180":"")})]}),r&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-64 sm:w-72 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 max-w-[calc(100vw-2rem)] sm:max-w-none animate-in fade-in-0 zoom-in-95 duration-100",children:[(0,s.jsx)("div",{className:"px-4 py-3 border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden",children:t.image?(0,s.jsx)(u.default,{src:t.image,alt:t.name||"User",width:40,height:40,className:"w-full h-full object-cover"}):(0,s.jsx)("span",{className:"text-lg font-medium text-gray-600",children:d(t.name)})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:t.name||"User"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 truncate",children:t.email}),t.role&&(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1",children:t.role.toLowerCase()})]})]})}),(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsxs)(n(),{href:"/dashboard",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>a(!1),children:[(0,s.jsx)(o.$BV,{className:"w-4 h-4 mr-3 text-gray-400"}),"Dashboard"]}),(0,s.jsxs)(n(),{href:"/profile",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>a(!1),children:[(0,s.jsx)(o.x$1,{className:"w-4 h-4 mr-3 text-gray-400"}),"Profile"]}),"REVIEWER"===t.role&&(0,s.jsxs)(n(),{href:"/dashboard/reviews",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>a(!1),children:[(0,s.jsx)(o.hko,{className:"w-4 h-4 mr-3 text-gray-400"}),"My Reviews"]}),"ADMIN"===t.role&&(0,s.jsxs)(n(),{href:"/dashboard/admin",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>a(!1),children:[(0,s.jsx)(o.e7y,{className:"w-4 h-4 mr-3 text-gray-400"}),"Admin Panel"]}),(0,s.jsxs)(n(),{href:"/dashboard/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>a(!1),children:[(0,s.jsx)(o.Pcn,{className:"w-4 h-4 mr-3 text-gray-400"}),"Settings"]})]}),(0,s.jsx)("div",{className:"border-t border-gray-200 my-1"}),(0,s.jsxs)("button",{onClick:()=>{(0,l.CI)({callbackUrl:"/"})},className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",children:[(0,s.jsx)(o.axc,{className:"w-4 h-4 mr-3"}),"Sign Out"]})]})]})}function h(){let{data:e}=(0,l.wV)(),t=(0,i.usePathname)(),r=(0,i.useSearchParams)(),a=()=>{let e=r.toString();return e?"".concat(t,"?").concat(e):t};return(0,s.jsxs)("div",{className:"flex justify-center items-center gap-5 relative text-gray-400",children:[!e&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("button",{className:"px-5 py-2 border border-black rounded text-gray-800 hover:bg-black hover:text-white hover:bg-opacity-10 transition-colors",onClick:()=>(0,l.Jv)(void 0,{callbackUrl:a()}),children:"Sign In"})}),e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d,{}),(0,s.jsx)(m,{user:e.user})]})]})}function f(){let[e,t]=(0,c.useState)(!1),{data:r}=(0,l.wV)(),a=(0,i.usePathname)(),d=r&&"/"===a;return(0,s.jsx)("header",{className:"fixed top-0 w-full bg-white shadow-md z-50",children:(0,s.jsx)("div",{className:"container mx-auto w-[90%] max-w-7xl",children:(0,s.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,s.jsx)(n(),{href:"/",className:"text-2xl font-bold text-gray-800",children:"Tina Education"}),(0,s.jsx)("button",{className:"text-gray-800 text-2xl md:hidden",onClick:()=>{t(!e)},"aria-label":"Toggle Menu",children:e?(0,s.jsx)(o.QCr,{}):(0,s.jsx)(o.OXb,{})}),(0,s.jsx)("nav",{className:"".concat(e?"block":"hidden"," md:block absolute md:static top-full left-0 w-full md:w-auto bg-white md:bg-transparent shadow-md md:shadow-none"),children:(0,s.jsxs)("ul",{className:"flex flex-col md:flex-row items-center",children:[(0,s.jsx)("li",{className:"ml-0 md:ml-8",children:(0,s.jsx)(n(),{href:d?"/dashboard":"/",className:"text-gray-800 font-medium",children:d?"Dashboard":"Home"})}),(0,s.jsx)("li",{className:"ml-0 md:ml-8",children:(0,s.jsx)(n(),{href:"/books",className:"text-gray-800 font-medium",children:"Books"})}),(0,s.jsx)("li",{className:"ml-0 md:ml-8",children:(0,s.jsx)(n(),{href:"/journals",className:"text-gray-800 font-medium",children:"Journals"})}),r&&(0,s.jsx)("li",{className:"ml-0 md:ml-8",children:(0,s.jsx)(n(),{href:"/wishlist",className:"text-gray-800 font-medium",children:"Wishlist"})}),r&&(0,s.jsx)("li",{className:"ml-0 md:ml-8",children:(0,s.jsx)(n(),{href:"/cart",className:"text-gray-800 font-medium",children:"Cart"})}),(0,s.jsx)("li",{className:"ml-0 md:ml-8",children:(0,s.jsx)(n(),{href:"#",className:"text-gray-800 font-medium",children:"Publisher with Us"})}),(0,s.jsx)("li",{className:"ml-0 md:ml-8",children:(0,s.jsx)("div",{className:"flex justify-center items-center gap-5 mb-8 md:mb-0 relative text-gray-500",children:(0,s.jsx)(c.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(h,{})})})})]})})]})})})}},1335:(e,t,r)=>{Promise.resolve().then(r.bind(r,344)),Promise.resolve().then(r.bind(r,2863)),Promise.resolve().then(r.bind(r,1)),Promise.resolve().then(r.t.bind(r,9324,23)),Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.t.bind(r,2353,23)),Promise.resolve().then(r.t.bind(r,7275,23))},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return i}});let s=r(8229),a=r(8883),n=r(3063),l=s._(r(1193));function i(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=n.Image},2353:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},2863:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(5155),a=r(2115),n=r(5493);function l(){var e;let{data:t,status:r}=(0,n.wV)(),[l,i]=(0,a.useState)([]),o=(0,a.useCallback)(e=>{i(t=>t.filter(t=>t.id!==e))},[]),c=(0,a.useCallback)(e=>{let t="toast_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(7)),r={...e,id:t};i(e=>[...e,r]),setTimeout(()=>{o(t)},5e3)},[o]);(0,a.useEffect)(()=>{var e;if("loading"===r||!(null==t||null==(e=t.user)?void 0:e.id))return;let s=async()=>{try{let e=await fetch("/api/auth/notifications",{headers:{"Cache-Control":"no-cache"}});if(e.ok)await e.json();else{if(401===e.status)return;console.warn("Failed to fetch notifications:",e.status,e.statusText)}}catch(e){if(e instanceof TypeError&&e.message.includes("fetch"))return;console.error("Failed to check for notifications:",e)}},a=setTimeout(()=>{s()},1e3),n=setInterval(s,3e4);return()=>{clearTimeout(a),clearInterval(n)}},[null==t||null==(e=t.user)?void 0:e.id,r]),(0,a.useEffect)(()=>(window.showNotificationToast=c,()=>{delete window.showNotificationToast}),[c]);let d=e=>{let t="p-4 rounded-lg shadow-lg border-l-4 max-w-sm";switch(e){case"success":return"".concat(t," bg-green-50 border-green-400 text-green-800");case"error":return"".concat(t," bg-red-50 border-red-400 text-red-800");case"warning":return"".concat(t," bg-yellow-50 border-yellow-400 text-yellow-800");default:return"".concat(t," bg-blue-50 border-blue-400 text-blue-800")}},u=e=>{switch(e){case"success":return"✅";case"error":return"❌";case"warning":return"⚠️";default:return"ℹ️"}};return 0===l.length?null:(0,s.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:l.map(e=>(0,s.jsx)("div",{className:"".concat(d(e.type)," animate-slide-in-right"),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-lg mr-3",children:u(e.type)}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium",children:e.title}),(0,s.jsx)("p",{className:"text-sm mt-1",children:e.message})]}),(0,s.jsx)("button",{onClick:()=>o(e.id),className:"ml-3 text-gray-400 hover:text-gray-600",children:"\xd7"})]})},e.id))})}},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=s.createContext&&s.createContext(a),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var s,a,n;s=e,a=t,n=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(u,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:a,size:n,title:o}=e,d=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)r=n[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,l),u=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,d,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==n?s.createElement(n.Consumer,null,e=>t(e)):t(a)}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(1469),a=r.n(s)},7275:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9324:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[519,6711,6874,3063,5493,8441,1684,7358],()=>t(1335)),_N_E=e.O()}]);