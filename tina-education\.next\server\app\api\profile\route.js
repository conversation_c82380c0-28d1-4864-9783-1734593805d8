(()=>{var e={};e.id=7073,e.ids=[7073],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var s=t(96330);let a=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54964:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>c,PATCH:()=>p});var a=t(96559),i=t(48088),o=t(37719),n=t(32190),l=t(56814),u=t(5956);async function c(){try{let e=await (0,l.j2)();if(!e?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let r=await u.z.user.findUnique({where:{id:e.user.id},select:{id:!0,name:!0,email:!0,image:!0,bio:!0,affiliation:!0,expertise:!0,phone:!0,website:!0,orcid:!0,role:!0,createdAt:!0}});if(!r)return n.NextResponse.json({error:"User not found"},{status:404});return n.NextResponse.json({user:r})}catch(e){return console.error("Failed to fetch profile:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{let r=await (0,l.j2)();if(!r?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{name:t,bio:s,affiliation:a,expertise:i,phone:o,website:c,orcid:p}=await e.json();if(p&&""!==p.trim()&&!/^\d{4}-\d{4}-\d{4}-\d{3}[\dX]$/.test(p.trim()))return n.NextResponse.json({error:"Invalid ORCID format. Please use format: 0000-0000-0000-0000"},{status:400});if(c&&""!==c.trim())try{new URL(c)}catch{return n.NextResponse.json({error:"Invalid website URL format"},{status:400})}let d=await u.z.user.update({where:{id:r.user.id},data:{name:t?.trim()||null,bio:s?.trim()||null,affiliation:a?.trim()||null,expertise:i?.trim()||null,phone:o?.trim()||null,website:c?.trim()||null,orcid:p?.trim()||null},select:{id:!0,name:!0,email:!0,image:!0,bio:!0,affiliation:!0,expertise:!0,phone:!0,website:!0,orcid:!0,role:!0,updatedAt:!0}});return n.NextResponse.json({message:"Profile updated successfully",user:d})}catch(e){return console.error("Failed to update profile:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/profile/route",pathname:"/api/profile",filename:"route",bundlePath:"app/api/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\profile\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:x}=d;function f(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>u,j2:()=>d});var s=t(19443),a=t(16467),i=t(5956),o=t(10189),n=t(56056),l=t(85663);let{handlers:u,signIn:c,signOut:p,auth:d}=(0,s.Ay)({adapter:(0,a.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[n.A,(0,o.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!r||!r.password||!await l.Ay.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,image:r.image,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,s=r.pathname.startsWith("/dashboard"),a=r.pathname.startsWith("/manuscripts"),i=r.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",t),console.log("\uD83D\uDD10 Authorized callback - pathname:",r.pathname),!s&&!a&&!i||t},session:async({session:e,token:r})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",r),r?.sub&&(e.user.id=r.sub,r.name&&(e.user.name=r.name),r.email&&(e.user.email=r.email),r.picture&&(e.user.image=r.picture),r.role&&(e.user.role=r.role)),e),jwt:async({token:e,user:r,account:t})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",r),console.log("\uD83D\uDD10 JWT callback - account:",t),r&&(e.sub=r.id,e.name=r.name,e.email=r.email,e.picture=r.image,e.role=r.role),e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,2190,5663,9404],()=>t(54964));module.exports=s})();