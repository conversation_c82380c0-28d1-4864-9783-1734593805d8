(()=>{var e={};e.id=8887,e.ids=[8887],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var s=t(96330);let a=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55201:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(96559),i=t(48088),n=t(37719),o=t(32190),u=t(5956),p=t(85663);async function c(e){try{let{name:r,email:t,password:s}=await e.json();if(!r||!t||!s)return o.NextResponse.json({error:"Name, email, and password are required"},{status:400});if(s.length<6)return o.NextResponse.json({error:"Password must be at least 6 characters long"},{status:400});if(await u.z.user.findUnique({where:{email:t}}))return o.NextResponse.json({error:"User with this email already exists"},{status:400});let a=await p.Ay.hash(s,12),i=await u.z.user.create({data:{name:r,email:t,password:a,role:"USER"},select:{id:!0,name:!0,email:!0,role:!0,createdAt:!0}});return o.NextResponse.json({message:"User created successfully",user:i},{status:201})}catch(e){return console.error("Signup error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\auth\\signup\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:h}=d;function g(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,2190,5663],()=>t(55201));module.exports=s})();