(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,r,s)=>{"use strict";s.d(r,{z:()=>a});var t=s(96330);let a=globalThis.prisma||new t.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17199:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(37413),a=s(56814),o=s(5956),i=s(39916),l=s(4536),n=s.n(l),d=s(88422),c=s(53384);async function m(e){try{return await o.z.user.findUnique({where:{id:e},select:{id:!0,name:!0,email:!0,image:!0,bio:!0,affiliation:!0,expertise:!0,phone:!0,website:!0,orcid:!0,role:!0,createdAt:!0}})}catch(e){return console.error("Failed to fetch user:",e),null}}async function u(){let e=await (0,a.j2)();e?.user?.id||(0,i.redirect)("/auth/signin");let r=await m(e.user.id);return r?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mt-13 mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6",children:[(0,t.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center",children:r.image?(0,t.jsx)(c.default,{src:r.image,alt:r.name||"User",width:96,height:96,className:"w-24 h-24 rounded-full object-cover"}):(0,t.jsx)("span",{className:"text-3xl",children:"\uD83D\uDC64"})}),(0,t.jsxs)("div",{className:"flex-1 text-center sm:text-left",children:[(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 mb-2",children:r.name||"User Profile"}),(0,t.jsx)("p",{className:"text-gray-600 mb-1",children:r.email}),(0,t.jsxs)("div",{className:"flex flex-wrap justify-center sm:justify-start items-center gap-2 sm:gap-4 text-sm text-gray-500",children:[(0,t.jsx)("span",{className:"capitalize",children:r.role.toLowerCase()}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,t.jsxs)("span",{children:["Member since ",new Date(r.createdAt).toLocaleDateString()]})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile Information"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Update your personal information and profile details"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(d.default,{user:r})})]}),(0,t.jsxs)("div",{className:"mt-8 grid grid-cols-1 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Account Information"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Account ID"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 font-mono",children:r.id})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Role"}),(0,t.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:r.role})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Member Since"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:new Date(r.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("a",{href:"/dashboard",className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200",children:"\uD83D\uDCCA Go to Dashboard"}),(0,t.jsx)(n(),{href:"/manuscripts",className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200",children:"\uD83D\uDCDD My Manuscripts"}),"REVIEWER"===r.role&&(0,t.jsx)("a",{href:"/dashboard/reviews",className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200",children:"\uD83D\uDCD6 My Reviews"}),"ADMIN"===r.role&&(0,t.jsx)("a",{href:"/dashboard/admin",className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200",children:"⚙️ Admin Dashboard"}),(0,t.jsx)("a",{href:"/dashboard/settings",className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200",children:"\uD83D\uDD27 Settings"})]})]})]})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"❌"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"User Not Found"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Unable to load user profile."})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24591:(e,r,s)=>{Promise.resolve().then(s.bind(s,94240)),Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.t.bind(s,46533,23))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46055:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,r,s)=>{"use strict";s.d(r,{Y9:()=>d,j2:()=>u});var t=s(19443),a=s(16467),o=s(5956),i=s(10189),l=s(56056),n=s(85663);let{handlers:d,signIn:c,signOut:m,auth:u}=(0,t.Ay)({adapter:(0,a.y)(o.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[l.A,(0,i.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await o.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!r||!r.password||!await n.Ay.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,image:r.image,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:r}}){let s=!!e?.user,t=r.pathname.startsWith("/dashboard"),a=r.pathname.startsWith("/manuscripts"),o=r.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",s),console.log("\uD83D\uDD10 Authorized callback - pathname:",r.pathname),!t&&!a&&!o||s},session:async({session:e,token:r})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",r),r?.sub&&(e.user.id=r.sub,r.name&&(e.user.name=r.name),r.email&&(e.user.email=r.email),r.picture&&(e.user.image=r.picture),r.role&&(e.user.role=r.role)),e),jwt:async({token:e,user:r,account:s})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",r),console.log("\uD83D\uDD10 JWT callback - account:",s),r&&(e.sub=r.id,e.name=r.name,e.email=r.email,e.picture=r.image,e.role=r.role),e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},87799:(e,r,s)=>{Promise.resolve().then(s.bind(s,88422)),Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.t.bind(s,49603,23))},88422:(e,r,s)=>{"use strict";s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\profile\\\\ProfileForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\profile\\ProfileForm.tsx","default")},90785:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=s(65239),a=s(48088),o=s(88170),i=s.n(o),l=s(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(r,n);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,17199)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\profile\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94240:(e,r,s)=>{"use strict";s.d(r,{default:()=>i});var t=s(60687),a=s(43210),o=s(16189);function i({user:e}){let r=(0,o.useRouter)(),[s,i]=(0,a.useState)(!1),[l,n]=(0,a.useState)(""),[d,c]=(0,a.useState)({name:e.name||"",bio:e.bio||"",affiliation:e.affiliation||"",expertise:e.expertise||"",phone:e.phone||"",website:e.website||"",orcid:e.orcid||""}),m=async e=>{e.preventDefault(),i(!0),n("");try{let e=await fetch("/api/profile",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)}),s=await e.json();if(!e.ok)throw Error(s.error||"Failed to update profile");n("Profile updated successfully!"),r.refresh()}catch(e){n(e instanceof Error?e.message:"An error occurred")}finally{i(!1)}},u=e=>{c({...d,[e.target.name]:e.target.value})};return(0,t.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[l&&(0,t.jsx)("div",{className:`p-4 rounded-md ${l.includes("successfully")?"bg-green-50 border border-green-200 text-green-700":"bg-red-50 border border-red-200 text-red-700"}`,children:l}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Basic Information"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",value:d.name,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your full name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,t.jsx)("input",{type:"email",id:"email",value:e.email,disabled:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Email cannot be changed. Contact support if needed."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number"}),(0,t.jsx)("input",{type:"tel",id:"phone",name:"phone",value:d.phone,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your phone number"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700",children:"Website"}),(0,t.jsx)("input",{type:"url",id:"website",name:"website",value:d.website,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"https://your-website.com"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Professional Information"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"affiliation",className:"block text-sm font-medium text-gray-700",children:"Affiliation"}),(0,t.jsx)("input",{type:"text",id:"affiliation",name:"affiliation",value:d.affiliation,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"University, Institution, or Organization"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"orcid",className:"block text-sm font-medium text-gray-700",children:"ORCID iD"}),(0,t.jsx)("input",{type:"text",id:"orcid",name:"orcid",value:d.orcid,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"0000-0000-0000-0000"}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Your ORCID identifier (optional)"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"expertise",className:"block text-sm font-medium text-gray-700",children:"Areas of Expertise"}),(0,t.jsx)("textarea",{id:"expertise",name:"expertise",rows:3,value:d.expertise,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"List your areas of expertise, research interests, or specializations"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700",children:"Biography"}),(0,t.jsx)("textarea",{id:"bio",name:"bio",rows:4,value:d.bio,onChange:u,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Tell us about yourself, your background, and your interests"})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:s,className:"px-6 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Updating...":"Update Profile"})})]})}},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,2190,5663,9404,7945,1658,3384,1795],()=>s(90785));module.exports=t})();