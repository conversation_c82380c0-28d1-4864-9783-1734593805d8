(()=>{var e={};e.id=4959,e.ids=[4959],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},41204:e=>{"use strict";e.exports=require("string_decoder")},42691:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>b,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>d});var i=t(96559),a=t(48088),o=t(37719),n=t(32190),u=t(56814),l=t(5956),c=t(42335);async function p(){try{let e=await (0,u.j2)();if(!e?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let r=await l.z.publication.findMany({where:{author_id:e.user.id},orderBy:{createdAt:"desc"},include:{user:{select:{name:!0,email:!0}},genre:{select:{id:!0,name:!0,slug:!0,parent:{select:{id:!0,name:!0,slug:!0}}}}}});return n.NextResponse.json({publications:r})}catch(e){return console.error("Failed to fetch publications:",e),n.NextResponse.json({error:"Failed to fetch publications"},{status:500})}}async function d(e){try{let r=await (0,u.j2)();if(!r?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});console.log("\uD83D\uDCDD Processing publication creation request...");let t=await e.formData(),s=t.get("title"),i=t.get("abstract"),a=t.get("keywords"),o=t.get("type"),p=t.get("genreId"),d=t.get("cover"),m=t.get("publicationFile");if(console.log("\uD83D\uDCDD Form data received:",{title:s,abstract:i?.substring(0,50)+"...",keywords:a,type:o,genreId:p,cover:d,hasFile:!!m,fileSize:m?.size||0}),!s?.trim())return n.NextResponse.json({error:"Title is required"},{status:400});if(!["JOURNAL","ARTICLE","BOOK","EBOOK","AUDIOBOOK"].includes(o))return n.NextResponse.json({error:"Invalid publication type"},{status:400});if(p&&p.trim()&&!await l.z.genre.findUnique({where:{id:p}}))return n.NextResponse.json({error:"Invalid genre selected"},{status:400});let g=null,x=null;if(m&&m.size>0){if(!["application/pdf","application/epub+zip","application/x-mobipocket-ebook","text/plain","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(m.type))return n.NextResponse.json({error:"Invalid file type. Please upload PDF, EPUB, MOBI, TXT, DOC, or DOCX files only."},{status:400});if(m.size>0x3200000)return n.NextResponse.json({error:"File size too large. Maximum size is 50MB."},{status:400});let e=Date.now(),r=Math.random().toString(36).substring(7),t=m.name.split(".").pop(),s=`publication-${e}-${r}.${t}`;try{let e=await (0,c.yJ)(`publications/${s}`,m,{access:"public",contentType:m.type});g=e.url,x=m.name,console.log(`✅ Publication file uploaded successfully: ${e.url}`)}catch(e){return console.error("❌ File upload failed:",e),n.NextResponse.json({error:"Failed to upload file"},{status:500})}}console.log("\uD83D\uDCDD Creating publication with file data...");let b=`pub_${Date.now()}_${Math.random().toString(36).substring(7)}`;await l.z.$executeRaw`
      INSERT INTO "Publication" (
        id, title, abstract, keywords, type, "genreId", cover, "fileUrl", "fileName", "author_id", "createdAt", "updatedAt"
      ) VALUES (
        ${b},
        ${s.trim()},
        ${i?.trim()||null},
        ${a?.trim()||null},
        ${o}::"Pub_type",
        ${p&&p.trim()||null},
        ${d?.trim()||null},
        ${g},
        ${x},
        ${r.user.id},
        NOW(),
        NOW()
      )
    `;let h=await l.z.publication.findUnique({where:{id:b},include:{user:{select:{name:!0,email:!0}},genre:{select:{id:!0,name:!0,slug:!0,parent:{select:{id:!0,name:!0,slug:!0}}}}}});return n.NextResponse.json({message:"Publication created successfully",publication:h},{status:201})}catch(e){return console.error("❌ Failed to create publication:",e),console.error("❌ Error details:",{message:e instanceof Error?e.message:"Unknown error",stack:e instanceof Error?e.stack:"No stack trace",name:e instanceof Error?e.name:"Unknown error type"}),n.NextResponse.json({error:"Failed to create publication",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/publications/route",pathname:"/api/publications",filename:"route",bundlePath:"app/api/publications/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\publications\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:b}=m;function h(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,r,t)=>{"use strict";t.d(r,{Y9:()=>l,j2:()=>d});var s=t(19443),i=t(16467),a=t(5956),o=t(10189),n=t(56056),u=t(85663);let{handlers:l,signIn:c,signOut:p,auth:d}=(0,s.Ay)({adapter:(0,i.y)(a.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[n.A,(0,o.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let r=await a.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!r||!r.password||!await u.Ay.compare(e.password,r.password))return null;return{id:r.id,email:r.email,name:r.name,image:r.image,role:r.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:r}}){let t=!!e?.user,s=r.pathname.startsWith("/dashboard"),i=r.pathname.startsWith("/manuscripts"),a=r.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",t),console.log("\uD83D\uDD10 Authorized callback - pathname:",r.pathname),!s&&!i&&!a||t},session:async({session:e,token:r})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",r),r?.sub&&(e.user.id=r.sub,r.name&&(e.user.name=r.name),r.email&&(e.user.email=r.email),r.picture&&(e.user.image=r.picture),r.role&&(e.user.role=r.role)),e),jwt:async({token:e,user:r,account:t})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",r),console.log("\uD83D\uDD10 JWT callback - account:",t),r&&(e.sub=r.id,e.name=r.name,e.email=r.email,e.picture=r.image,e.role=r.role),e)}})},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,2190,5663,9404,3285],()=>t(42691));module.exports=s})();