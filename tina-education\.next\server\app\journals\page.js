"use strict";(()=>{var e={};e.id=1055,e.ids=[1055],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},56089:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>c,tree:()=>p});var n=t(65239),a=t(48088),o=t(88170),i=t.n(o),s=t(30893),d={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);t.d(r,d);let p={children:["",{children:["journals",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90640)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\journals\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\journals\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/journals/page",pathname:"/journals",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{e.exports=require("url")},90640:(e,r,t)=>{t.r(r),t.d(r,{default:()=>o});var n=t(37413),a=t(89454);function o(){return(0,n.jsx)(a.default,{apiEndpoint:"/api/repository/journals",title:"Journals Repository",description:"Browse our comprehensive collection of academic journals and research publications.",emptyStateMessage:"No journals found. Try adjusting your search criteria or check back later for new publications.",emptyStateIcon:"\uD83D\uDCF0"})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,7945,1658,1795,7980],()=>t(56089));module.exports=n})();