(()=>{var e={};e.id=1654,e.ids=[1654],e.modules={222:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return a}});let n=r(4e4);function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:i}=(0,n.parsePath)(e);return""+r+t+a+i}},2720:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRenderSpan:function(){return c},AppRouteRouteHandlersSpan:function(){return d},BaseServerSpan:function(){return r},LoadComponentsSpan:function(){return n},LogSpanAllowList:function(){return b},MiddlewareSpan:function(){return p},NextNodeServerSpan:function(){return i},NextServerSpan:function(){return a},NextVanillaSpanAllowlist:function(){return h},NodeSpan:function(){return l},RenderSpan:function(){return s},ResolveMetadataSpan:function(){return f},RouterSpan:function(){return u},StartServerSpan:function(){return o}});var r=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(r||{}),n=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(n||{}),a=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(a||{}),i=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(i||{}),o=function(e){return e.startServer="startServer.startServer",e}(o||{}),s=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(s||{}),c=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(c||{}),u=function(e){return e.executeRoute="Router.executeRoute",e}(u||{}),l=function(e){return e.runHandler="Node.runHandler",e}(l||{}),d=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(d||{}),f=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(f||{}),p=function(e){return e.execute="Middleware.execute",e}(p||{});let h=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],b=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"]},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3582:(e,t,r)=>{"use strict";let n=r(63033),a=r(29294),i=r(75124),o=r(75659),s=r(66224),c=r(38248);function u(){let e=a.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return l(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return l(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function l(e,t){let r,n=d.get(u);return n||(r=f(e),d.set(e,r),r)}let d=new WeakMap;function f(e){let t=new p(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class p{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){b("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){b("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let h=(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function b(e){let t=a.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},4431:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,c.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(37461),a=r(39413),i=r(97683),o=r(21351),s=r(75124),c=r(38248);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var n=r(96330);let a=globalThis.prisma||new n.PrismaClient},6475:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return i},findSourceMapURL:function(){return a.findSourceMapURL}});let n=r(11264),a=r(11448),i=r(19357).createServerReference},7153:(e,t,r)=>{"use strict";e.exports=r(65239).vendored["react-rsc"].React},7944:(e,t,r)=>{let n={unstable_cache:r(18839).e,revalidateTag:r(51851).revalidateTag,revalidatePath:r(51851).revalidatePath,unstable_expireTag:r(51851).unstable_expireTag,unstable_expirePath:r(51851).unstable_expirePath,unstable_noStore:r(18446).M,unstable_cacheLife:r(83409).F,unstable_cacheTag:r(10421).z};e.exports=n,t.unstable_cache=n.unstable_cache,t.revalidatePath=n.revalidatePath,t.revalidateTag=n.revalidateTag,t.unstable_expireTag=n.unstable_expireTag,t.unstable_expirePath=n.unstable_expirePath,t.unstable_noStore=n.unstable_noStore,t.unstable_cacheLife=n.unstable_cacheLife,t.unstable_cacheTag=n.unstable_cacheTag},8658:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(60687),a=r(85814),i=r.n(a),o=r(16189),s=r(43210),c=r(69587);function u({href:e,icon:t,label:r,onClick:a}){let s=(0,o.usePathname)(),c=s===e||"/dashboard"!==e&&s.startsWith(e);return(0,n.jsx)("li",{className:"mb-1",children:(0,n.jsxs)(i(),{href:e,onClick:a,className:`flex items-center px-5 py-3 text-gray-800 font-medium hover:bg-blue-50 hover:text-blue-900 hover:border-l-3 hover:border-blue-900 transition-colors ${c?"bg-blue-50 text-blue-900 border-l-3 border-blue-900":""}`,children:[(0,n.jsx)("span",{className:"mr-3 text-lg",children:t}),(0,n.jsx)("span",{children:r})]})})}function l(){let[e,t]=(0,s.useState)(!1),r=()=>{t(!1)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{id:"mobile-sidebar-toggle",onClick:()=>t(!e),className:"lg:hidden fixed top-24 left-4 z-50 p-2 bg-white rounded-md shadow-md border border-gray-200","aria-label":"Toggle sidebar menu",children:e?(0,n.jsx)(c.QCr,{className:"text-gray-600"}):(0,n.jsx)(c.OXb,{className:"text-gray-600"})}),e&&(0,n.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 top-20"}),(0,n.jsx)("aside",{id:"mobile-sidebar",className:`
          fixed lg:sticky w-64 bg-white shadow-md top-20 h-[calc(100vh-5rem)] overflow-y-auto border-r border-gray-200 flex-shrink-0 z-40
          transition-transform duration-300 ease-in-out
          ${e?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:(0,n.jsxs)("div",{className:"py-5",children:[(0,n.jsxs)("ul",{children:[(0,n.jsx)(u,{href:"/dashboard",icon:(0,n.jsx)(c.v$b,{}),label:"Dashboard",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/publications",icon:(0,n.jsx)(c.vd0,{}),label:"My Publications",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/manuscripts",icon:(0,n.jsx)(c.t69,{}),label:"Manuscripts",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/reviews",icon:(0,n.jsx)(c.kkc,{}),label:"Reviews",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/analytics",icon:(0,n.jsx)(c.YYR,{}),label:"Analytics",onClick:r})]}),(0,n.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,n.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Collaboration"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)(u,{href:"/dashboard/collaborators",icon:(0,n.jsx)(c.YXz,{}),label:"Co-authors",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/messages",icon:(0,n.jsx)(c.uN,{}),label:"Messages",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/notifications",icon:(0,n.jsx)(c.jNV,{}),label:"Notifications",onClick:r})]})]}),(0,n.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,n.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Repository"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)(u,{href:"/repository",icon:(0,n.jsx)(c.vd0,{}),label:"All Publications",onClick:r}),(0,n.jsx)(u,{href:"/books",icon:(0,n.jsx)(c.hko,{}),label:"Books",onClick:r}),(0,n.jsx)(u,{href:"/journals",icon:(0,n.jsx)(c.Nhm,{}),label:"Journals",onClick:r}),(0,n.jsx)(u,{href:"/articles",icon:(0,n.jsx)(c.Gp9,{}),label:"Articles",onClick:r})]})]}),(0,n.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,n.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Resources"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)(u,{href:"/dashboard/library",icon:(0,n.jsx)(c.U$b,{}),label:"My Library",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/tools",icon:(0,n.jsx)(c.xdT,{}),label:"Writing Tools",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/calendar",icon:(0,n.jsx)(c.bfZ,{}),label:"Calendar",onClick:r}),(0,n.jsx)(u,{href:"/dashboard/settings",icon:(0,n.jsx)(c.Pcn,{}),label:"Settings",onClick:r})]})]})]})})]})}},8768:(e,t,r)=>{Promise.resolve().then(r.bind(r,87401))},9835:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return o}});let n=r(31666),a=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,i=/\/\[[^/]+\](?=\/|$)/;function o(e,t){return(void 0===t&&(t=!0),(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),t)?i.test(e):a.test(e)}},10421:(e,t,r)=>{"use strict";function n(...e){throw Object.defineProperty(Error("cacheTag() is only available with the experimental.useCache config."),"__NEXT_ERROR_CODE",{value:"E628",enumerable:!1,configurable:!0})}Object.defineProperty(t,"z",{enumerable:!0,get:function(){return n}}),r(63033),r(24624)},10492:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10920:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_REQUEST_META:function(){return r},addRequestMeta:function(){return i},getRequestMeta:function(){return n},removeRequestMeta:function(){return o},setRequestMeta:function(){return a}});let r=Symbol.for("NextInternalRequestMeta");function n(e,t){let n=e[r]||{};return"string"==typeof t?n[t]:n}function a(e,t){return e[r]=t,t}function i(e,t,r){let i=n(e);return i[t]=r,a(e,i)}function o(e,t){let r=n(e);return delete r[t],a(e,r)}},12130:(e,t,r)=>{var n;(()=>{var a={226:function(a,i){!function(o,s){"use strict";var c="function",u="undefined",l="object",d="string",f="major",p="model",h="name",b="type",g="vendor",y="version",m="architecture",v="console",_="mobile",w="tablet",x="smarttv",E="wearable",S="embedded",R="Amazon",A="Apple",O="ASUS",P="BlackBerry",T="Browser",k="Chrome",C="Firefox",j="Google",N="Huawei",I="Microsoft",D="Motorola",M="Opera",U="Samsung",$="Sharp",L="Sony",H="Xiaomi",W="Zebra",B="Facebook",K="Chromium OS",F="Mac OS",q=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},G=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},J=function(e,t){return typeof e===d&&-1!==V(t).indexOf(V(e))},V=function(e){return e.toLowerCase()},X=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},z=function(e,t){for(var r,n,a,i,o,u,d=0;d<t.length&&!o;){var f=t[d],p=t[d+1];for(r=n=0;r<f.length&&!o&&f[r];)if(o=f[r++].exec(e))for(a=0;a<p.length;a++)u=o[++n],typeof(i=p[a])===l&&i.length>0?2===i.length?typeof i[1]==c?this[i[0]]=i[1].call(this,u):this[i[0]]=i[1]:3===i.length?typeof i[1]!==c||i[1].exec&&i[1].test?this[i[0]]=u?u.replace(i[1],i[2]):void 0:this[i[0]]=u?i[1].call(this,u,i[2]):void 0:4===i.length&&(this[i[0]]=u?i[3].call(this,u.replace(i[1],i[2])):s):this[i]=u||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===l&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(J(t[r][n],e))return"?"===r?s:r}else if(J(t[r],e))return"?"===r?s:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,y],[/opios[\/ ]+([\w\.]+)/i],[y,[h,M+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[h,M]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[h,"UC"+T]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[h,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[h,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+T],y],[/\bfocus\/([\w\.]+)/i],[y,[h,C+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[h,M+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[h,M+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[h,"MIUI "+T]],[/fxios\/([-\w\.]+)/i],[y,[h,C]],[/\bqihu|(qi?ho?o?|360)browser/i],[[h,"360 "+T]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1 "+T],y],[/(comodo_dragon)\/([\w\.]+)/i],[[h,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[h,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,B],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[h,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[h,k+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,k+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[h,"Android "+T]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[y,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[h,C+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[h,y],[/(cobalt)\/([\w\.]+)/i],[h,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[m,"amd64"]],[/(ia32(?=;))/i],[[m,V]],[/((?:i[346]|x)86)[;\)]/i],[[m,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[m,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[m,"armhf"]],[/windows (ce|mobile); ppc;/i],[[m,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[m,/ower/,"",V]],[/(sun4\w)[;\)]/i],[[m,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[m,V]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[g,U],[b,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[g,U],[b,_]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[g,A],[b,_]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[g,A],[b,w]],[/(macintosh);/i],[p,[g,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[g,$],[b,_]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[g,N],[b,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[g,N],[b,_]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[g,H],[b,_]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[g,H],[b,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[g,"OPPO"],[b,_]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[g,"Vivo"],[b,_]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[g,"Realme"],[b,_]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[g,D],[b,_]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[g,D],[b,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[g,"LG"],[b,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[g,"LG"],[b,_]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[g,"Lenovo"],[b,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[g,"Nokia"],[b,_]],[/(pixel c)\b/i],[p,[g,j],[b,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[g,j],[b,_]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[g,L],[b,_]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[g,L],[b,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[g,"OnePlus"],[b,_]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[g,R],[b,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[g,R],[b,_]],[/(playbook);[-\w\),; ]+(rim)/i],[p,g,[b,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[g,P],[b,_]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[g,O],[b,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[g,O],[b,_]],[/(nexus 9)/i],[p,[g,"HTC"],[b,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[g,[p,/_/g," "],[b,_]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[g,"Acer"],[b,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[g,"Meizu"],[b,_]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[g,p,[b,_]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[g,p,[b,w]],[/(surface duo)/i],[p,[g,I],[b,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[g,"Fairphone"],[b,_]],[/(u304aa)/i],[p,[g,"AT&T"],[b,_]],[/\bsie-(\w*)/i],[p,[g,"Siemens"],[b,_]],[/\b(rct\w+) b/i],[p,[g,"RCA"],[b,w]],[/\b(venue[\d ]{2,7}) b/i],[p,[g,"Dell"],[b,w]],[/\b(q(?:mv|ta)\w+) b/i],[p,[g,"Verizon"],[b,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[g,"Barnes & Noble"],[b,w]],[/\b(tm\d{3}\w+) b/i],[p,[g,"NuVision"],[b,w]],[/\b(k88) b/i],[p,[g,"ZTE"],[b,w]],[/\b(nx\d{3}j) b/i],[p,[g,"ZTE"],[b,_]],[/\b(gen\d{3}) b.+49h/i],[p,[g,"Swiss"],[b,_]],[/\b(zur\d{3}) b/i],[p,[g,"Swiss"],[b,w]],[/\b((zeki)?tb.*\b) b/i],[p,[g,"Zeki"],[b,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[g,"Dragon Touch"],p,[b,w]],[/\b(ns-?\w{0,9}) b/i],[p,[g,"Insignia"],[b,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[g,"NextBook"],[b,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[g,"Voice"],p,[b,_]],[/\b(lvtel\-)?(v1[12]) b/i],[[g,"LvTel"],p,[b,_]],[/\b(ph-1) /i],[p,[g,"Essential"],[b,_]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[g,"Envizen"],[b,w]],[/\b(trio[-\w\. ]+) b/i],[p,[g,"MachSpeed"],[b,w]],[/\btu_(1491) b/i],[p,[g,"Rotor"],[b,w]],[/(shield[\w ]+) b/i],[p,[g,"Nvidia"],[b,w]],[/(sprint) (\w+)/i],[g,p,[b,_]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[g,I],[b,_]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[g,W],[b,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[g,W],[b,_]],[/smart-tv.+(samsung)/i],[g,[b,x]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[g,U],[b,x]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[g,"LG"],[b,x]],[/(apple) ?tv/i],[g,[p,A+" TV"],[b,x]],[/crkey/i],[[p,k+"cast"],[g,j],[b,x]],[/droid.+aft(\w)( bui|\))/i],[p,[g,R],[b,x]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[g,$],[b,x]],[/(bravia[\w ]+)( bui|\))/i],[p,[g,L],[b,x]],[/(mitv-\w{5}) bui/i],[p,[g,H],[b,x]],[/Hbbtv.*(technisat) (.*);/i],[g,p,[b,x]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[g,X],[p,X],[b,x]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[b,x]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[g,p,[b,v]],[/droid.+; (shield) bui/i],[p,[g,"Nvidia"],[b,v]],[/(playstation [345portablevi]+)/i],[p,[g,L],[b,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[g,I],[b,v]],[/((pebble))app/i],[g,p,[b,E]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[g,A],[b,E]],[/droid.+; (glass) \d/i],[p,[g,j],[b,E]],[/droid.+; (wt63?0{2,3})\)/i],[p,[g,W],[b,E]],[/(quest( 2| pro)?)/i],[p,[g,B],[b,E]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[g,[b,S]],[/(aeobc)\b/i],[p,[g,R],[b,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[b,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[b,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[b,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[b,_]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[g,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[h,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[h,[y,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[h,"Windows"],[y,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,F],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,y],[/\(bb(10);/i],[y,[h,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[h,C+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[h,k+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,K],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,y],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,y]]},ee=function(e,t){if(typeof e===l&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==u&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),a=r&&r.userAgentData?r.userAgentData:s,i=t?q(Q,t):Q,v=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[h]=s,t[y]=s,z.call(t,n,i.browser),t[f]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,v&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[m]=s,z.call(e,n,i.cpu),e},this.getDevice=function(){var e={};return e[g]=s,e[p]=s,e[b]=s,z.call(e,n,i.device),v&&!e[b]&&a&&a.mobile&&(e[b]=_),v&&"Macintosh"==e[p]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[b]=w),e},this.getEngine=function(){var e={};return e[h]=s,e[y]=s,z.call(e,n,i.engine),e},this.getOS=function(){var e={};return e[h]=s,e[y]=s,z.call(e,n,i.os),v&&!e[h]&&a&&"Unknown"!=a.platform&&(e[h]=a.platform.replace(/chrome os/i,K).replace(/macos/i,F)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?X(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=G([h,y,f]),ee.CPU=G([m]),ee.DEVICE=G([p,g,b,v,_,x,w,E,S]),ee.ENGINE=ee.OS=G([h,y]),typeof i!==u?(a.exports&&(i=a.exports=ee),i.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof o!==u&&(o.UAParser=ee);var et=typeof o!==u&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},i={};function o(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}},n=!0;try{a[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete i[e]}return r.exports}o.ab=__dirname+"/",e.exports=o(226)})()},13944:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DetachedPromise",{enumerable:!0,get:function(){return r}});class r{constructor(){let e,t;this.promise=new Promise((r,n)=>{e=r,t=n}),this.resolve=e,this.reject=t}}},14245:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{CachedRouteKind:function(){return r},IncrementalCacheKind:function(){return n}});var r=function(e){return e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.PAGES="PAGES",e.FETCH="FETCH",e.REDIRECT="REDIRECT",e.IMAGE="IMAGE",e}({}),n=function(e){return e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.PAGES="PAGES",e.FETCH="FETCH",e.IMAGE="IMAGE",e}({})},14659:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies},stringifyCookie:function(){return n.stringifyCookie}});let n=r(90036)},16296:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return s},throwWithStaticGenerationBailoutError:function(){return i},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return o}});let n=r(66224),a=r(3295);function i(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function o(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function s(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function c(){let e=a.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},17529:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return n.getSortedRouteObjects},getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return a.isDynamicRoute}});let n=r(26343),a=r(9835)},18389:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18446:(e,t,r)=>{"use strict";Object.defineProperty(t,"M",{enumerable:!0,get:function(){return o}});let n=r(29294),a=r(63033),i=r(75124);function o(){let e=n.workAsyncStorage.getStore(),t=a.workUnitAsyncStorage.getStore();if(e)!e.forceStatic&&(e.isUnstableNoStore=!0,t&&"prerender"===t.type||(0,i.markCurrentScopeAsDynamic)(e,t,"unstable_noStore()"))}},18463:(e,t)=>{"use strict";function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},18839:(e,t,r)=>{"use strict";Object.defineProperty(t,"e",{enumerable:!0,get:function(){return l}});let n=r(36376),a=r(24624),i=r(29294),o=r(63033),s=r(75626),c=0;async function u(e,t,r,a,i,o,c){await t.set(r,{kind:s.CachedRouteKind.FETCH,data:{headers:{},body:JSON.stringify(e),status:200,url:""},revalidate:"number"!=typeof i?n.CACHE_ONE_YEAR:i},{fetchCache:!0,tags:a,fetchIdx:o,fetchUrl:c})}function l(e,t,r={}){if(0===r.revalidate)throw Object.defineProperty(Error(`Invariant revalidate: 0 can not be passed to unstable_cache(), must be "false" or "> 0" ${e.toString()}`),"__NEXT_ERROR_CODE",{value:"E57",enumerable:!1,configurable:!0});let n=r.tags?(0,a.validateTags)(r.tags,`unstable_cache ${e.toString()}`):[];(0,a.validateRevalidate)(r.revalidate,`unstable_cache ${e.name||e.toString()}`);let d=`${e.toString()}-${Array.isArray(t)&&t.join(",")}`;return async(...t)=>{let a=i.workAsyncStorage.getStore(),l=o.workUnitAsyncStorage.getStore(),f=(null==a?void 0:a.incrementalCache)||globalThis.__incrementalCache;if(!f)throw Object.defineProperty(Error(`Invariant: incrementalCache missing in unstable_cache ${e.toString()}`),"__NEXT_ERROR_CODE",{value:"E469",enumerable:!1,configurable:!0});let p=l&&"prerender"===l.type?l.cacheSignal:null;p&&p.beginRead();try{let i=l&&"request"===l.type?l:void 0,p=(null==i?void 0:i.url.pathname)??(null==a?void 0:a.route)??"",h=new URLSearchParams((null==i?void 0:i.url.search)??""),b=[...h.keys()].sort((e,t)=>e.localeCompare(t)).map(e=>`${e}=${h.get(e)}`).join("&"),g=`${d}-${JSON.stringify(t)}`,y=await f.generateCacheKey(g),m=`unstable_cache ${p}${b.length?"?":""}${b} ${e.name?` ${e.name}`:y}`,v=(a?a.nextFetchId:c)??1,_=null==l?void 0:l.implicitTags,w={type:"unstable-cache",phase:"render",implicitTags:_,draftMode:l&&a&&(0,o.getDraftModeProviderForCacheScope)(a,l)};if(a){if(a.nextFetchId=v+1,l&&("cache"===l.type||"prerender"===l.type||"prerender-ppr"===l.type||"prerender-legacy"===l.type)){"number"==typeof r.revalidate&&(l.revalidate<r.revalidate||(l.revalidate=r.revalidate));let e=l.tags;if(null===e)l.tags=n.slice();else for(let t of n)e.includes(t)||e.push(t)}if(!(l&&"unstable-cache"===l.type)&&"force-no-store"!==a.fetchCache&&!a.isOnDemandRevalidate&&!f.isOnDemandRevalidate&&!a.isDraftMode){let i=await f.get(y,{kind:s.IncrementalCacheKind.FETCH,revalidate:r.revalidate,tags:n,softTags:null==_?void 0:_.tags,fetchIdx:v,fetchUrl:m});if(i&&i.value)if(i.value.kind!==s.CachedRouteKind.FETCH)console.error(`Invariant invalid cacheEntry returned for ${g}`);else{let s=void 0!==i.value.data.body?JSON.parse(i.value.data.body):void 0;return i.isStale&&(a.pendingRevalidates||(a.pendingRevalidates={}),a.pendingRevalidates[g]=o.workUnitAsyncStorage.run(w,e,...t).then(e=>u(e,f,y,n,r.revalidate,v,m)).catch(e=>console.error(`revalidating cache with key: ${g}`,e))),s}}let i=await o.workUnitAsyncStorage.run(w,e,...t);return a.isDraftMode||u(i,f,y,n,r.revalidate,v,m),i}{if(c+=1,!f.isOnDemandRevalidate){let e=await f.get(y,{kind:s.IncrementalCacheKind.FETCH,revalidate:r.revalidate,tags:n,fetchIdx:v,fetchUrl:m,softTags:null==_?void 0:_.tags});if(e&&e.value){if(e.value.kind!==s.CachedRouteKind.FETCH)console.error(`Invariant invalid cacheEntry returned for ${g}`);else if(!e.isStale)return void 0!==e.value.data.body?JSON.parse(e.value.data.body):void 0}}let a=await o.workUnitAsyncStorage.run(w,e,...t);return u(a,f,y,n,r.revalidate,v,m),a}}finally{p&&p.endRead()}}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isAbortError:function(){return c},pipeToNodeResponse:function(){return u}});let n=r(75738),a=r(13944),i=r(44334),o=r(2720),s=r(37317);function c(e){return(null==e?void 0:e.name)==="AbortError"||(null==e?void 0:e.name)===n.ResponseAbortedName}async function u(e,t,r){try{let{errored:c,destroyed:u}=t;if(c||u)return;let l=(0,n.createAbortController)(t),d=function(e,t){let r=!1,n=new a.DetachedPromise;function c(){n.resolve()}e.on("drain",c),e.once("close",()=>{e.off("drain",c),n.resolve()});let u=new a.DetachedPromise;return e.once("finish",()=>{u.resolve()}),new WritableStream({write:async t=>{if(!r){if(r=!0,"performance"in globalThis&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX){let e=(0,s.getClientComponentLoaderMetrics)();e&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,{start:e.clientComponentLoadStart,end:e.clientComponentLoadStart+e.clientComponentLoadTimes})}e.flushHeaders(),(0,i.getTracer)().trace(o.NextNodeServerSpan.startResponse,{spanName:"start response"},()=>void 0)}try{let r=e.write(t);"flush"in e&&"function"==typeof e.flush&&e.flush(),r||(await n.promise,n=new a.DetachedPromise)}catch(t){throw e.end(),Object.defineProperty(Error("failed to write chunk to response",{cause:t}),"__NEXT_ERROR_CODE",{value:"E321",enumerable:!1,configurable:!0})}},abort:t=>{e.writableFinished||e.destroy(t)},close:async()=>{if(t&&await t,!e.writableFinished)return e.end(),u.promise}})}(t,r);await e.pipeTo(d,{signal:l.signal})}catch(e){if(c(e))return;throw Object.defineProperty(Error("failed to pipe response",{cause:e}),"__NEXT_ERROR_CODE",{value:"E180",enumerable:!1,configurable:!0})}}},19186:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return a}});let n=r(4e4);function a(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},20464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"after",{enumerable:!0,get:function(){return a}});let n=r(29294);function a(e){let t=n.workAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}},21351:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return i}});let n=r(32913),a=r(59311);function i(e){return(0,a.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23302:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"InvariantError",{enumerable:!0,get:function(){return r}});class r extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}},23794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ImageResponse:function(){return n.ImageResponse},NextRequest:function(){return a.NextRequest},NextResponse:function(){return i.NextResponse},URLPattern:function(){return s.URLPattern},after:function(){return c.after},connection:function(){return u.connection},unstable_rootParams:function(){return l.unstable_rootParams},userAgent:function(){return o.userAgent},userAgentFromString:function(){return o.userAgentFromString}});let n=r(91339),a=r(81661),i=r(39999),o=r(64043),s=r(70388),c=r(44234),u=r(40193),l=r(61096)},23874:(e,t,r)=>{"use strict";Object.defineProperty(t,"U",{enumerable:!0,get:function(){return f}});let n=r(82362),a=r(14659),i=r(29294),o=r(63033),s=r(75124),c=r(66224),u=r(37461),l=r(75659),d=(r(97748),r(16296));function f(){let e="cookies",t=i.workAsyncStorage.getStore(),r=o.workUnitAsyncStorage.getStore();if(t){if(r&&"after"===r.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return h(n.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new c.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var l=t.route,f=r;let e=p.get(f);if(e)return e;let n=(0,u.makeHangingPromise)(f.renderSignal,"`cookies()`");return p.set(f,n),Object.defineProperties(n,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}},size:{get(){let e="`cookies().size`",t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${b(arguments[0])})\``;let t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${b(arguments[0])})\``;let t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${b(arguments[0])})\``;let t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${b(t)}, ...)\``:"`cookies().set(...)`"}let t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${b(arguments[0])})\``:`\`cookies().delete(${b(arguments[0])}, ...)\``;let t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}},clear:{value:function(){let e="`cookies().clear()`",t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}},toString:{value:function(){let e="`cookies().toString()`",t=y(l,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(l,e,t,f)}}}),n}else"prerender-ppr"===r.type?(0,s.postponeWithTracking)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,s.throwToInterruptStaticGeneration)(e,t,r);(0,s.trackDynamicDataInDynamicRender)(t,r)}let g=(0,o.getExpectedRequestStore)(e);return h((0,n.areCookiesMutableInCurrentPhase)(g)?g.userspaceMutableCookies:g.cookies)}let p=new WeakMap;function h(e){let t=p.get(e);if(t)return t;let r=Promise.resolve(e);return p.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):m.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):v.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function b(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let g=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(y);function y(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function m(){return this.getAll().map(e=>[e.name,e]).values()}function v(e){for(let e of this.getAll())this.delete(e.name);return e}},23879:(e,t)=>{"use strict";function r(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isThenable",{enumerable:!0,get:function(){return r}})},24624:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_PATCH_SYMBOL:function(){return f},createPatchedFetcher:function(){return g},patchFetch:function(){return y},validateRevalidate:function(){return p},validateTags:function(){return h}});let n=r(2720),a=r(44334),i=r(36376),o=r(75124),s=r(37461),c=r(98485),u=r(75626),l=r(97748),d=r(72150),f=Symbol.for("next-patch");function p(e,t){try{let r;if(!1===e)r=i.INFINITE_CACHE;else if("number"==typeof e&&!isNaN(e)&&e>-1)r=e;else if(void 0!==e)throw Object.defineProperty(Error(`Invalid revalidate value "${e}" on "${t}", must be a non-negative number or false`),"__NEXT_ERROR_CODE",{value:"E179",enumerable:!1,configurable:!0});return r}catch(e){if(e instanceof Error&&e.message.includes("Invalid revalidate"))throw e;return}}function h(e,t){let r=[],n=[];for(let a=0;a<e.length;a++){let o=e[a];if("string"!=typeof o?n.push({tag:o,reason:"invalid type, must be a string"}):o.length>i.NEXT_CACHE_TAG_MAX_LENGTH?n.push({tag:o,reason:`exceeded max length of ${i.NEXT_CACHE_TAG_MAX_LENGTH}`}):r.push(o),r.length>i.NEXT_CACHE_TAG_MAX_ITEMS){console.warn(`Warning: exceeded max tag count for ${t}, dropped tags:`,e.slice(a).join(", "));break}}if(n.length>0)for(let{tag:e,reason:r}of(console.warn(`Warning: invalid tags passed to ${t}: `),n))console.log(`tag: "${e}" ${r}`);return r}function b(e,t){var r;if(e&&(null==(r=e.requestEndedState)?!void 0:!r.ended))((process.env.NEXT_DEBUG_BUILD||"1"===process.env.NEXT_SSG_FETCH_METRICS)&&e.isStaticGeneration||0)&&(e.fetchMetrics??=[],e.fetchMetrics.push({...t,end:performance.timeOrigin+performance.now(),idx:e.nextFetchId||0}))}function g(e,{workAsyncStorage:t,workUnitAsyncStorage:r}){let c=async(c,f)=>{var g,y;let m;try{(m=new URL(c instanceof Request?c.url:c)).username="",m.password=""}catch{m=void 0}let v=(null==m?void 0:m.href)??"",_=(null==f||null==(g=f.method)?void 0:g.toUpperCase())||"GET",w=(null==f||null==(y=f.next)?void 0:y.internal)===!0,x="1"===process.env.NEXT_OTEL_FETCH_DISABLED,E=w?void 0:performance.timeOrigin+performance.now(),S=t.getStore(),R=r.getStore(),A=R&&"prerender"===R.type?R.cacheSignal:null;A&&A.beginRead();let O=(0,a.getTracer)().trace(w?n.NextNodeServerSpan.internalFetch:n.AppRenderSpan.fetch,{hideSpan:x,kind:a.SpanKind.CLIENT,spanName:["fetch",_,v].filter(Boolean).join(" "),attributes:{"http.url":v,"http.method":_,"net.peer.name":null==m?void 0:m.hostname,"net.peer.port":(null==m?void 0:m.port)||void 0}},async()=>{var t;let r,n,a,g;if(w||!S||S.isDraftMode)return e(c,f);let y=c&&"object"==typeof c&&"string"==typeof c.method,m=e=>(null==f?void 0:f[e])||(y?c[e]:null),_=e=>{var t,r,n;return void 0!==(null==f||null==(t=f.next)?void 0:t[e])?null==f||null==(r=f.next)?void 0:r[e]:y?null==(n=c.next)?void 0:n[e]:void 0},x=_("revalidate"),O=h(_("tags")||[],`fetch ${c.toString()}`),P=R&&("cache"===R.type||"prerender"===R.type||"prerender-ppr"===R.type||"prerender-legacy"===R.type)?R:void 0;if(P&&Array.isArray(O)){let e=P.tags??(P.tags=[]);for(let t of O)e.includes(t)||e.push(t)}let T=null==R?void 0:R.implicitTags,k=R&&"unstable-cache"===R.type?"force-no-store":S.fetchCache,C=!!S.isUnstableNoStore,j=m("cache"),N="";"string"==typeof j&&void 0!==x&&("force-cache"===j&&0===x||"no-store"===j&&(x>0||!1===x))&&(r=`Specified "cache: ${j}" and "revalidate: ${x}", only one should be specified.`,j=void 0,x=void 0);let I="no-cache"===j||"no-store"===j||"force-no-store"===k||"only-no-store"===k,D=!k&&!j&&!x&&S.forceDynamic;"force-cache"===j&&void 0===x?x=!1:(null==R?void 0:R.type)!=="cache"&&(I||D)&&(x=0),("no-cache"===j||"no-store"===j)&&(N=`cache: ${j}`),g=p(x,S.route);let M=m("headers"),U="function"==typeof(null==M?void 0:M.get)?M:new Headers(M||{}),$=U.get("authorization")||U.get("cookie"),L=!["get","head"].includes((null==(t=m("method"))?void 0:t.toLowerCase())||"get"),H=void 0==k&&(void 0==j||"default"===j)&&void 0==x,W=H&&!S.isPrerendering||($||L)&&P&&0===P.revalidate;if(H&&void 0!==R&&"prerender"===R.type)return A&&(A.endRead(),A=null),(0,s.makeHangingPromise)(R.renderSignal,"fetch()");switch(k){case"force-no-store":N="fetchCache = force-no-store";break;case"only-no-store":if("force-cache"===j||void 0!==g&&g>0)throw Object.defineProperty(Error(`cache: 'force-cache' used on fetch for ${v} with 'export const fetchCache = 'only-no-store'`),"__NEXT_ERROR_CODE",{value:"E448",enumerable:!1,configurable:!0});N="fetchCache = only-no-store";break;case"only-cache":if("no-store"===j)throw Object.defineProperty(Error(`cache: 'no-store' used on fetch for ${v} with 'export const fetchCache = 'only-cache'`),"__NEXT_ERROR_CODE",{value:"E521",enumerable:!1,configurable:!0});break;case"force-cache":(void 0===x||0===x)&&(N="fetchCache = force-cache",g=i.INFINITE_CACHE)}if(void 0===g?"default-cache"!==k||C?"default-no-store"===k?(g=0,N="fetchCache = default-no-store"):C?(g=0,N="noStore call"):W?(g=0,N="auto no cache"):(N="auto cache",g=P?P.revalidate:i.INFINITE_CACHE):(g=i.INFINITE_CACHE,N="fetchCache = default-cache"):N||(N=`revalidate: ${g}`),!(S.forceStatic&&0===g)&&!W&&P&&g<P.revalidate){if(0===g)if(R&&"prerender"===R.type)return A&&(A.endRead(),A=null),(0,s.makeHangingPromise)(R.renderSignal,"fetch()");else(0,o.markCurrentScopeAsDynamic)(S,R,`revalidate: 0 fetch ${c} ${S.route}`);P&&x===g&&(P.revalidate=g)}let B="number"==typeof g&&g>0,{incrementalCache:K}=S,F=(null==R?void 0:R.type)==="request"||(null==R?void 0:R.type)==="cache"?R:void 0;if(K&&(B||(null==F?void 0:F.serverComponentsHmrCache)))try{n=await K.generateCacheKey(v,y?c:f)}catch(e){console.error("Failed to generate cache key for",c)}let q=S.nextFetchId??1;S.nextFetchId=q+1;let G=()=>Promise.resolve(),J=async(t,a)=>{let o=["cache","credentials","headers","integrity","keepalive","method","mode","redirect","referrer","referrerPolicy","window","duplex",...t?[]:["signal"]];if(y){let e=c,t={body:e._ogBody||e.body};for(let r of o)t[r]=e[r];c=new Request(e.url,t)}else if(f){let{_ogBody:e,body:r,signal:n,...a}=f;f={...a,body:e||r,signal:t?void 0:n}}let s={...f,next:{...null==f?void 0:f.next,fetchType:"origin",fetchIdx:q}};return e(c,s).then(async e=>{if(!t&&E&&b(S,{start:E,url:v,cacheReason:a||N,cacheStatus:0===g||a?"skip":"miss",cacheWarning:r,status:e.status,method:s.method||"GET"}),200===e.status&&K&&n&&(B||(null==F?void 0:F.serverComponentsHmrCache))){let t=g>=i.INFINITE_CACHE?i.CACHE_ONE_YEAR:g;if(R&&"prerender"===R.type){let r=await e.arrayBuffer(),a={headers:Object.fromEntries(e.headers.entries()),body:Buffer.from(r).toString("base64"),status:e.status,url:e.url};return await K.set(n,{kind:u.CachedRouteKind.FETCH,data:a,revalidate:t},{fetchCache:!0,fetchUrl:v,fetchIdx:q,tags:O}),await G(),new Response(r,{headers:e.headers,status:e.status,statusText:e.statusText})}{let[r,a]=(0,d.cloneResponse)(e);return r.arrayBuffer().then(async e=>{var a;let i=Buffer.from(e),o={headers:Object.fromEntries(r.headers.entries()),body:i.toString("base64"),status:r.status,url:r.url};null==F||null==(a=F.serverComponentsHmrCache)||a.set(n,o),B&&await K.set(n,{kind:u.CachedRouteKind.FETCH,data:o,revalidate:t},{fetchCache:!0,fetchUrl:v,fetchIdx:q,tags:O})}).catch(e=>console.warn("Failed to set fetch cache",c,e)).finally(G),a}}return await G(),e}).catch(e=>{throw G(),e})},V=!1,X=!1;if(n&&K){let e;if((null==F?void 0:F.isHmrRefresh)&&F.serverComponentsHmrCache&&(e=F.serverComponentsHmrCache.get(n),X=!0),B&&!e){G=await K.lock(n);let t=S.isOnDemandRevalidate?null:await K.get(n,{kind:u.IncrementalCacheKind.FETCH,revalidate:g,fetchUrl:v,fetchIdx:q,tags:O,softTags:null==T?void 0:T.tags});if(H&&R&&"prerender"===R.type&&await (0,l.waitAtLeastOneReactRenderTask)(),t?await G():a="cache-control: no-cache (hard refresh)",(null==t?void 0:t.value)&&t.value.kind===u.CachedRouteKind.FETCH)if(S.isRevalidate&&t.isStale)V=!0;else{if(t.isStale&&(S.pendingRevalidates??={},!S.pendingRevalidates[n])){let e=J(!0).then(async e=>({body:await e.arrayBuffer(),headers:e.headers,status:e.status,statusText:e.statusText})).finally(()=>{S.pendingRevalidates??={},delete S.pendingRevalidates[n||""]});e.catch(console.error),S.pendingRevalidates[n]=e}e=t.value.data}}if(e){E&&b(S,{start:E,url:v,cacheReason:N,cacheStatus:X?"hmr":"hit",cacheWarning:r,status:e.status||200,method:(null==f?void 0:f.method)||"GET"});let t=new Response(Buffer.from(e.body,"base64"),{headers:e.headers,status:e.status});return Object.defineProperty(t,"url",{value:e.url}),t}}if(S.isStaticGeneration&&f&&"object"==typeof f){let{cache:e}=f;if("no-store"===e)if(R&&"prerender"===R.type)return A&&(A.endRead(),A=null),(0,s.makeHangingPromise)(R.renderSignal,"fetch()");else(0,o.markCurrentScopeAsDynamic)(S,R,`no-store fetch ${c} ${S.route}`);let t="next"in f,{next:r={}}=f;if("number"==typeof r.revalidate&&P&&r.revalidate<P.revalidate){if(0===r.revalidate)if(R&&"prerender"===R.type)return(0,s.makeHangingPromise)(R.renderSignal,"fetch()");else(0,o.markCurrentScopeAsDynamic)(S,R,`revalidate: 0 fetch ${c} ${S.route}`);S.forceStatic&&0===r.revalidate||(P.revalidate=r.revalidate)}t&&delete f.next}if(!n||!V)return J(!1,a);{let e=n;S.pendingRevalidates??={};let t=S.pendingRevalidates[e];if(t){let e=await t;return new Response(e.body,{headers:e.headers,status:e.status,statusText:e.statusText})}let r=J(!0,a).then(d.cloneResponse);return(t=r.then(async e=>{let t=e[0];return{body:await t.arrayBuffer(),headers:t.headers,status:t.status,statusText:t.statusText}}).finally(()=>{var t;(null==(t=S.pendingRevalidates)?void 0:t[e])&&delete S.pendingRevalidates[e]})).catch(()=>{}),S.pendingRevalidates[e]=t,r.then(e=>e[1])}});if(A)try{return await O}finally{A&&A.endRead()}return O};return c.__nextPatched=!0,c.__nextGetStaticStore=()=>t,c._nextOriginalFetch=e,globalThis[f]=!0,c}function y(e){if(!0===globalThis[f])return;let t=(0,c.createDedupeFetch)(globalThis.fetch);globalThis.fetch=g(t,e)}},24796:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(32913).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25104:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},25220:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PageSignatureError:function(){return r},RemovedPageError:function(){return n},RemovedUAError:function(){return a}});class r extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class a extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},26343:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return a},getSortedRoutes:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let a=e[0];if(a.startsWith("[")&&a.endsWith("]")){let r=a.slice(1,-1),o=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),o=!0),r.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+r+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(r.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function i(e,r){if(null!==e&&e!==r)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});t.forEach(e=>{if(e===r)throw Object.defineProperty(Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(e.replace(/\W/g,"")===a.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),t.push(r)}if(n)if(o){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});i(this.optionalRestSlugName,r),this.optionalRestSlugName=r,a="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});i(this.restSlugName,r),this.restSlugName=r,a="[...]"}else{if(o)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});i(this.slugName,r),this.slugName=r,a="[]"}}this.children.has(a)||this.children.set(a,new r),this.children.get(a)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}function a(e,t){let r={},a=[];for(let n=0;n<e.length;n++){let i=t(e[n]);r[i]=n,a[n]=i}return n(a).map(t=>e[r[t]])}},26861:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(37413),a=r(56814),i=r(87401),o=r(5956),s=r(61120);async function c(){let e=await (0,a.j2)();if(!e?.user?.id)return(0,n.jsx)("div",{children:"Unauthorized"});let[t,r]=await Promise.all([o.z.notification.findMany({where:{userId:e.user.id},orderBy:{createdAt:"desc"},take:50}),o.z.notification.count({where:{userId:e.user.id,isRead:!1}})]);return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"}),r>0&&(0,n.jsxs)("span",{className:"bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full",children:[r," unread"]})]}),0===t.length?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD14"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications yet"}),(0,n.jsx)("p",{className:"text-gray-500",children:"You'll see notifications here when there's activity on your manuscripts."})]}):(0,n.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,n.jsx)(i.default,{notification:{...e,createdAt:e.createdAt.toISOString()}},e.id))})]})}function u(){return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("div",{className:"flex items-center justify-between",children:(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"})}),(0,n.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,n.jsx)("div",{className:"p-4 border rounded-lg animate-pulse",children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"}),(0,n.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/4"})]})]})},t))})]})}function l(){return(0,n.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,n.jsx)(s.Suspense,{fallback:(0,n.jsx)(u,{}),children:(0,n.jsx)(c,{})})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31666:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return a},extractInterceptionRouteInformation:function(){return o},isInterceptionRouteAppPath:function(){return i}});let n=r(79535),a=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>a.find(t=>e.startsWith(t)))}function o(e){let t,r,i;for(let n of e.split("/"))if(r=a.find(e=>n.startsWith(e))){[t,i]=e.split(r,2);break}if(!t||!r||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":i="/"===t?"/"+i:t+"/"+i;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=o.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:i}}},32913:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return a},getAccessFallbackErrorTypeByStatus:function(){return s},getAccessFallbackHTTPStatus:function(){return o},isHTTPAccessFallbackError:function(){return i}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),a="NEXT_HTTP_ERROR_FALLBACK";function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===a&&n.has(Number(r))}function o(e){return Number(e.digest.split(";")[1])}function s(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33331:(e,t)=>{"use strict";function r(e){for(let t=0;t<e.length;t++){let r=e[t];if("function"!=typeof r)throw Object.defineProperty(Error(`A "use server" file can only export async functions, found ${typeof r}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`),"__NEXT_ERROR_CODE",{value:"E352",enumerable:!1,configurable:!0})}}Object.defineProperty(t,"D",{enumerable:!0,get:function(){return r}})},33873:e=>{"use strict";e.exports=require("path")},34360:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{chainStreams:function(){return f},continueDynamicHTMLResume:function(){return O},continueDynamicPrerender:function(){return R},continueFizzStream:function(){return S},continueStaticPrerender:function(){return A},createBufferedTransformStream:function(){return y},createDocumentClosingStream:function(){return P},createRootLayoutValidatorStream:function(){return E},renderToInitialFizzStream:function(){return m},streamFromBuffer:function(){return h},streamFromString:function(){return p},streamToBuffer:function(){return b},streamToString:function(){return g}});let n=r(44334),a=r(2720),i=r(13944),o=r(97748),s=r(42503),c=r(88477),u=r(54806);function l(){}let d=new TextEncoder;function f(...e){if(0===e.length)throw Object.defineProperty(Error("Invariant: chainStreams requires at least one stream"),"__NEXT_ERROR_CODE",{value:"E437",enumerable:!1,configurable:!0});if(1===e.length)return e[0];let{readable:t,writable:r}=new TransformStream,n=e[0].pipeTo(r,{preventClose:!0}),a=1;for(;a<e.length-1;a++){let t=e[a];n=n.then(()=>t.pipeTo(r,{preventClose:!0}))}let i=e[a];return(n=n.then(()=>i.pipeTo(r))).catch(l),t}function p(e){return new ReadableStream({start(t){t.enqueue(d.encode(e)),t.close()}})}function h(e){return new ReadableStream({start(t){t.enqueue(e),t.close()}})}async function b(e){let t=e.getReader(),r=[];for(;;){let{done:e,value:n}=await t.read();if(e)break;r.push(n)}return Buffer.concat(r)}async function g(e,t){let r=new TextDecoder("utf-8",{fatal:!0}),n="";for await(let a of e){if(null==t?void 0:t.aborted)return n;n+=r.decode(a,{stream:!0})}return n+r.decode()}function y(){let e,t=[],r=0,n=n=>{if(e)return;let a=new i.DetachedPromise;e=a,(0,o.scheduleImmediate)(()=>{try{let e=new Uint8Array(r),a=0;for(let r=0;r<t.length;r++){let n=t[r];e.set(n,a),a+=n.byteLength}t.length=0,r=0,n.enqueue(e)}catch{}finally{e=void 0,a.resolve()}})};return new TransformStream({transform(e,a){t.push(e),r+=e.byteLength,n(a)},flush(){if(e)return e.promise}})}function m({ReactDOMServer:e,element:t,streamOptions:r}){return(0,n.getTracer)().trace(a.AppRenderSpan.renderToReadableStream,async()=>e.renderToReadableStream(t,r))}function v(e){let t=!1,r=!1;return new TransformStream({async transform(n,a){r=!0;let i=await e();if(t){if(i){let e=d.encode(i);a.enqueue(e)}a.enqueue(n)}else{let e=(0,c.indexOfUint8Array)(n,s.ENCODED_TAGS.CLOSED.HEAD);if(-1!==e){if(i){let t=d.encode(i),r=new Uint8Array(n.length+t.length);r.set(n.slice(0,e)),r.set(t,e),r.set(n.slice(e),e+t.length),a.enqueue(r)}else a.enqueue(n);t=!0}else i&&a.enqueue(d.encode(i)),a.enqueue(n),t=!0}},async flush(t){if(r){let r=await e();r&&t.enqueue(d.encode(r))}}})}function _(e){let t=null,r=!1;async function n(n){if(t)return;let a=e.getReader();await (0,o.atLeastOneTask)();try{for(;;){let{done:e,value:t}=await a.read();if(e){r=!0;return}n.enqueue(t)}}catch(e){n.error(e)}}return new TransformStream({transform(e,r){r.enqueue(e),t||(t=n(r))},flush(e){if(!r)return t||n(e)}})}let w="</body></html>";function x(){let e=!1;return new TransformStream({transform(t,r){if(e)return r.enqueue(t);let n=(0,c.indexOfUint8Array)(t,s.ENCODED_TAGS.CLOSED.BODY_AND_HTML);if(n>-1){if(e=!0,t.length===s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length)return;let a=t.slice(0,n);if(r.enqueue(a),t.length>s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length+n){let e=t.slice(n+s.ENCODED_TAGS.CLOSED.BODY_AND_HTML.length);r.enqueue(e)}}else r.enqueue(t)},flush(e){e.enqueue(s.ENCODED_TAGS.CLOSED.BODY_AND_HTML)}})}function E(){let e=!1,t=!1;return new TransformStream({async transform(r,n){!e&&(0,c.indexOfUint8Array)(r,s.ENCODED_TAGS.OPENING.HTML)>-1&&(e=!0),!t&&(0,c.indexOfUint8Array)(r,s.ENCODED_TAGS.OPENING.BODY)>-1&&(t=!0),n.enqueue(r)},flush(r){let n=[];e||n.push("html"),t||n.push("body"),n.length&&r.enqueue(d.encode(`<html id="__next_error__">
            <template
              data-next-error-message="Missing ${n.map(e=>`<${e}>`).join(n.length>1?" and ":"")} tags in the root layout.
Read more at https://nextjs.org/docs/messages/missing-root-layout-tags""
              data-next-error-digest="${u.MISSING_ROOT_TAGS_ERROR}"
              data-next-error-stack=""
            ></template>
          `))}})}async function S(e,{suffix:t,inlinedDataStream:r,isStaticGeneration:n,getServerInsertedHTML:a,getServerInsertedMetadata:s,validateRootLayout:c}){let u=t?t.split(w,1)[0]:null;n&&"allReady"in e&&await e.allReady;var l=[y(),v(s),null!=u&&u.length>0?function(e){let t,r=!1,n=r=>{let n=new i.DetachedPromise;t=n,(0,o.scheduleImmediate)(()=>{try{r.enqueue(d.encode(e))}catch{}finally{t=void 0,n.resolve()}})};return new TransformStream({transform(e,t){t.enqueue(e),r||(r=!0,n(t))},flush(n){if(t)return t.promise;r||n.enqueue(d.encode(e))}})}(u):null,r?_(r):null,c?E():null,x(),v(a)];let f=e;for(let e of l)e&&(f=f.pipeThrough(e));return f}async function R(e,{getServerInsertedHTML:t,getServerInsertedMetadata:r}){return e.pipeThrough(y()).pipeThrough(new TransformStream({transform(e,t){(0,c.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.BODY_AND_HTML)||(0,c.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.BODY)||(0,c.isEquivalentUint8Arrays)(e,s.ENCODED_TAGS.CLOSED.HTML)||(e=(0,c.removeFromUint8Array)(e,s.ENCODED_TAGS.CLOSED.BODY),e=(0,c.removeFromUint8Array)(e,s.ENCODED_TAGS.CLOSED.HTML),t.enqueue(e))}})).pipeThrough(v(t)).pipeThrough(v(r))}async function A(e,{inlinedDataStream:t,getServerInsertedHTML:r,getServerInsertedMetadata:n}){return e.pipeThrough(y()).pipeThrough(v(r)).pipeThrough(v(n)).pipeThrough(_(t)).pipeThrough(x())}async function O(e,{inlinedDataStream:t,getServerInsertedHTML:r,getServerInsertedMetadata:n}){return e.pipeThrough(y()).pipeThrough(v(r)).pipeThrough(v(n)).pipeThrough(_(t)).pipeThrough(x())}function P(){return p(w)}},34821:(e,t,r)=>{"use strict";var n=r(68790),a={stream:!0},i=new Map;function o(e){var t=globalThis.__next_require__(e);return"function"!=typeof t.then||"fulfilled"===t.status?null:(t.then(function(e){t.status="fulfilled",t.value=e},function(e){t.status="rejected",t.reason=e}),t)}function s(){}function c(e){for(var t=e[1],n=[],a=0;a<t.length;){var c=t[a++];t[a++];var u=i.get(c);if(void 0===u){u=r.e(c),n.push(u);var l=i.set.bind(i,c,null);u.then(l,s),i.set(c,u)}else null!==u&&n.push(u)}return 4===e.length?0===n.length?o(e[0]):Promise.all(n).then(function(){return o(e[0])}):0<n.length?Promise.all(n):null}function u(e){var t=globalThis.__next_require__(e[0]);if(4===e.length&&"function"==typeof t.then)if("fulfilled"===t.status)t=t.value;else throw t.reason;return"*"===e[2]?t:""===e[2]?t.__esModule?t.default:t:t[e[2]]}var l=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,d=Symbol.for("react.transitional.element"),f=Symbol.for("react.lazy"),p=Symbol.iterator,h=Symbol.asyncIterator,b=Array.isArray,g=Object.getPrototypeOf,y=Object.prototype,m=new WeakMap;function v(e,t,r,n,a){function i(e,r){r=new Blob([new Uint8Array(r.buffer,r.byteOffset,r.byteLength)]);var n=c++;return null===l&&(l=new FormData),l.append(t+n,r),"$"+e+n.toString(16)}function o(e,w){if(null===w)return null;if("object"==typeof w){switch(w.$$typeof){case d:if(void 0!==r&&-1===e.indexOf(":")){var x,E,S,R,A,O=v.get(this);if(void 0!==O)return r.set(O+":"+e,w),"$T"}throw Error("React Element cannot be passed to Server Functions from the Client without a temporary reference set. Pass a TemporaryReferenceSet to the options.");case f:O=w._payload;var P=w._init;null===l&&(l=new FormData),u++;try{var T=P(O),k=c++,C=s(T,k);return l.append(t+k,C),"$"+k.toString(16)}catch(e){if("object"==typeof e&&null!==e&&"function"==typeof e.then){u++;var j=c++;return O=function(){try{var e=s(w,j),r=l;r.append(t+j,e),u--,0===u&&n(r)}catch(e){a(e)}},e.then(O,O),"$"+j.toString(16)}return a(e),null}finally{u--}}if("function"==typeof w.then){null===l&&(l=new FormData),u++;var N=c++;return w.then(function(e){try{var r=s(e,N);(e=l).append(t+N,r),u--,0===u&&n(e)}catch(e){a(e)}},a),"$@"+N.toString(16)}if(void 0!==(O=v.get(w)))if(_!==w)return O;else _=null;else -1===e.indexOf(":")&&void 0!==(O=v.get(this))&&(e=O+":"+e,v.set(w,e),void 0!==r&&r.set(e,w));if(b(w))return w;if(w instanceof FormData){null===l&&(l=new FormData);var I=l,D=t+(e=c++)+"_";return w.forEach(function(e,t){I.append(D+t,e)}),"$K"+e.toString(16)}if(w instanceof Map)return e=c++,O=s(Array.from(w),e),null===l&&(l=new FormData),l.append(t+e,O),"$Q"+e.toString(16);if(w instanceof Set)return e=c++,O=s(Array.from(w),e),null===l&&(l=new FormData),l.append(t+e,O),"$W"+e.toString(16);if(w instanceof ArrayBuffer)return e=new Blob([w]),O=c++,null===l&&(l=new FormData),l.append(t+O,e),"$A"+O.toString(16);if(w instanceof Int8Array)return i("O",w);if(w instanceof Uint8Array)return i("o",w);if(w instanceof Uint8ClampedArray)return i("U",w);if(w instanceof Int16Array)return i("S",w);if(w instanceof Uint16Array)return i("s",w);if(w instanceof Int32Array)return i("L",w);if(w instanceof Uint32Array)return i("l",w);if(w instanceof Float32Array)return i("G",w);if(w instanceof Float64Array)return i("g",w);if(w instanceof BigInt64Array)return i("M",w);if(w instanceof BigUint64Array)return i("m",w);if(w instanceof DataView)return i("V",w);if("function"==typeof Blob&&w instanceof Blob)return null===l&&(l=new FormData),e=c++,l.append(t+e,w),"$B"+e.toString(16);if(e=null===(x=w)||"object"!=typeof x?null:"function"==typeof(x=p&&x[p]||x["@@iterator"])?x:null)return(O=e.call(w))===w?(e=c++,O=s(Array.from(O),e),null===l&&(l=new FormData),l.append(t+e,O),"$i"+e.toString(16)):Array.from(O);if("function"==typeof ReadableStream&&w instanceof ReadableStream)return function(e){try{var r,i,s,d,f,p,h,b=e.getReader({mode:"byob"})}catch(d){return r=e.getReader(),null===l&&(l=new FormData),i=l,u++,s=c++,r.read().then(function e(c){if(c.done)i.append(t+s,"C"),0==--u&&n(i);else try{var l=JSON.stringify(c.value,o);i.append(t+s,l),r.read().then(e,a)}catch(e){a(e)}},a),"$R"+s.toString(16)}return d=b,null===l&&(l=new FormData),f=l,u++,p=c++,h=[],d.read(new Uint8Array(1024)).then(function e(r){r.done?(r=c++,f.append(t+r,new Blob(h)),f.append(t+p,'"$o'+r.toString(16)+'"'),f.append(t+p,"C"),0==--u&&n(f)):(h.push(r.value),d.read(new Uint8Array(1024)).then(e,a))},a),"$r"+p.toString(16)}(w);if("function"==typeof(e=w[h]))return E=w,S=e.call(w),null===l&&(l=new FormData),R=l,u++,A=c++,E=E===S,S.next().then(function e(r){if(r.done){if(void 0===r.value)R.append(t+A,"C");else try{var i=JSON.stringify(r.value,o);R.append(t+A,"C"+i)}catch(e){a(e);return}0==--u&&n(R)}else try{var s=JSON.stringify(r.value,o);R.append(t+A,s),S.next().then(e,a)}catch(e){a(e)}},a),"$"+(E?"x":"X")+A.toString(16);if((e=g(w))!==y&&(null===e||null!==g(e))){if(void 0===r)throw Error("Only plain objects, and a few built-ins, can be passed to Server Functions. Classes or null prototypes are not supported.");return"$T"}return w}if("string"==typeof w)return"Z"===w[w.length-1]&&this[e]instanceof Date?"$D"+w:e="$"===w[0]?"$"+w:w;if("boolean"==typeof w)return w;if("number"==typeof w)return Number.isFinite(w)?0===w&&-1/0==1/w?"$-0":w:1/0===w?"$Infinity":-1/0===w?"$-Infinity":"$NaN";if(void 0===w)return"$undefined";if("function"==typeof w){if(void 0!==(O=m.get(w)))return e=JSON.stringify({id:O.id,bound:O.bound},o),null===l&&(l=new FormData),O=c++,l.set(t+O,e),"$F"+O.toString(16);if(void 0!==r&&-1===e.indexOf(":")&&void 0!==(O=v.get(this)))return r.set(O+":"+e,w),"$T";throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.")}if("symbol"==typeof w){if(void 0!==r&&-1===e.indexOf(":")&&void 0!==(O=v.get(this)))return r.set(O+":"+e,w),"$T";throw Error("Symbols cannot be passed to a Server Function without a temporary reference set. Pass a TemporaryReferenceSet to the options.")}if("bigint"==typeof w)return"$n"+w.toString(10);throw Error("Type "+typeof w+" is not supported as an argument to a Server Function.")}function s(e,t){return"object"==typeof e&&null!==e&&(t="$"+t.toString(16),v.set(e,t),void 0!==r&&r.set(t,e)),_=e,JSON.stringify(e,o)}var c=1,u=0,l=null,v=new WeakMap,_=e,w=s(e,0);return null===l?n(w):(l.set(t+"0",w),0===u&&n(l)),function(){0<u&&(u=0,null===l?n(w):n(l))}}var _=new WeakMap;function w(e){var t=m.get(this);if(!t)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var r=null;if(null!==t.bound){if((r=_.get(t))||(n={id:t.id,bound:t.bound},o=new Promise(function(e,t){a=e,i=t}),v(n,"",void 0,function(e){if("string"==typeof e){var t=new FormData;t.append("0",e),e=t}o.status="fulfilled",o.value=e,a(e)},function(e){o.status="rejected",o.reason=e,i(e)}),r=o,_.set(t,r)),"rejected"===r.status)throw r.reason;if("fulfilled"!==r.status)throw r;t=r.value;var n,a,i,o,s=new FormData;t.forEach(function(t,r){s.append("$ACTION_"+e+":"+r,t)}),r=s,t="$ACTION_REF_"+e}else t="$ACTION_ID_"+t.id;return{name:t,method:"POST",encType:"multipart/form-data",data:r}}function x(e,t){var r=m.get(this);if(!r)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(r.id!==e)return!1;var n=r.bound;if(null===n)return 0===t;switch(n.status){case"fulfilled":return n.value.length===t;case"pending":throw n;case"rejected":throw n.reason;default:throw"string"!=typeof n.status&&(n.status="pending",n.then(function(e){n.status="fulfilled",n.value=e},function(e){n.status="rejected",n.reason=e})),n}}function E(e,t,r,n){m.has(e)||(m.set(e,{id:t,originalBind:e.bind,bound:r}),Object.defineProperties(e,{$$FORM_ACTION:{value:void 0===n?w:function(){var e=m.get(this);if(!e)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var t=e.bound;return null===t&&(t=Promise.resolve([])),n(e.id,t)}},$$IS_SIGNATURE_EQUAL:{value:x},bind:{value:A}}))}var S=Function.prototype.bind,R=Array.prototype.slice;function A(){var e=m.get(this);if(!e)return S.apply(this,arguments);var t=e.originalBind.apply(this,arguments),r=R.call(arguments,1),n=null;return n=null!==e.bound?Promise.resolve(e.bound).then(function(e){return e.concat(r)}):Promise.resolve(r),m.set(t,{id:e.id,originalBind:t.bind,bound:n}),Object.defineProperties(t,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:x},bind:{value:A}}),t}function O(e,t,r,n){this.status=e,this.value=t,this.reason=r,this._response=n}function P(e){switch(e.status){case"resolved_model":$(e);break;case"resolved_module":L(e)}switch(e.status){case"fulfilled":return e.value;case"pending":case"blocked":throw e;default:throw e.reason}}function T(e){return new O("pending",null,null,e)}function k(e,t){for(var r=0;r<e.length;r++)(0,e[r])(t)}function C(e,t,r){switch(e.status){case"fulfilled":k(t,e.value);break;case"pending":case"blocked":if(e.value)for(var n=0;n<t.length;n++)e.value.push(t[n]);else e.value=t;if(e.reason){if(r)for(t=0;t<r.length;t++)e.reason.push(r[t])}else e.reason=r;break;case"rejected":r&&k(r,e.reason)}}function j(e,t){if("pending"!==e.status&&"blocked"!==e.status)e.reason.error(t);else{var r=e.reason;e.status="rejected",e.reason=t,null!==r&&k(r,t)}}function N(e,t,r){return new O("resolved_model",(r?'{"done":true,"value":':'{"done":false,"value":')+t+"}",null,e)}function I(e,t,r){D(e,(r?'{"done":true,"value":':'{"done":false,"value":')+t+"}")}function D(e,t){if("pending"!==e.status)e.reason.enqueueModel(t);else{var r=e.value,n=e.reason;e.status="resolved_model",e.value=t,null!==r&&($(e),C(e,r,n))}}function M(e,t){if("pending"===e.status||"blocked"===e.status){var r=e.value,n=e.reason;e.status="resolved_module",e.value=t,null!==r&&(L(e),C(e,r,n))}}O.prototype=Object.create(Promise.prototype),O.prototype.then=function(e,t){switch(this.status){case"resolved_model":$(this);break;case"resolved_module":L(this)}switch(this.status){case"fulfilled":e(this.value);break;case"pending":case"blocked":e&&(null===this.value&&(this.value=[]),this.value.push(e)),t&&(null===this.reason&&(this.reason=[]),this.reason.push(t));break;default:t&&t(this.reason)}};var U=null;function $(e){var t=U;U=null;var r=e.value;e.status="blocked",e.value=null,e.reason=null;try{var n=JSON.parse(r,e._response._fromJSON),a=e.value;if(null!==a&&(e.value=null,e.reason=null,k(a,n)),null!==U){if(U.errored)throw U.value;if(0<U.deps){U.value=n,U.chunk=e;return}}e.status="fulfilled",e.value=n}catch(t){e.status="rejected",e.reason=t}finally{U=t}}function L(e){try{var t=u(e.value);e.status="fulfilled",e.value=t}catch(t){e.status="rejected",e.reason=t}}function H(e,t){e._closed=!0,e._closedReason=t,e._chunks.forEach(function(e){"pending"===e.status&&j(e,t)})}function W(e){return{$$typeof:f,_payload:e,_init:P}}function B(e,t){var r=e._chunks,n=r.get(t);return n||(n=e._closed?new O("rejected",null,e._closedReason,e):T(e),r.set(t,n)),n}function K(e,t,r,n,a,i){function o(e){if(!s.errored){s.errored=!0,s.value=e;var t=s.chunk;null!==t&&"blocked"===t.status&&j(t,e)}}if(U){var s=U;s.deps++}else s=U={parent:null,chunk:null,value:null,deps:1,errored:!1};return e.then(function e(c){for(var u=1;u<i.length;u++){for(;c.$$typeof===f;)if((c=c._payload)===s.chunk)c=s.value;else if("fulfilled"===c.status)c=c.value;else{i.splice(0,u-1),c.then(e,o);return}c=c[i[u]]}u=a(n,c,t,r),t[r]=u,""===r&&null===s.value&&(s.value=u),t[0]===d&&"object"==typeof s.value&&null!==s.value&&s.value.$$typeof===d&&(c=s.value,"3"===r)&&(c.props=u),s.deps--,0===s.deps&&null!==(u=s.chunk)&&"blocked"===u.status&&(c=u.value,u.status="fulfilled",u.value=s.value,null!==c&&k(c,s.value))},o),null}function F(e,t,r,n){if(!e._serverReferenceConfig)return function(e,t,r){function n(){var e=Array.prototype.slice.call(arguments);return i?"fulfilled"===i.status?t(a,i.value.concat(e)):Promise.resolve(i).then(function(r){return t(a,r.concat(e))}):t(a,e)}var a=e.id,i=e.bound;return E(n,a,i,r),n}(t,e._callServer,e._encodeFormAction);var a=function(e,t){var r="",n=e[t];if(n)r=n.name;else{var a=t.lastIndexOf("#");if(-1!==a&&(r=t.slice(a+1),n=e[t.slice(0,a)]),!n)throw Error('Could not find the module "'+t+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.')}return n.async?[n.id,n.chunks,r,1]:[n.id,n.chunks,r]}(e._serverReferenceConfig,t.id),i=c(a);if(i)t.bound&&(i=Promise.all([i,t.bound]));else{if(!t.bound)return E(i=u(a),t.id,t.bound,e._encodeFormAction),i;i=Promise.resolve(t.bound)}if(U){var o=U;o.deps++}else o=U={parent:null,chunk:null,value:null,deps:1,errored:!1};return i.then(function(){var i=u(a);if(t.bound){var s=t.bound.value.slice(0);s.unshift(null),i=i.bind.apply(i,s)}E(i,t.id,t.bound,e._encodeFormAction),r[n]=i,""===n&&null===o.value&&(o.value=i),r[0]===d&&"object"==typeof o.value&&null!==o.value&&o.value.$$typeof===d&&(s=o.value,"3"===n)&&(s.props=i),o.deps--,0===o.deps&&null!==(i=o.chunk)&&"blocked"===i.status&&(s=i.value,i.status="fulfilled",i.value=o.value,null!==s&&k(s,o.value))},function(e){if(!o.errored){o.errored=!0,o.value=e;var t=o.chunk;null!==t&&"blocked"===t.status&&j(t,e)}}),null}function q(e,t,r,n,a){var i=parseInt((t=t.split(":"))[0],16);switch((i=B(e,i)).status){case"resolved_model":$(i);break;case"resolved_module":L(i)}switch(i.status){case"fulfilled":var o=i.value;for(i=1;i<t.length;i++){for(;o.$$typeof===f;)if("fulfilled"!==(o=o._payload).status)return K(o,r,n,e,a,t.slice(i-1));else o=o.value;o=o[t[i]]}return a(e,o,r,n);case"pending":case"blocked":return K(i,r,n,e,a,t);default:return U?(U.errored=!0,U.value=i.reason):U={parent:null,chunk:null,value:i.reason,deps:0,errored:!0},null}}function G(e,t){return new Map(t)}function J(e,t){return new Set(t)}function V(e,t){return new Blob(t.slice(1),{type:t[0]})}function X(e,t){e=new FormData;for(var r=0;r<t.length;r++)e.append(t[r][0],t[r][1]);return e}function z(e,t){return t[Symbol.iterator]()}function Y(e,t){return t}function Z(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.')}function Q(e,t,r,n,a,i,o){var s,c=new Map;this._bundlerConfig=e,this._serverReferenceConfig=t,this._moduleLoading=r,this._callServer=void 0!==n?n:Z,this._encodeFormAction=a,this._nonce=i,this._chunks=c,this._stringDecoder=new TextDecoder,this._fromJSON=null,this._rowLength=this._rowTag=this._rowID=this._rowState=0,this._buffer=[],this._closed=!1,this._closedReason=null,this._tempRefs=o,this._fromJSON=(s=this,function(e,t){if("string"==typeof t){var r=s,n=this,a=e,i=t;if("$"===i[0]){if("$"===i)return null!==U&&"0"===a&&(U={parent:U,chunk:null,value:null,deps:0,errored:!1}),d;switch(i[1]){case"$":return i.slice(1);case"L":return W(r=B(r,n=parseInt(i.slice(2),16)));case"@":if(2===i.length)return new Promise(function(){});return B(r,n=parseInt(i.slice(2),16));case"S":return Symbol.for(i.slice(2));case"F":return q(r,i=i.slice(2),n,a,F);case"T":if(n="$"+i.slice(2),null==(r=r._tempRefs))throw Error("Missing a temporary reference set but the RSC response returned a temporary reference. Pass a temporaryReference option with the set that was used with the reply.");return r.get(n);case"Q":return q(r,i=i.slice(2),n,a,G);case"W":return q(r,i=i.slice(2),n,a,J);case"B":return q(r,i=i.slice(2),n,a,V);case"K":return q(r,i=i.slice(2),n,a,X);case"Z":return ei();case"i":return q(r,i=i.slice(2),n,a,z);case"I":return 1/0;case"-":return"$-0"===i?-0:-1/0;case"N":return NaN;case"u":return;case"D":return new Date(Date.parse(i.slice(2)));case"n":return BigInt(i.slice(2));default:return q(r,i=i.slice(1),n,a,Y)}}return i}if("object"==typeof t&&null!==t){if(t[0]===d){if(e={$$typeof:d,type:t[1],key:t[2],ref:null,props:t[3]},null!==U){if(U=(t=U).parent,t.errored)e=W(e=new O("rejected",null,t.value,s));else if(0<t.deps){var o=new O("blocked",null,null,s);t.value=e,t.chunk=o,e=W(o)}}}else e=t;return e}return t})}function ee(e,t,r){var n=e._chunks,a=n.get(t);a&&"pending"!==a.status?a.reason.enqueueValue(r):n.set(t,new O("fulfilled",r,null,e))}function et(e,t,r,n){var a=e._chunks,i=a.get(t);i?"pending"===i.status&&(e=i.value,i.status="fulfilled",i.value=r,i.reason=n,null!==e&&k(e,i.value)):a.set(t,new O("fulfilled",r,n,e))}function er(e,t,r){var n=null;r=new ReadableStream({type:r,start:function(e){n=e}});var a=null;et(e,t,r,{enqueueValue:function(e){null===a?n.enqueue(e):a.then(function(){n.enqueue(e)})},enqueueModel:function(t){if(null===a){var r=new O("resolved_model",t,null,e);$(r),"fulfilled"===r.status?n.enqueue(r.value):(r.then(function(e){return n.enqueue(e)},function(e){return n.error(e)}),a=r)}else{r=a;var i=T(e);i.then(function(e){return n.enqueue(e)},function(e){return n.error(e)}),a=i,r.then(function(){a===i&&(a=null),D(i,t)})}},close:function(){if(null===a)n.close();else{var e=a;a=null,e.then(function(){return n.close()})}},error:function(e){if(null===a)n.error(e);else{var t=a;a=null,t.then(function(){return n.error(e)})}}})}function en(){return this}function ea(e,t,r){var n=[],a=!1,i=0,o={};o[h]=function(){var t,r=0;return(t={next:t=function(t){if(void 0!==t)throw Error("Values cannot be passed to next() of AsyncIterables passed to Client Components.");if(r===n.length){if(a)return new O("fulfilled",{done:!0,value:void 0},null,e);n[r]=T(e)}return n[r++]}})[h]=en,t},et(e,t,r?o[h]():o,{enqueueValue:function(t){if(i===n.length)n[i]=new O("fulfilled",{done:!1,value:t},null,e);else{var r=n[i],a=r.value,o=r.reason;r.status="fulfilled",r.value={done:!1,value:t},null!==a&&C(r,a,o)}i++},enqueueModel:function(t){i===n.length?n[i]=N(e,t,!1):I(n[i],t,!1),i++},close:function(t){for(a=!0,i===n.length?n[i]=N(e,t,!0):I(n[i],t,!0),i++;i<n.length;)I(n[i++],'"$undefined"',!0)},error:function(t){for(a=!0,i===n.length&&(n[i]=T(e));i<n.length;)j(n[i++],t)}})}function ei(){var e=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");return e.stack="Error: "+e.message,e}function eo(e,t){for(var r=e.length,n=t.length,a=0;a<r;a++)n+=e[a].byteLength;n=new Uint8Array(n);for(var i=a=0;i<r;i++){var o=e[i];n.set(o,a),a+=o.byteLength}return n.set(t,a),n}function es(e,t,r,n,a,i){ee(e,t,a=new a((r=0===r.length&&0==n.byteOffset%i?n:eo(r,n)).buffer,r.byteOffset,r.byteLength/i))}function ec(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.")}function eu(e){return new Q(e.serverConsumerManifest.moduleMap,e.serverConsumerManifest.serverModuleMap,e.serverConsumerManifest.moduleLoading,ec,e.encodeFormAction,"string"==typeof e.nonce?e.nonce:void 0,e&&e.temporaryReferences?e.temporaryReferences:void 0)}function el(e,t){function r(t){H(e,t)}var n=t.getReader();n.read().then(function t(i){var o=i.value;if(i.done)H(e,Error("Connection closed."));else{var s=0,u=e._rowState;i=e._rowID;for(var d=e._rowTag,f=e._rowLength,p=e._buffer,h=o.length;s<h;){var b=-1;switch(u){case 0:58===(b=o[s++])?u=1:i=i<<4|(96<b?b-87:b-48);continue;case 1:84===(u=o[s])||65===u||79===u||111===u||85===u||83===u||115===u||76===u||108===u||71===u||103===u||77===u||109===u||86===u?(d=u,u=2,s++):64<u&&91>u||35===u||114===u||120===u?(d=u,u=3,s++):(d=0,u=3);continue;case 2:44===(b=o[s++])?u=4:f=f<<4|(96<b?b-87:b-48);continue;case 3:b=o.indexOf(10,s);break;case 4:(b=s+f)>o.length&&(b=-1)}var g=o.byteOffset+s;if(-1<b)(function(e,t,r,n,i){switch(r){case 65:ee(e,t,eo(n,i).buffer);return;case 79:es(e,t,n,i,Int8Array,1);return;case 111:ee(e,t,0===n.length?i:eo(n,i));return;case 85:es(e,t,n,i,Uint8ClampedArray,1);return;case 83:es(e,t,n,i,Int16Array,2);return;case 115:es(e,t,n,i,Uint16Array,2);return;case 76:es(e,t,n,i,Int32Array,4);return;case 108:es(e,t,n,i,Uint32Array,4);return;case 71:es(e,t,n,i,Float32Array,4);return;case 103:es(e,t,n,i,Float64Array,8);return;case 77:es(e,t,n,i,BigInt64Array,8);return;case 109:es(e,t,n,i,BigUint64Array,8);return;case 86:es(e,t,n,i,DataView,1);return}for(var o=e._stringDecoder,s="",u=0;u<n.length;u++)s+=o.decode(n[u],a);switch(n=s+=o.decode(i),r){case 73:var d=e,f=t,p=n,h=d._chunks,b=h.get(f);p=JSON.parse(p,d._fromJSON);var g=function(e,t){if(e){var r=e[t[0]];if(e=r&&r[t[2]])r=e.name;else{if(!(e=r&&r["*"]))throw Error('Could not find the module "'+t[0]+'" in the React Server Consumer Manifest. This is probably a bug in the React Server Components bundler.');r=t[2]}return 4===t.length?[e.id,e.chunks,r,1]:[e.id,e.chunks,r]}return t}(d._bundlerConfig,p);if(!function(e,t,r){if(null!==e)for(var n=1;n<t.length;n+=2){var a=l.d,i=a.X,o=e.prefix+t[n],s=e.crossOrigin;s="string"==typeof s?"use-credentials"===s?s:"":void 0,i.call(a,o,{crossOrigin:s,nonce:r})}}(d._moduleLoading,p[1],d._nonce),p=c(g)){if(b){var y=b;y.status="blocked"}else y=new O("blocked",null,null,d),h.set(f,y);p.then(function(){return M(y,g)},function(e){return j(y,e)})}else b?M(b,g):h.set(f,new O("resolved_module",g,null,d));break;case 72:switch(t=n[0],e=JSON.parse(n=n.slice(1),e._fromJSON),n=l.d,t){case"D":n.D(e);break;case"C":"string"==typeof e?n.C(e):n.C(e[0],e[1]);break;case"L":t=e[0],r=e[1],3===e.length?n.L(t,r,e[2]):n.L(t,r);break;case"m":"string"==typeof e?n.m(e):n.m(e[0],e[1]);break;case"X":"string"==typeof e?n.X(e):n.X(e[0],e[1]);break;case"S":"string"==typeof e?n.S(e):n.S(e[0],0===e[1]?void 0:e[1],3===e.length?e[2]:void 0);break;case"M":"string"==typeof e?n.M(e):n.M(e[0],e[1])}break;case 69:r=JSON.parse(n),(n=ei()).digest=r.digest,(i=(r=e._chunks).get(t))?j(i,n):r.set(t,new O("rejected",null,n,e));break;case 84:(i=(r=e._chunks).get(t))&&"pending"!==i.status?i.reason.enqueueValue(n):r.set(t,new O("fulfilled",n,null,e));break;case 78:case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");case 82:er(e,t,void 0);break;case 114:er(e,t,"bytes");break;case 88:ea(e,t,!1);break;case 120:ea(e,t,!0);break;case 67:(e=e._chunks.get(t))&&"fulfilled"===e.status&&e.reason.close(""===n?'"$undefined"':n);break;default:(i=(r=e._chunks).get(t))?D(i,n):r.set(t,new O("resolved_model",n,null,e))}})(e,i,d,p,f=new Uint8Array(o.buffer,g,b-s)),s=b,3===u&&s++,f=i=d=u=0,p.length=0;else{o=new Uint8Array(o.buffer,g,o.byteLength-s),p.push(o),f-=o.byteLength;break}}return e._rowState=u,e._rowID=i,e._rowTag=d,e._rowLength=f,n.read().then(t).catch(r)}}).catch(r)}t.createFromFetch=function(e,t){var r=eu(t);return e.then(function(e){el(r,e.body)},function(e){H(r,e)}),B(r,0)},t.createFromReadableStream=function(e,t){return el(t=eu(t),e),B(t,0)},t.createServerReference=function(e){function t(){var t=Array.prototype.slice.call(arguments);return ec(e,t)}return E(t,e,null,void 0),t},t.createTemporaryReferenceSet=function(){return new Map},t.encodeReply=function(e,t){return new Promise(function(r,n){var a=v(e,"",t&&t.temporaryReferences?t.temporaryReferences:void 0,r,n);if(t&&t.signal){var i=t.signal;if(i.aborted)a(i.reason);else{var o=function(){a(i.reason),i.removeEventListener("abort",o)};i.addEventListener("abort",o)}}})},t.registerServerReference=function(e,t,r){return E(e,t,null,r),e}},35333:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return a}});let n=r(4e4);function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:i}=(0,n.parsePath)(e);return""+t+r+a+i}},36376:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return C},CACHE_ONE_YEAR:function(){return E},DOT_NEXT_ALIAS:function(){return T},ESLINT_DEFAULT_DIRS:function(){return z},GSP_NO_RETURNED_VALUE:function(){return F},GSSP_COMPONENT_MEMBER_ERROR:function(){return J},GSSP_NO_RETURNED_VALUE:function(){return q},INFINITE_CACHE:function(){return S},INSTRUMENTATION_HOOK_FILENAME:function(){return O},MATCHED_PATH_HEADER:function(){return a},MIDDLEWARE_FILENAME:function(){return R},MIDDLEWARE_LOCATION_REGEXP:function(){return A},NEXT_BODY_SUFFIX:function(){return h},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return x},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return g},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return y},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return w},NEXT_CACHE_TAGS_HEADER:function(){return b},NEXT_CACHE_TAG_MAX_ITEMS:function(){return v},NEXT_CACHE_TAG_MAX_LENGTH:function(){return _},NEXT_DATA_SUFFIX:function(){return f},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return p},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return m},NON_STANDARD_NODE_ENV:function(){return V},PAGES_DIR_ALIAS:function(){return P},PRERENDER_REVALIDATE_HEADER:function(){return i},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return $},ROOT_DIR_ALIAS:function(){return k},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return U},RSC_ACTION_ENCRYPTION_ALIAS:function(){return M},RSC_ACTION_PROXY_ALIAS:function(){return I},RSC_ACTION_VALIDATE_ALIAS:function(){return N},RSC_CACHE_WRAPPER_ALIAS:function(){return D},RSC_MOD_REF_PROXY_ALIAS:function(){return j},RSC_PREFETCH_SUFFIX:function(){return s},RSC_SEGMENTS_DIR_SUFFIX:function(){return c},RSC_SEGMENT_SUFFIX:function(){return u},RSC_SUFFIX:function(){return l},SERVER_PROPS_EXPORT_ERROR:function(){return K},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return H},SERVER_PROPS_SSG_CONFLICT:function(){return W},SERVER_RUNTIME:function(){return Y},SSG_FALLBACK_EXPORT_ERROR:function(){return X},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return L},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return B},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return G},WEBPACK_LAYERS:function(){return Q},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",n="nxtI",a="x-matched-path",i="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s=".prefetch.rsc",c=".segments",u=".segment.rsc",l=".rsc",d=".action",f=".json",p=".meta",h=".body",b="x-next-cache-tags",g="x-next-revalidated-tags",y="x-next-revalidate-tag-token",m="next-resume",v=128,_=256,w=1024,x="_N_T_",E=31536e3,S=0xfffffffe,R="middleware",A=`(?:src/)?${R}`,O="instrumentation",P="private-next-pages",T="private-dot-next",k="private-next-root-dir",C="private-next-app-dir",j="next/dist/build/webpack/loaders/next-flight-loader/module-proxy",N="private-next-rsc-action-validate",I="private-next-rsc-server-reference",D="private-next-rsc-cache-wrapper",M="private-next-rsc-action-encryption",U="private-next-rsc-action-client-wrapper",$="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",L="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",H="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",W="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",B="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",K="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",F="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",q="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",G="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",J="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",V='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',X="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",z=["app","pages","components","lib","src"],Y={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Z={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Q={...Z,GROUP:{builtinReact:[Z.reactServerComponents,Z.actionBrowser],serverOnly:[Z.reactServerComponents,Z.actionBrowser,Z.instrument,Z.middleware],neutralTarget:[Z.apiNode,Z.apiEdge],clientOnly:[Z.serverSideRendering,Z.appPagesBrowser],bundled:[Z.reactServerComponents,Z.actionBrowser,Z.serverSideRendering,Z.appPagesBrowser,Z.shared,Z.instrument,Z.middleware],appPages:[Z.reactServerComponents,Z.serverSideRendering,Z.appPagesBrowser,Z.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},37317:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getClientComponentLoaderMetrics:function(){return o},wrapClientComponentLoader:function(){return i}});let r=0,n=0,a=0;function i(e){return"performance"in globalThis?{require:(...t)=>{let i=performance.now();0===r&&(r=i);try{return a+=1,e.__next_app__.require(...t)}finally{n+=performance.now()-i}},loadChunk:(...t)=>{let r=performance.now(),a=e.__next_app__.loadChunk(...t);return a.finally(()=>{n+=performance.now()-r}),a}}:e.__next_app__}function o(e={}){let t=0===r?void 0:{clientComponentLoadStart:r,clientComponentLoadTimes:n,clientComponentLoadCount:a};return e.reset&&(r=0,n=0,a=0),t}},37461:(e,t)=>{"use strict";function r(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isHangingPromiseRejectionError:function(){return r},makeHangingPromise:function(){return o}});let n="HANGING_PROMISE_REJECTION";class a extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=n}}let i=new WeakMap;function o(e,t){if(e.aborted)return Promise.reject(new a(t));{let r=new Promise((r,n)=>{let o=n.bind(null,new a(t)),s=i.get(e);if(s)s.push(o);else{let t=[o];i.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(s),r}}function s(){}},38248:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return a}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39413:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},39999:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return d}});let n=r(14659),a=r(49761),i=r(40857),o=r(10492),s=r(14659),c=Symbol("internal response"),u=new Set([301,302,303,307,308]);function l(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,a]of e.request.headers)t.set("x-middleware-request-"+n,a),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class d extends Response{constructor(e,t={}){super(e,t);let r=this.headers,u=new Proxy(new s.ResponseCookies(r),{get(e,a,i){switch(a){case"delete":case"set":return(...i)=>{let o=Reflect.apply(e[a],e,i),c=new Headers(r);return o instanceof s.ResponseCookies&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,n.stringifyCookie)(e)).join(",")),l(t,c),o};default:return o.ReflectAdapter.get(e,a,i)}}});this[c]={cookies:u,url:t.url?new a.NextURL(t.url,{headers:(0,i.toNodeOutgoingHttpHeaders)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[c].cookies}static json(e,t){let r=Response.json(e,t);return new d(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!u.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},a=new Headers(null==n?void 0:n.headers);return a.set("Location",(0,i.validateURL)(e)),new d(null,{...n,headers:a,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,i.validateURL)(e)),l(t,r),new d(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),l(e,t),new d(null,{...e,headers:t})}}},4e4:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},40193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"connection",{enumerable:!0,get:function(){return u}});let n=r(29294),a=r(63033),i=r(75124),o=r(66224),s=r(37461),c=r(16296);function u(){let e=n.workAsyncStorage.getStore(),t=a.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type)return(0,s.makeHangingPromise)(t.renderSignal,"`connection()`");else"prerender-ppr"===t.type?(0,i.postponeWithTracking)(e.route,"connection",t.dynamicTracking):"prerender-legacy"===t.type&&(0,i.throwToInterruptStaticGeneration)("connection",e,t);(0,i.trackDynamicDataInDynamicRender)(e,t)}return Promise.resolve(void 0)}},40857:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return a},normalizeNextQueryParam:function(){return c},splitCookiesString:function(){return i},toNodeOutgoingHttpHeaders:function(){return o},validateURL:function(){return s}});let n=r(36376);function a(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function i(e){var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function o(e){let t={},r=[];if(e)for(let[n,a]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...i(a)),t[n]=1===r.length?r[0]:r):t[n]=a;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function c(e){for(let t of[n.NEXT_QUERY_PARAM_PREFIX,n.NEXT_INTERCEPTION_MARKER_PREFIX])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}},42087:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{decryptActionBoundArgs:function(){return b},encryptActionBoundArgs:function(){return h}}),r(54387);let n=r(74932),a=r(64390),i=r(34360),o=r(68676),s=r(63033),c=r(75124),u=function(e){return e&&e.__esModule?e:{default:e}}(r(7153)),l=new TextEncoder,d=new TextDecoder;async function f(e,t){let r=await (0,o.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=atob(t),a=n.slice(0,16),i=n.slice(16),s=d.decode(await (0,o.decrypt)(r,(0,o.stringToUint8Array)(a),(0,o.stringToUint8Array)(i)));if(!s.startsWith(e))throw Object.defineProperty(Error("Invalid Server Action payload: failed to decrypt."),"__NEXT_ERROR_CODE",{value:"E191",enumerable:!1,configurable:!0});return s.slice(e.length)}async function p(e,t){let r=await (0,o.getActionEncryptionKey)();if(void 0===r)throw Object.defineProperty(Error("Missing encryption key for Server Action. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E65",enumerable:!1,configurable:!0});let n=new Uint8Array(16);s.workUnitAsyncStorage.exit(()=>crypto.getRandomValues(n));let a=(0,o.arrayBufferToString)(n.buffer),i=await (0,o.encrypt)(r,n,l.encode(e+t));return btoa(a+(0,o.arrayBufferToString)(i))}let h=u.default.cache(async function e(t,...r){let{clientModules:a}=(0,o.getClientReferenceManifestForRsc)(),u=Error();Error.captureStackTrace(u,e);let l=!1,d=s.workUnitAsyncStorage.getStore(),f=(null==d?void 0:d.type)==="prerender"?(0,c.createHangingInputAbortSignal)(d):void 0,h=await (0,i.streamToString)((0,n.renderToReadableStream)(r,a,{signal:f,onError(e){(null==f||!f.aborted)&&(l||(l=!0,u.message=e instanceof Error?e.message:String(e)))}}),f);if(l)throw u;if(!d)return p(t,h);let b=(0,s.getPrerenderResumeDataCache)(d),g=(0,s.getRenderResumeDataCache)(d),y=t+h,m=(null==b?void 0:b.encryptedBoundArgs.get(y))??(null==g?void 0:g.encryptedBoundArgs.get(y));if(m)return m;let v="prerender"===d.type?d.cacheSignal:void 0;null==v||v.beginRead();let _=await p(t,h);return null==v||v.endRead(),null==b||b.encryptedBoundArgs.set(y,_),_});async function b(e,t){let r,n=await t,i=s.workUnitAsyncStorage.getStore();if(i){let t="prerender"===i.type?i.cacheSignal:void 0,a=(0,s.getPrerenderResumeDataCache)(i),o=(0,s.getRenderResumeDataCache)(i);(r=(null==a?void 0:a.decryptedBoundArgs.get(n))??(null==o?void 0:o.decryptedBoundArgs.get(n)))||(null==t||t.beginRead(),r=await f(e,n),null==t||t.endRead(),null==a||a.decryptedBoundArgs.set(n,r))}else r=await f(e,n);let{edgeRscModuleMapping:c,rscModuleMapping:u}=(0,o.getClientReferenceManifestForRsc)();return await (0,a.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(l.encode(r)),(null==i?void 0:i.type)==="prerender"?i.renderSignal.aborted?e.close():i.renderSignal.addEventListener("abort",()=>e.close(),{once:!0}):e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:u,serverModuleMap:(0,o.getServerModuleMap)()}})}},42383:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return o}});let n=r(62736),a=r(68002),i=r(19186);function o(e,t){var r,o;let{basePath:s,i18n:c,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},l={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};s&&(0,i.pathHasPrefix)(l.pathname,s)&&(l.pathname=(0,a.removePathPrefix)(l.pathname,s),l.basePath=s);let d=l.pathname;if(l.pathname.startsWith("/_next/data/")&&l.pathname.endsWith(".json")){let e=l.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");l.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(l.pathname=d)}if(c){let e=t.i18nProvider?t.i18nProvider.analyze(l.pathname):(0,n.normalizeLocalePath)(l.pathname,c.locales);l.locale=e.detectedLocale,l.pathname=null!=(o=e.pathname)?o:l.pathname,!e.detectedLocale&&l.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,n.normalizeLocalePath)(d,c.locales)).detectedLocale&&(l.locale=e.detectedLocale)}return l}},42503:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ENCODED_TAGS",{enumerable:!0,get:function(){return r}});let r={OPENING:{HTML:new Uint8Array([60,104,116,109,108]),BODY:new Uint8Array([60,98,111,100,121])},CLOSED:{HEAD:new Uint8Array([60,47,104,101,97,100,62]),BODY:new Uint8Array([60,47,98,111,100,121,62]),HTML:new Uint8Array([60,47,104,116,109,108,62]),BODY_AND_HTML:new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62])}}},42753:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>l,routeModule:()=>f,tree:()=>u});var n=r(65239),a=r(48088),i=r(88170),o=r.n(i),s=r(30893),c={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);r.d(t,c);let u={children:["",{children:["dashboard",{children:["notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26861)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\notifications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83249)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\notifications\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/notifications/page",pathname:"/dashboard/notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},44234:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(r(20464),t)},44334:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BubbledError:function(){return f},SpanKind:function(){return l},SpanStatusCode:function(){return u},getTracer:function(){return w},isBubbledError:function(){return p}});let a=r(2720),i=r(23879);try{n=r(73214)}catch(e){n=r(73214)}let{context:o,propagation:s,trace:c,SpanStatusCode:u,SpanKind:l,ROOT_CONTEXT:d}=n;class f extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}function p(e){return"object"==typeof e&&null!==e&&e instanceof f}let h=(e,t)=>{p(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:u.ERROR,message:null==t?void 0:t.message})),e.end()},b=new Map,g=n.createContextKey("next.rootSpanId"),y=0,m=()=>y++,v={set(e,t,r){e.push({key:t,value:r})}};class _{getTracerInstance(){return c.getTracer("next.js","0.0.1")}getContext(){return o}getTracePropagationData(){let e=o.active(),t=[];return s.inject(e,t,v),t}getActiveScopeSpan(){return c.getSpan(null==o?void 0:o.active())}withPropagatedContext(e,t,r){let n=o.active();if(c.getSpanContext(n))return t();let a=s.extract(n,e,r);return o.with(a,t)}trace(...e){var t;let[r,n,s]=e,{fn:u,options:l}="function"==typeof n?{fn:n,options:{}}:{fn:s,options:{...n}},f=l.spanName??r;if(!a.NextVanillaSpanAllowlist.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||l.hideSpan)return u();let p=this.getSpanContext((null==l?void 0:l.parentSpan)??this.getActiveScopeSpan()),y=!1;p?(null==(t=c.getSpanContext(p))?void 0:t.isRemote)&&(y=!0):(p=(null==o?void 0:o.active())??d,y=!0);let v=m();return l.attributes={"next.span_name":f,"next.span_type":r,...l.attributes},o.with(p.setValue(g,v),()=>this.getTracerInstance().startActiveSpan(f,l,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{b.delete(v),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&a.LogSpanAllowList.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};y&&b.set(v,new Map(Object.entries(l.attributes??{})));try{if(u.length>1)return u(e,t=>h(e,t));let t=u(e);if((0,i.isThenable)(t))return t.then(t=>(e.end(),t)).catch(t=>{throw h(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw h(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return a.NextVanillaSpanAllowlist.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let a=arguments.length-1,s=arguments[a];if("function"!=typeof s)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(o.active(),s);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?c.setSpan(o.active(),e):void 0}getRootSpanAttributes(){let e=o.active().getValue(g);return b.get(e)}setRootSpanAttribute(e,t){let r=o.active().getValue(g),n=b.get(r);n&&n.set(e,t)}}let w=(()=>{let e=new _;return()=>e})()},44927:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNodeNextRequest:function(){return a},isNodeNextResponse:function(){return i},isWebNextRequest:function(){return r},isWebNextResponse:function(){return n}});let r=e=>!1,n=e=>!1,a=e=>!0,i=e=>!0},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},46729:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouteKind",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},49761:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return l}});let n=r(96197),a=r(71490),i=r(18463),o=r(42383),s=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function c(e,t){return new URL(String(e).replace(s,"localhost"),t&&String(t).replace(s,"localhost"))}let u=Symbol("NextURLInternal");class l{constructor(e,t,r){let n,a;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,a=r||{}):a=r||t||{},this[u]={url:c(e,n??a.base),options:a,basePath:""},this.analyze()}analyze(){var e,t,r,a,s;let c=(0,o.getNextPathnameInfo)(this[u].url.pathname,{nextConfig:this[u].options.nextConfig,parseData:!0,i18nProvider:this[u].options.i18nProvider}),l=(0,i.getHostname)(this[u].url,this[u].options.headers);this[u].domainLocale=this[u].options.i18nProvider?this[u].options.i18nProvider.detectDomainLocale(l):(0,n.detectDomainLocale)(null==(t=this[u].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,l);let d=(null==(r=this[u].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[u].options.nextConfig)||null==(a=s.i18n)?void 0:a.defaultLocale);this[u].url.pathname=c.pathname,this[u].defaultLocale=d,this[u].basePath=c.basePath??"",this[u].buildId=c.buildId,this[u].locale=c.locale??d,this[u].trailingSlash=c.trailingSlash}formatPathname(){return(0,a.formatNextPathnameInfo)({basePath:this[u].basePath,buildId:this[u].buildId,defaultLocale:this[u].options.forceLocale?void 0:this[u].defaultLocale,locale:this[u].locale,pathname:this[u].url.pathname,trailingSlash:this[u].trailingSlash})}formatSearch(){return this[u].url.search}get buildId(){return this[u].buildId}set buildId(e){this[u].buildId=e}get locale(){return this[u].locale??""}set locale(e){var t,r;if(!this[u].locale||!(null==(r=this[u].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[u].locale=e}get defaultLocale(){return this[u].defaultLocale}get domainLocale(){return this[u].domainLocale}get searchParams(){return this[u].url.searchParams}get host(){return this[u].url.host}set host(e){this[u].url.host=e}get hostname(){return this[u].url.hostname}set hostname(e){this[u].url.hostname=e}get port(){return this[u].url.port}set port(e){this[u].url.port=e}get protocol(){return this[u].url.protocol}set protocol(e){this[u].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[u].url=c(e),this.analyze()}get origin(){return this[u].url.origin}get pathname(){return this[u].url.pathname}set pathname(e){this[u].url.pathname=e}get hash(){return this[u].url.hash}set hash(e){this[u].url.hash=e}get search(){return this[u].url.search}set search(e){this[u].url.search=e}get password(){return this[u].url.password}set password(e){this[u].url.password=e}get username(){return this[u].url.username}set username(e){this[u].url.username=e}get basePath(){return this[u].basePath}set basePath(e){this[u].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new l(String(this),this[u].options)}}},51078:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return i}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},51851:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{revalidatePath:function(){return f},revalidateTag:function(){return u},unstable_expirePath:function(){return l},unstable_expireTag:function(){return d}});let n=r(75124),a=r(17529),i=r(36376),o=r(29294),s=r(63033),c=r(38248);function u(e){return p([e],`revalidateTag ${e}`)}function l(e,t){if(e.length>i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH)return void console.warn(`Warning: expirePath received "${e}" which exceeded max length of ${i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`);let r=`${i.NEXT_CACHE_IMPLICIT_TAG_ID}${e}`;return t?r+=`${r.endsWith("/")?"":"/"}${t}`:(0,a.isDynamicRoute)(e)&&console.warn(`Warning: a dynamic page path "${e}" was passed to "expirePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/unstable_expirePath`),p([r],`unstable_expirePath ${e}`)}function d(...e){return p(e,`unstable_expireTag ${e.join(", ")}`)}function f(e,t){if(e.length>i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH)return void console.warn(`Warning: revalidatePath received "${e}" which exceeded max length of ${i.NEXT_CACHE_SOFT_TAG_MAX_LENGTH}. See more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`);let r=`${i.NEXT_CACHE_IMPLICIT_TAG_ID}${e}`;return t?r+=`${r.endsWith("/")?"":"/"}${t}`:(0,a.isDynamicRoute)(e)&&console.warn(`Warning: a dynamic page path "${e}" was passed to "revalidatePath", but the "type" parameter is missing. This has no effect by default, see more info here https://nextjs.org/docs/app/api-reference/functions/revalidatePath`),p([r],`revalidatePath ${e}`)}function p(e,t){let r=o.workAsyncStorage.getStore();if(!r||!r.incrementalCache)throw Object.defineProperty(Error(`Invariant: static generation store missing in ${t}`),"__NEXT_ERROR_CODE",{value:"E263",enumerable:!1,configurable:!0});let a=s.workUnitAsyncStorage.getStore();if(a){if("cache"===a.type)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" inside a "use cache" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E181",enumerable:!1,configurable:!0});if("unstable-cache"===a.type)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" inside a function cached with "unstable_cache(...)" which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E306",enumerable:!1,configurable:!0});if("render"===a.phase)throw Object.defineProperty(Error(`Route ${r.route} used "${t}" during render which is unsupported. To ensure revalidation is performed consistently it must always happen outside of renders and cached functions. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E7",enumerable:!1,configurable:!0});if("prerender"===a.type){let e=Object.defineProperty(Error(`Route ${r.route} used ${t} without first calling \`await connection()\`.`),"__NEXT_ERROR_CODE",{value:"E406",enumerable:!1,configurable:!0});(0,n.abortAndThrowOnSynchronousRequestDataAccess)(r.route,t,e,a)}else if("prerender-ppr"===a.type)(0,n.postponeWithTracking)(r.route,t,a.dynamicTracking);else if("prerender-legacy"===a.type){a.revalidate=0;let e=Object.defineProperty(new c.DynamicServerError(`Route ${r.route} couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.dynamicUsageDescription=t,r.dynamicUsageStack=e.stack,e}}for(let t of(r.pendingRevalidatedTags||(r.pendingRevalidatedTags=[]),e))r.pendingRevalidatedTags.includes(t)||r.pendingRevalidatedTags.push(t);r.pathWasRevalidated=!0}},54387:()=>{},54806:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"MISSING_ROOT_TAGS_ERROR",{enumerable:!0,get:function(){return r}});let r="NEXT_MISSING_ROOT_TAGS";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>u,j2:()=>f});var n=r(19443),a=r(16467),i=r(5956),o=r(10189),s=r(56056),c=r(85663);let{handlers:u,signIn:l,signOut:d,auth:f}=(0,n.Ay)({adapter:(0,a.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[s.A,(0,o.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!t||!t.password||!await c.Ay.compare(e.password,t.password))return null;return{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,n=t.pathname.startsWith("/dashboard"),a=t.pathname.startsWith("/manuscripts"),i=t.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",t.pathname),!n&&!a&&!i||r},session:async({session:e,token:t})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",t),t?.sub&&(e.user.id=t.sub,t.name&&(e.user.name=t.name),t.email&&(e.user.email=t.email),t.picture&&(e.user.image=t.picture),t.role&&(e.user.role=t.role)),e),jwt:async({token:e,user:t,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",t),console.log("\uD83D\uDD10 JWT callback - account:",r),t&&(e.sub=t.id,e.name=t.name,e.email=t.email,e.picture=t.image,e.role=t.role),e)}})},59311:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return a},RedirectType:function(){return i},isRedirectError:function(){return o}});let n=r(18389),a="NEXT_REDIRECT";var i=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===a&&("replace"===i||"push"===i)&&"string"==typeof o&&!isNaN(s)&&s in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},60358:(e,t,r)=>{Promise.resolve().then(r.bind(r,84168))},61096:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rootParams",{enumerable:!0,get:function(){return l}});let n=r(23302),a=r(75124),i=r(29294),o=r(63033),s=r(37461),c=r(51078),u=new WeakMap;async function l(){let e=i.workAsyncStorage.getStore();if(!e)throw Object.defineProperty(new n.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=o.workUnitAsyncStorage.getStore();if(!t)throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){let n=t.fallbackRouteParams;if(n){let f=!1;for(let t in e)if(n.has(t)){f=!0;break}if(f){if("prerender"===r.type){let t=u.get(e);if(t)return t;let n=(0,s.makeHangingPromise)(r.renderSignal,"`unstable_rootParams`");return u.set(e,n),n}var i=e,o=n,l=t,d=r;let f=u.get(i);if(f)return f;let p={...i},h=Promise.resolve(p);return u.set(i,h),Object.keys(i).forEach(e=>{c.wellKnownProperties.has(e)||(o.has(e)?Object.defineProperty(p,e,{get(){let t=(0,c.describeStringPropertyAccess)("unstable_rootParams",e);"prerender-ppr"===d.type?(0,a.postponeWithTracking)(l.route,t,d.dynamicTracking):(0,a.throwToInterruptStaticGeneration)(t,l,d)},enumerable:!0}):h[e]=i[e])}),h}}return Promise.resolve(e)}(t.rootParams,e,t);default:return Promise.resolve(t.rootParams)}}},62736:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return n}});let r=new WeakMap;function n(e,t){let n;if(!t)return{pathname:e};let a=r.get(t);a||(a=t.map(e=>e.toLowerCase()),r.set(t,a));let i=e.split("/",2);if(!i[1])return{pathname:e};let o=i[1].toLowerCase(),s=a.indexOf(o);return s<0?{pathname:e}:(n=t[s],{pathname:e=e.slice(n.length+1)||"/",detectedLocale:n})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64043:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isBot:function(){return a},userAgent:function(){return o},userAgentFromString:function(){return i}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(12130));function a(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}function i(e){return{...(0,n.default)(e),isBot:void 0!==e&&a(e)}}function o({headers:e}){return i(e.get("user-agent")||void 0)}},64390:(e,t,r)=>{"use strict";e.exports=r(34821)},64665:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return c.unstable_rethrow}});let n=r(68022),a=r(59311),i=r(86834),o=r(91777),s=r(24796),c=r(81452);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class l extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66224:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return a}});let r="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=r}}function a(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66455:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return i}});let n=r(35333),a=r(19186);function i(e,t,r,i){if(!t||t===r)return e;let o=e.toLowerCase();return!i&&((0,a.pathHasPrefix)(o,"/api")||(0,a.pathHasPrefix)(o,"/"+t.toLowerCase()))?e:(0,n.addPathPrefix)(e,"/"+t)}},68002:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return a}});let n=r(19186);function a(e,t){if(!(0,n.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},68022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return l},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return s}});let n=r(18389),a=r(59311),i=r(19121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function s(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){throw void 0===t&&(t=a.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function l(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68676:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{arrayBufferToString:function(){return s},decrypt:function(){return l},encrypt:function(){return u},getActionEncryptionKey:function(){return b},getClientReferenceManifestForRsc:function(){return h},getServerModuleMap:function(){return p},setReferenceManifestsSingleton:function(){return f},stringToUint8Array:function(){return c}});let a=r(23302),i=r(79535),o=r(29294);function s(e){let t=new Uint8Array(e),r=t.byteLength;if(r<65535)return String.fromCharCode.apply(null,t);let n="";for(let e=0;e<r;e++)n+=String.fromCharCode(t[e]);return n}function c(e){let t=e.length,r=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);return r}function u(e,t,r){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,r)}function l(e,t,r){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,r)}let d=Symbol.for("next.server.action-manifests");function f({page:e,clientReferenceManifest:t,serverActionsManifest:r,serverModuleMap:n}){var a;let o=null==(a=globalThis[d])?void 0:a.clientReferenceManifestsPerPage;globalThis[d]={clientReferenceManifestsPerPage:{...o,[(0,i.normalizeAppPath)(e)]:t},serverActionsManifest:r,serverModuleMap:n}}function p(){let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});return e.serverModuleMap}function h(){let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let{clientReferenceManifestsPerPage:t}=e,r=o.workAsyncStorage.getStore();if(!r){var n=t;let e=Object.values(n),r={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let t of e)r.clientModules={...r.clientModules,...t.clientModules},r.edgeRscModuleMapping={...r.edgeRscModuleMapping,...t.edgeRscModuleMapping},r.rscModuleMapping={...r.rscModuleMapping,...t.rscModuleMapping};return r}let i=t[r.route];if(!i)throw Object.defineProperty(new a.InvariantError(`Missing Client Reference Manifest for ${r.route}.`),"__NEXT_ERROR_CODE",{value:"E570",enumerable:!1,configurable:!0});return i}async function b(){if(n)return n;let e=globalThis[d];if(!e)throw Object.defineProperty(new a.InvariantError("Missing manifest for Server Actions."),"__NEXT_ERROR_CODE",{value:"E606",enumerable:!1,configurable:!0});let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Object.defineProperty(new a.InvariantError("Missing encryption key for Server Actions"),"__NEXT_ERROR_CODE",{value:"E571",enumerable:!1,configurable:!0});return n=await crypto.subtle.importKey("raw",c(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},68790:(e,t,r)=>{"use strict";e.exports=r(65239).vendored["react-rsc"].ReactDOM},70388:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"URLPattern",{enumerable:!0,get:function(){return r}});let r="undefined"==typeof URLPattern?void 0:URLPattern},70700:(e,t,r)=>{"use strict";let n,a,i,o,s;r.r(t),r.d(t,{"00b7f0d14f6ea68729c29c773d01151c448d4354ca":()=>oF,"00fbe3ba0344bf0a5130669dd2edd62442ad81bf23":()=>oq,"401017a333c37c4c5862c5d6321d7519f5e672aaaa":()=>oK});var c={};r.r(c),r.d(c,{q:()=>tq,l:()=>tV});var u=r(91199);r(42087);var l=function(e,t,r,n,a){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,r):a?a.value=r:t.set(e,r),r},d=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function f(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class p{constructor(e,t,r){if(rv.add(this),r_.set(this,{}),rw.set(this,void 0),rx.set(this,void 0),l(this,rx,r,"f"),l(this,rw,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(d(this,r_,"f")[e]=r)}get value(){return Object.keys(d(this,r_,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>d(this,r_,"f")[e]).join("")}chunk(e,t){let r=d(this,rv,"m",rS).call(this);for(let n of d(this,rv,"m",rE).call(this,{name:d(this,rw,"f").name,value:e,options:{...d(this,rw,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(d(this,rv,"m",rS).call(this))}}r_=new WeakMap,rw=new WeakMap,rx=new WeakMap,rv=new WeakSet,rE=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return d(this,r_,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,a=e.value.substr(3936*n,3936);r.push({...e,name:t,value:a}),d(this,r_,"f")[t]=a}return d(this,rx,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},rS=function(){let e={};for(let t in d(this,r_,"f"))delete d(this,r_,"f")?.[t],e[t]={name:t,value:"",options:{...d(this,rw,"f").options,maxAge:0}};return e};class h extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class b extends h{}b.kind="signIn";class g extends h{}g.type="AdapterError";class y extends h{}y.type="AccessDenied";class m extends h{}m.type="CallbackRouteError";class v extends h{}v.type="ErrorPageLoop";class _ extends h{}_.type="EventError";class w extends h{}w.type="InvalidCallbackUrl";class x extends b{constructor(){super(...arguments),this.code="credentials"}}x.type="CredentialsSignin";class E extends h{}E.type="InvalidEndpoints";class S extends h{}S.type="InvalidCheck";class R extends h{}R.type="JWTSessionError";class A extends h{}A.type="MissingAdapter";class O extends h{}O.type="MissingAdapterMethods";class P extends h{}P.type="MissingAuthorize";class T extends h{}T.type="MissingSecret";class k extends b{}k.type="OAuthAccountNotLinked";class C extends b{}C.type="OAuthCallbackError";class j extends h{}j.type="OAuthProfileParseError";class N extends h{}N.type="SessionTokenError";class I extends b{}I.type="OAuthSignInError";class D extends b{}D.type="EmailSignInError";class M extends h{}M.type="SignOutError";class U extends h{}U.type="UnknownAction";class $ extends h{}$.type="UnsupportedStrategy";class L extends h{}L.type="InvalidProvider";class H extends h{}H.type="UntrustedHost";class W extends h{}W.type="Verification";class B extends b{}B.type="MissingCSRF";let K=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class F extends h{}F.type="DuplicateConditionalUI";class q extends h{}q.type="MissingWebAuthnAutocomplete";class G extends h{}G.type="WebAuthnVerificationError";class J extends b{}J.type="AccountNotLinked";class V extends h{}V.type="ExperimentalFeatureNotEnabled";let X=!1;function z(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let Y=!1,Z=!1,Q=!1,ee=["createVerificationToken","useVerificationToken","getUserByEmail"],et=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],er=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"];var en=r(55511);let ea=(e,t,r,n,a)=>{let i=parseInt(e.substr(3),10)>>3||20,o=(0,en.createHmac)(e,r.byteLength?r:new Uint8Array(i)).update(t).digest(),s=Math.ceil(a/i),c=new Uint8Array(i*s+n.byteLength+1),u=0,l=0;for(let t=1;t<=s;t++)c.set(n,l),c[l+n.byteLength]=t,c.set((0,en.createHmac)(e,o).update(c.subarray(u,l+n.byteLength+1)).digest(),l),u=l,l+=i;return c.slice(0,a)};"function"!=typeof en.hkdf||process.versions.electron||(n=async(...e)=>new Promise((t,r)=>{en.hkdf(...e,(e,n)=>{e?r(e):t(new Uint8Array(n))})}));let ei=async(e,t,r,a,i)=>(n||ea)(e,t,r,a,i);function eo(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function es(e,t,r,n,a){return ei(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=eo(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),eo(r,"salt"),function(e){let t=eo(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(a,e))}let ec=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},eu=new TextEncoder,el=new TextDecoder;function ed(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function ef(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function ep(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return ef(r,t,0),ef(r,e%0x100000000,4),r}function eh(e){let t=new Uint8Array(4);return ef(t,e),t}function eb(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:el.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=el.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function eg(e){let t=e;return("string"==typeof t&&(t=eu.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class ey extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class em extends ey{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ev extends ey{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class e_ extends ey{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class ew extends ey{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class ex extends ey{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class eE extends ey{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class eS extends ey{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class eR extends ey{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class eA extends ey{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function eO(e){if(!eP(e))throw Error("CryptoKey instance expected")}function eP(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function eT(e){return e?.[Symbol.toStringTag]==="KeyObject"}let ek=e=>eP(e)||eT(e),eC=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function ej(e){return eC(e)&&"string"==typeof e.kty}function eN(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let eI=(e,...t)=>eN("Key must be ",e,...t);function eD(e,t,...r){return eN(`Key for the ${e} algorithm must be `,t,...r)}async function eM(e){if(eT(e))if("secret"!==e.type)return e.export({format:"jwk"});else e=e.export();if(e instanceof Uint8Array)return{kty:"oct",k:eg(e)};if(!eP(e))throw TypeError(eI(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:a,...i}=await crypto.subtle.exportKey("jwk",e);return i}async function eU(e){return eM(e)}let e$=(e,t)=>{if("string"!=typeof e||!e)throw new eR(`${t} missing or invalid`)};async function eL(e,t){let r,n;if(ej(e))r=e;else if(ek(e))r=await eU(e);else throw TypeError(eI(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":e$(r.crv,'"crv" (Curve) Parameter'),e$(r.x,'"x" (X Coordinate) Parameter'),e$(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":e$(r.crv,'"crv" (Subtype of Key Pair) Parameter'),e$(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":e$(r.e,'"e" (Exponent) Parameter'),e$(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":e$(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new ew('"kty" (Key Type) Parameter missing or unsupported')}let a=eu.encode(JSON.stringify(n));return eg(await ec(t,a))}let eH=Symbol();function eW(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new ew(`Unsupported JWE Algorithm: ${e}`)}}let eB=e=>crypto.getRandomValues(new Uint8Array(eW(e)>>3)),eK=(e,t)=>{if(t.length<<3!==eW(e))throw new eE("Invalid Initialization Vector length")},eF=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new eE(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function eq(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function eG(e,t){return e.name===t}function eJ(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!eG(e.algorithm,"AES-GCM"))throw eq("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eq(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!eG(e.algorithm,"AES-KW"))throw eq("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw eq(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw eq("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!eG(e.algorithm,"PBKDF2"))throw eq("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!eG(e.algorithm,"RSA-OAEP"))throw eq("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw eq(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,a=r;if(a&&!n.usages.includes(a))throw TypeError(`CryptoKey does not support this operation, its usages must include ${a}.`)}async function eV(e,t,r,n,a){if(!(r instanceof Uint8Array))throw TypeError(eI(r,"Uint8Array"));let i=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(i>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,i>>3),{hash:`SHA-${i<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),u=ed(a,n,c,ep(a.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,u)).slice(0,i>>3)),iv:n}}async function eX(e,t,r,n,a){let i;r instanceof Uint8Array?i=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(eJ(r,e,"encrypt"),i=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},i,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let ez=async(e,t,r,n,a)=>{if(!eP(r)&&!(r instanceof Uint8Array))throw TypeError(eI(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?eK(e,n):n=eB(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&eF(r,parseInt(e.slice(-3),10)),eV(e,t,r,n,a);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&eF(r,parseInt(e.slice(1,4),10)),eX(e,t,r,n,a);default:throw new ew("Unsupported JWE Content Encryption Algorithm")}};function eY(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function eZ(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(eJ(e,t,r),e)}async function eQ(e,t,r){let n=await eZ(t,e,"wrapKey");eY(n,e);let a=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",a,n,"AES-KW"))}async function e0(e,t,r){let n=await eZ(t,e,"unwrapKey");eY(n,e);let a=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",a))}function e1(e){return ed(eh(e.length),e)}async function e2(e,t,r){let n=Math.ceil((t>>3)/32),a=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(eh(t+1)),n.set(e,4),n.set(r,4+e.length),a.set(await ec("sha256",n),32*t)}return a.slice(0,t>>3)}async function e5(e,t,r,n,a=new Uint8Array(0),i=new Uint8Array(0)){let o;eJ(e,"ECDH"),eJ(t,"ECDH","deriveBits");let s=ed(e1(eu.encode(r)),e1(a),e1(i),eh(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,e2(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function e6(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let e3=(e,t)=>ed(eu.encode(e),new Uint8Array([0]),t);async function e4(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new eE("PBES2 Salt Input must be 8 or more octets");let a=e3(t,e),i=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:a},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(eJ(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,i))}async function e8(e,t,r,n=2048,a=crypto.getRandomValues(new Uint8Array(16))){let i=await e4(a,e,n,t);return{encryptedKey:await eQ(e.slice(-6),i,r),p2c:n,p2s:eg(a)}}async function e9(e,t,r,n,a){let i=await e4(a,e,n,t);return e0(e.slice(-6),i,r)}let e7=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},te=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new ew(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function tt(e,t,r){return eJ(t,e,"encrypt"),e7(e,t),new Uint8Array(await crypto.subtle.encrypt(te(e),t,r))}async function tr(e,t,r){return eJ(t,e,"decrypt"),e7(e,t),new Uint8Array(await crypto.subtle.decrypt(te(e),t,r))}let tn=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new ew('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ew('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new ew('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new ew('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},ta=async(e,t,r,n=!1)=>{let i=(a||=new WeakMap).get(e);if(i?.[r])return i[r];let o=await tn({...t,alg:r});return n&&Object.freeze(e),i?i[r]=o:a.set(e,{[r]:o}),o},ti=(e,t)=>{let r,n=(a||=new WeakMap).get(e);if(n?.[t])return n[t];let i="public"===e.type,o=!!i;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,i?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[i?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,i?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[i?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[i?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,i?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:a.set(e,{[t]:r}),r},to=async(e,t)=>{if(e instanceof Uint8Array||eP(e))return e;if(eT(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return ti(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return ta(e,r,t)}if(ej(e))return e.k?eb(e.k):ta(e,e,t,!0);throw Error("unreachable")};function ts(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new ew(`Unsupported JWE Algorithm: ${e}`)}}let tc=e=>crypto.getRandomValues(new Uint8Array(ts(e)>>3));async function tu(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),a=new Uint8Array(await crypto.subtle.sign(r,n,e)),i=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=a[s]^i[s];return 0===o}async function tl(e,t,r,n,a,i){let o,s;if(!(t instanceof Uint8Array))throw TypeError(eI(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),u=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),l=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=ed(i,n,r,ep(i.length<<3)),f=new Uint8Array((await crypto.subtle.sign("HMAC",l,d)).slice(0,c>>3));try{o=await tu(a,f)}catch{}if(!o)throw new ex;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},u,r))}catch{}if(!s)throw new ex;return s}async function td(e,t,r,n,a,i){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(eJ(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},o,ed(r,a)))}catch{throw new ex}}let tf=async(e,t,r,n,a,i)=>{if(!eP(t)&&!(t instanceof Uint8Array))throw TypeError(eI(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new eE("JWE Initialization Vector missing");if(!a)throw new eE("JWE Authentication Tag missing");switch(eK(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&eF(t,parseInt(e.slice(-3),10)),tl(e,t,r,n,a,i);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&eF(t,parseInt(e.slice(1,4),10)),td(e,t,r,n,a,i);default:throw new ew("Unsupported JWE Content Encryption Algorithm")}};async function tp(e,t,r,n){let a=e.slice(0,7),i=await ez(a,r,t,n,new Uint8Array(0));return{encryptedKey:i.ciphertext,iv:eg(i.iv),tag:eg(i.tag)}}async function th(e,t,r,n,a){return tf(e.slice(0,7),t,r,n,a,new Uint8Array(0))}let tb=async(e,t,r,n,a={})=>{let i,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(eO(r),!e6(r))throw new ew("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:u,apv:l}=a;c=a.epk?await to(a.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:f,crv:p,kty:h}=await eU(c),b=await e5(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?ts(t):parseInt(e.slice(-5,-2),10),u,l);if(o={epk:{x:d,crv:p,kty:h}},"EC"===h&&(o.epk.y=f),u&&(o.apu=eg(u)),l&&(o.apv=eg(l)),"ECDH-ES"===e){s=b;break}s=n||tc(t);let g=e.slice(-6);i=await eQ(g,b,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||tc(t),eO(r),i=await tt(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||tc(t);let{p2c:c,p2s:u}=a;({encryptedKey:i,...o}=await e8(e,r,s,c,u));break}case"A128KW":case"A192KW":case"A256KW":s=n||tc(t),i=await eQ(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||tc(t);let{iv:c}=a;({encryptedKey:i,...o}=await tp(e,r,s,c));break}default:throw new ew('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:i,parameters:o}},tg=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},ty=(e,t,r,n,a)=>{let i;if(void 0!==a.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!i.has(o))throw new ew(`Extension Header Parameter "${o}" is not recognized`);if(void 0===a[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},tm=e=>e?.[Symbol.toStringTag],tv=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},t_=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(ej(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&tv(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!ek(t))throw TypeError(eD(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${tm(t)} instances for symmetric algorithms must be of type "secret"`)}},tw=(e,t,r)=>{if(ej(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&tv(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&tv(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!ek(t))throw TypeError(eD(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${tm(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${tm(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${tm(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${tm(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${tm(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},tx=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?t_(e,t,r):tw(e,t,r)};class tE{#e;#t;#r;#n;#a;#i;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#a=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,a,i,o;if(!this.#t&&!this.#n&&!this.#r)throw new eE("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!tg(this.#t,this.#n,this.#r))throw new eE("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(ty(eE,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new ew('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:u}=s;if("string"!=typeof c||!c)throw new eE('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof u||!u)throw new eE('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#i&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);tx("dir"===c?u:c,e,"encrypt");{let a,i=await to(e,c);({cek:n,encryptedKey:r,parameters:a}=await tb(c,u,i,this.#i,this.#s)),a&&(t&&eH in t?this.#n?this.#n={...this.#n,...a}:this.setUnprotectedHeader(a):this.#t?this.#t={...this.#t,...a}:this.setProtectedHeader(a))}i=this.#t?eu.encode(eg(JSON.stringify(this.#t))):eu.encode(""),this.#a?(o=eg(this.#a),a=ed(i,eu.encode("."),eu.encode(o))):a=i;let{ciphertext:l,tag:d,iv:f}=await ez(u,this.#e,n,this.#o,a),p={ciphertext:eg(l)};return f&&(p.iv=eg(f)),d&&(p.tag=eg(d)),r&&(p.encrypted_key=eg(r)),o&&(p.aad=o),this.#t&&(p.protected=el.decode(i)),this.#r&&(p.unprotected=this.#r),this.#n&&(p.header=this.#n),p}}class tS{#c;constructor(e){this.#c=new tE(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let tR=e=>Math.floor(e.getTime()/1e3),tA=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,tO=e=>{let t,r=tA.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function tP(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let tT=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,tk=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class tC{#u;constructor(e){if(!eC(e))throw TypeError("JWT Claims Set MUST be an object");this.#u=structuredClone(e)}data(){return eu.encode(JSON.stringify(this.#u))}get iss(){return this.#u.iss}set iss(e){this.#u.iss=e}get sub(){return this.#u.sub}set sub(e){this.#u.sub=e}get aud(){return this.#u.aud}set aud(e){this.#u.aud=e}set jti(e){this.#u.jti=e}set nbf(e){"number"==typeof e?this.#u.nbf=tP("setNotBefore",e):e instanceof Date?this.#u.nbf=tP("setNotBefore",tR(e)):this.#u.nbf=tR(new Date)+tO(e)}set exp(e){"number"==typeof e?this.#u.exp=tP("setExpirationTime",e):e instanceof Date?this.#u.exp=tP("setExpirationTime",tR(e)):this.#u.exp=tR(new Date)+tO(e)}set iat(e){void 0===e?this.#u.iat=tR(new Date):e instanceof Date?this.#u.iat=tP("setIssuedAt",tR(e)):"string"==typeof e?this.#u.iat=tP("setIssuedAt",tR(new Date)+tO(e)):this.#u.iat=tP("setIssuedAt",e)}}class tj{#i;#o;#s;#t;#l;#d;#f;#p;constructor(e={}){this.#p=new tC(e)}setIssuer(e){return this.#p.iss=e,this}setSubject(e){return this.#p.sub=e,this}setAudience(e){return this.#p.aud=e,this}setJti(e){return this.#p.jti=e,this}setNotBefore(e){return this.#p.nbf=e,this}setExpirationTime(e){return this.#p.exp=e,this}setIssuedAt(e){return this.#p.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#i)throw TypeError("setContentEncryptionKey can only be called once");return this.#i=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#l=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#f=!0,this}async encrypt(e,t){let r=new tS(this.#p.data());return this.#t&&(this.#l||this.#d||this.#f)&&(this.#t={...this.#t,iss:this.#l?this.#p.iss:void 0,sub:this.#d?this.#p.sub:void 0,aud:this.#f?this.#p.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#i&&r.setContentEncryptionKey(this.#i),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function tN(e,t,r){let n;if(!eC(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return eb(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new ew('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return tn({...e,alg:t,ext:n});default:throw new ew('Unsupported "kty" (Key Type) Parameter value')}}let tI=async(e,t,r,n,a)=>{switch(e){case"dir":if(void 0!==r)throw new eE("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new eE("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,i;if(!eC(n.epk))throw new eE('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(eO(t),!e6(t))throw new ew("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await tN(n.epk,e);if(eO(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new eE('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=eb(n.apu)}catch{throw new eE("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new eE('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{i=eb(n.apv)}catch{throw new eE("Failed to base64url decode the apv")}}let s=await e5(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?ts(n.enc):parseInt(e.slice(-5,-2),10),a,i);if("ECDH-ES"===e)return s;if(void 0===r)throw new eE("JWE Encrypted Key missing");return e0(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new eE("JWE Encrypted Key missing");return eO(t),tr(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new eE("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new eE('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=a?.maxPBES2Count||1e4;if(n.p2c>o)throw new eE('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new eE('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=eb(n.p2s)}catch{throw new eE("Failed to base64url decode the p2s")}return e9(e,t,r,n.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new eE("JWE Encrypted Key missing");return e0(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,i;if(void 0===r)throw new eE("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new eE('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new eE('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=eb(n.iv)}catch{throw new eE("Failed to base64url decode the iv")}try{i=eb(n.tag)}catch{throw new eE("Failed to base64url decode the tag")}return th(e,t,r,a,i)}default:throw new ew('Invalid or unsupported "alg" (JWE Algorithm) header value')}},tD=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function tM(e,t,r){let n,a,i,o,s,c,u;if(!eC(e))throw new eE("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new eE("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new eE("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new eE("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new eE("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new eE("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new eE("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new eE("JWE AAD incorrect type");if(void 0!==e.header&&!eC(e.header))throw new eE("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!eC(e.unprotected))throw new eE("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=eb(e.protected);n=JSON.parse(el.decode(t))}catch{throw new eE("JWE Protected Header is invalid")}if(!tg(n,e.header,e.unprotected))throw new eE("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let l={...n,...e.header,...e.unprotected};if(ty(eE,new Map,r?.crit,n,l),void 0!==l.zip)throw new ew('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:f}=l;if("string"!=typeof d||!d)throw new eE("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof f||!f)throw new eE("missing JWE Encryption Algorithm (enc) in JWE Header");let p=r&&tD("keyManagementAlgorithms",r.keyManagementAlgorithms),h=r&&tD("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(p&&!p.has(d)||!p&&d.startsWith("PBES2"))throw new e_('"alg" (Algorithm) Header Parameter value not allowed');if(h&&!h.has(f))throw new e_('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{a=eb(e.encrypted_key)}catch{throw new eE("Failed to base64url decode the encrypted_key")}let b=!1;"function"==typeof t&&(t=await t(n,e),b=!0),tx("dir"===d?f:d,t,"decrypt");let g=await to(t,d);try{i=await tI(d,g,a,l,r)}catch(e){if(e instanceof TypeError||e instanceof eE||e instanceof ew)throw e;i=tc(f)}if(void 0!==e.iv)try{o=eb(e.iv)}catch{throw new eE("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=eb(e.tag)}catch{throw new eE("Failed to base64url decode the tag")}let y=eu.encode(e.protected??"");c=void 0!==e.aad?ed(y,eu.encode("."),eu.encode(e.aad)):y;try{u=eb(e.ciphertext)}catch{throw new eE("Failed to base64url decode the ciphertext")}let m={plaintext:await tf(f,i,u,o,s,c)};if(void 0!==e.protected&&(m.protectedHeader=n),void 0!==e.aad)try{m.additionalAuthenticatedData=eb(e.aad)}catch{throw new eE("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(m.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(m.unprotectedHeader=e.header),b)?{...m,key:g}:m}async function tU(e,t,r){if(e instanceof Uint8Array&&(e=el.decode(e)),"string"!=typeof e)throw new eE("Compact JWE must be a string or Uint8Array");let{0:n,1:a,2:i,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new eE("Invalid Compact JWE");let u=await tM({ciphertext:o,iv:i||void 0,protected:n,tag:s||void 0,encrypted_key:a||void 0},t,r),l={plaintext:u.plaintext,protectedHeader:u.protectedHeader};return"function"==typeof t?{...l,key:u.key}:l}async function t$(e,t,r){let n=await tU(e,t,r),a=function(e,t,r={}){let n,a;try{n=JSON.parse(el.decode(t))}catch{}if(!eC(n))throw new eS("JWT Claims Set must be a top-level JSON object");let{typ:i}=r;if(i&&("string"!=typeof e.typ||tT(e.typ)!==tT(i)))throw new em('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:u,maxTokenAge:l}=r,d=[...o];for(let e of(void 0!==l&&d.push("iat"),void 0!==u&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new em(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new em('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new em('unexpected "sub" claim value',n,"sub","check_failed");if(u&&!tk(n.aud,"string"==typeof u?[u]:u))throw new em('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":a=tO(r.clockTolerance);break;case"number":a=r.clockTolerance;break;case"undefined":a=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=r,p=tR(f||new Date);if((void 0!==n.iat||l)&&"number"!=typeof n.iat)throw new em('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new em('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>p+a)throw new em('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new em('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=p-a)throw new ev('"exp" claim timestamp check failed',n,"exp","check_failed")}if(l){let e=p-n.iat;if(e-a>("number"==typeof l?l:tO(l)))throw new ev('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-a)throw new em('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:i}=n;if(void 0!==i.iss&&i.iss!==a.iss)throw new em('replicated "iss" claim header parameter mismatch',a,"iss","mismatch");if(void 0!==i.sub&&i.sub!==a.sub)throw new em('replicated "sub" claim header parameter mismatch',a,"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(a.aud))throw new em('replicated "aud" claim header parameter mismatch',a,"aud","mismatch");let o={payload:a,protectedHeader:i};return"function"==typeof t?{...o,key:n.key}:o}let tL=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,tH=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,tW=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,tB=/^[\u0020-\u003A\u003D-\u007E]*$/,tK=Object.prototype.toString,tF=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function tq(e,t){let r=new tF,n=e.length;if(n<2)return r;let a=t?.decode||tX,i=0;do{let t=e.indexOf("=",i);if(-1===t)break;let o=e.indexOf(";",i),s=-1===o?n:o;if(t>s){i=e.lastIndexOf(";",t-1)+1;continue}let c=tG(e,i,t),u=tJ(e,t,c),l=e.slice(c,u);if(void 0===r[l]){let n=tG(e,t+1,s),i=tJ(e,s,n),o=a(e.slice(n,i));r[l]=o}i=s+1}while(i<n);return r}function tG(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function tJ(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function tV(e,t,r){let n=r?.encode||encodeURIComponent;if(!tL.test(e))throw TypeError(`argument name is invalid: ${e}`);let a=n(t);if(!tH.test(a))throw TypeError(`argument val is invalid: ${t}`);let i=e+"="+a;if(!r)return i;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);i+="; Max-Age="+r.maxAge}if(r.domain){if(!tW.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);i+="; Domain="+r.domain}if(r.path){if(!tB.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);i+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==tK.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);i+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(i+="; HttpOnly"),r.secure&&(i+="; Secure"),r.partitioned&&(i+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":i+="; Priority=Low";break;case"medium":i+="; Priority=Medium";break;case"high":i+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return i}function tX(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:tz}=c,tY=()=>Date.now()/1e3|0,tZ="A256CBC-HS512";async function tQ(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:a}=e,i=Array.isArray(r)?r:[r],o=await t1(tZ,i[0],a),s=await eL({kty:"oct",k:eg(o)},`sha${o.byteLength<<3}`);return await new tj(t).setProtectedHeader({alg:"dir",enc:tZ,kid:s}).setIssuedAt().setExpirationTime(tY()+n).setJti(crypto.randomUUID()).encrypt(o)}async function t0(e){let{token:t,secret:r,salt:n}=e,a=Array.isArray(r)?r:[r];if(!t)return null;let{payload:i}=await t$(t,async({kid:e,enc:t})=>{for(let r of a){let a=await t1(t,r,n);if(void 0===e||e===await eL({kty:"oct",k:eg(a)},`sha${a.byteLength<<3}`))return a}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[tZ,"A256GCM"]});return i}async function t1(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await es("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function t2({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:a}=e,i=n.origin;return t?i=await a.redirect({url:t,baseUrl:n.origin}):r&&(i=await a.redirect({url:r,baseUrl:n.origin})),{callbackUrl:i,callbackUrlCookie:i!==r?i:void 0}}let t5="\x1b[31m",t6="\x1b[0m",t3={error(e){let t=e instanceof h?e.type:e.name;if(console.error(`${t5}[auth][error]${t6} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${t5}[auth][cause]${t6}:`,t.stack),r&&console.error(`${t5}[auth][details]${t6}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${t6}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${t6} ${e}`,JSON.stringify(t,null,2))}};function t4(e){let t={...t3};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let t8=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:t9,l:t7}=c;async function re(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function rt(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new U("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:a}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new U(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new U(`Cannot parse action at ${e}`);let[a,i]=n;if(!t8.includes(a)||i&&!["signin","callback","webauthn-options"].includes(a))throw new U(`Cannot parse action at ${e}`);return{action:a,providerId:i}}(r.pathname,t.basePath);return{url:r,action:n,providerId:a,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await re(e):void 0,cookies:t9(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=t4(t);r.error(n),r.debug("request",e)}}function rr(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:a}=e,i=t7(r,n,a);t.has("Set-Cookie")?t.append("Set-Cookie",i):t.set("Set-Cookie",i)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function rn(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function ra(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function ri({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[a,i]=t.split("|");if(i===await rn(`${a}${e.secret}`))return{csrfTokenVerified:r&&a===n,csrfToken:a}}let a=ra(32),i=await rn(`${a}${e.secret}`);return{cookie:`${a}|${i}`,csrfToken:a}}function ro(e,t){if(!t)throw new B(`CSRF token was missing during an action ${e}`)}function rs(e){return null!==e&&"object"==typeof e}function rc(e,...t){if(!t.length)return e;let r=t.shift();if(rs(e)&&rs(r))for(let t in r)rs(r[t])?(rs(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),rc(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return rc(e,...t)}let ru=Symbol("skip-csrf-check"),rl=Symbol("return-type-raw"),rd=Symbol("custom-fetch"),rf=Symbol("conform-internal"),rp=e=>rb({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),rh=e=>rb({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function rb(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function rg(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let ry={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function rm({authOptions:e,providerId:t,action:r,url:n,cookies:a,callbackUrl:i,csrfToken:o,csrfDisabled:s,isPost:c}){var u,l;let d=t4(e),{providers:p,provider:h}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),a=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:a,...i}=t,o=a?.id??i.id,s=rc(i,a,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=a?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=rg(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=rg(e.token,e.issuer),n=rg(e.userinfo,e.issuer),a=e.checks??["pkce"];return e.redirectProxyUrl&&(a.includes("state")||a.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:a,userinfo:n,profile:e.profile??rp,account:e.account??rh}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[rd]??(e[rd]=a?.[rd]),e}return s}),i=a.find(({id:e})=>e===t);if(t&&!i){let e=a.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:a,provider:i}}({url:n,providerId:t,config:e}),b=!1;if((h?.type==="oauth"||h?.type==="oidc")&&h.redirectProxyUrl)try{b=new URL(h.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${h.redirectProxyUrl}`)}let y={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:h,cookies:rc(f(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:p,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:tQ,decode:t0,...e.jwt},events:(u=e.events??{},l=d,Object.keys(u).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=u[t];return await r(...e)}catch(e){l.error(new _(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let a=e[n];return await a(...r)}catch(r){let e=new g(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...ry,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:b,experimental:{...e.experimental}},m=[];if(s)y.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await ri({options:y,cookieValue:a?.[y.cookies.csrfToken.name],isPost:c,bodyValue:o});y.csrfToken=e,y.csrfTokenVerified=r,t&&m.push({name:y.cookies.csrfToken.name,value:t,options:y.cookies.csrfToken.options})}let{callbackUrl:v,callbackUrlCookie:w}=await t2({options:y,cookieValue:a?.[y.cookies.callbackUrl.name],paramValue:i});return y.callbackUrl=v,w&&m.push({name:y.cookies.callbackUrl.name,value:w,options:y.cookies.callbackUrl.options}),{options:y,cookies:m}}var rv,r_,rw,rx,rE,rS,rR,rA,rO,rP,rT,rk,rC,rj,rN,rI,rD={},rM=[],rU=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,r$=Array.isArray;function rL(e,t){for(var r in t)e[r]=t[r];return e}function rH(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function rW(e,t,r){var n,a,i,o={};for(i in t)"key"==i?n=t[i]:"ref"==i?a=t[i]:o[i]=t[i];if(arguments.length>2&&(o.children=arguments.length>3?rR.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===o[i]&&(o[i]=e.defaultProps[i]);return rB(e,o,n,a,null)}function rB(e,t,r,n,a){var i={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==a?++rO:a,__i:-1,__u:0};return null==a&&null!=rA.vnode&&rA.vnode(i),i}function rK(e){return e.children}function rF(e,t){this.props=e,this.context=t}function rq(e,t){if(null==t)return e.__?rq(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?rq(e):null}function rG(e){(!e.__d&&(e.__d=!0)&&rP.push(e)&&!rJ.__r++||rT!==rA.debounceRendering)&&((rT=rA.debounceRendering)||rk)(rJ)}function rJ(){var e,t,r,n,a,i,o,s;for(rP.sort(rC);e=rP.shift();)e.__d&&(t=rP.length,n=void 0,i=(a=(r=e).__v).__e,o=[],s=[],r.__P&&((n=rL({},a)).__v=a.__v+1,rA.vnode&&rA.vnode(n),rZ(r.__P,n,a,r.__n,r.__P.namespaceURI,32&a.__u?[i]:null,o,null==i?rq(a):i,!!(32&a.__u),s),n.__v=a.__v,n.__.__k[n.__i]=n,rQ(o,n,s),n.__e!=i&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),rP.length>t&&rP.sort(rC));rJ.__r=0}function rV(e,t,r,n,a,i,o,s,c,u,l){var d,f,p,h,b,g=n&&n.__k||rM,y=t.length;for(r.__d=c,function(e,t,r){var n,a,i,o,s,c=t.length,u=r.length,l=u,d=0;for(e.__k=[],n=0;n<c;n++)null!=(a=t[n])&&"boolean"!=typeof a&&"function"!=typeof a?(o=n+d,(a=e.__k[n]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?rB(null,a,null,null,null):r$(a)?rB(rK,{children:a},null,null,null):void 0===a.constructor&&a.__b>0?rB(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=e,a.__b=e.__b+1,i=null,-1!==(s=a.__i=function(e,t,r,n){var a=e.key,i=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&a==c.key&&i===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&a==c.key&&i===c.type)return s;s++}}return -1}(a,r,o,l))&&(l--,(i=r[s])&&(i.__u|=131072)),null==i||null===i.__v?(-1==s&&d--,"function"!=typeof a.type&&(a.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,a.__u|=65536))):a=e.__k[n]=null;if(l)for(n=0;n<u;n++)null!=(i=r[n])&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=rq(i)),function e(t,r,n){var a,i;if(rA.unmount&&rA.unmount(t),(a=t.ref)&&(a.current&&a.current!==t.__e||r0(a,null,r)),null!=(a=t.__c)){if(a.componentWillUnmount)try{a.componentWillUnmount()}catch(e){rA.__e(e,r)}a.base=a.__P=null}if(a=t.__k)for(i=0;i<a.length;i++)a[i]&&e(a[i],r,n||"function"!=typeof t.type);n||rH(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(i,i))}(r,t,g),c=r.__d,d=0;d<y;d++)null!=(p=r.__k[d])&&(f=-1===p.__i?rD:g[p.__i]||rD,p.__i=d,rZ(e,p,f,a,i,o,s,c,u,l),h=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&r0(f.ref,null,p),l.push(p.ref,p.__c||h,p)),null==b&&null!=h&&(b=h),65536&p.__u||f.__k===p.__k?c=function e(t,r,n){var a,i;if("function"==typeof t.type){for(a=t.__k,i=0;a&&i<a.length;i++)a[i]&&(a[i].__=t,r=e(a[i],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=rq(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(p,c,e):"function"==typeof p.type&&void 0!==p.__d?c=p.__d:h&&(c=h.nextSibling),p.__d=void 0,p.__u&=-196609);r.__d=c,r.__e=b}function rX(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||rU.test(t)?r:r+"px"}function rz(e,t,r,n,a){var i;e:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||rX(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||rX(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=r,r?n?r.u=n.u:(r.u=rj,e.addEventListener(t,i?rI:rN,i)):e.removeEventListener(t,i?rI:rN,i);else{if("http://www.w3.org/2000/svg"==a)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break e}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function rY(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=rj++;else if(t.t<r.u)return;return r(rA.event?rA.event(t):t)}}}function rZ(e,t,r,n,a,i,o,s,c,u){var l,d,f,p,h,b,g,y,m,v,_,w,x,E,S,R,A=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),i=[s=t.__e=r.__e]),(l=rA.__b)&&l(t);e:if("function"==typeof A)try{if(y=t.props,m="prototype"in A&&A.prototype.render,v=(l=A.contextType)&&n[l.__c],_=l?v?v.props.value:l.__:n,r.__c?g=(d=t.__c=r.__c).__=d.__E:(m?t.__c=d=new A(y,_):(t.__c=d=new rF(y,_),d.constructor=A,d.render=r1),v&&v.sub(d),d.props=y,d.state||(d.state={}),d.context=_,d.__n=n,f=d.__d=!0,d.__h=[],d._sb=[]),m&&null==d.__s&&(d.__s=d.state),m&&null!=A.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=rL({},d.__s)),rL(d.__s,A.getDerivedStateFromProps(y,d.__s))),p=d.props,h=d.state,d.__v=t,f)m&&null==A.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),m&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(m&&null==A.getDerivedStateFromProps&&y!==p&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(y,_),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(y,d.__s,_)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=y,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),w=0;w<d._sb.length;w++)d.__h.push(d._sb[w]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(y,d.__s,_),m&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(p,h,b)})}if(d.context=_,d.props=y,d.__P=e,d.__e=!1,x=rA.__r,E=0,m){for(d.state=d.__s,d.__d=!1,x&&x(t),l=d.render(d.props,d.state,d.context),S=0;S<d._sb.length;S++)d.__h.push(d._sb[S]);d._sb=[]}else do d.__d=!1,x&&x(t),l=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++E<25);d.state=d.__s,null!=d.getChildContext&&(n=rL(rL({},n),d.getChildContext())),m&&!f&&null!=d.getSnapshotBeforeUpdate&&(b=d.getSnapshotBeforeUpdate(p,h)),rV(e,r$(R=null!=l&&l.type===rK&&null==l.key?l.props.children:l)?R:[R],t,r,n,a,i,o,s,c,u),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),g&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=i){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;i[i.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;rA.__e(e,t,r)}else null==i&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,a,i,o,s,c){var u,l,d,f,p,h,b,g=r.props,y=t.props,m=t.type;if("svg"===m?a="http://www.w3.org/2000/svg":"math"===m?a="http://www.w3.org/1998/Math/MathML":a||(a="http://www.w3.org/1999/xhtml"),null!=i){for(u=0;u<i.length;u++)if((p=i[u])&&"setAttribute"in p==!!m&&(m?p.localName===m:3===p.nodeType)){e=p,i[u]=null;break}}if(null==e){if(null===m)return document.createTextNode(y);e=document.createElementNS(a,m,y.is&&y),s&&(rA.__m&&rA.__m(t,i),s=!1),i=null}if(null===m)g===y||s&&e.data===y||(e.data=y);else{if(i=i&&rR.call(e.childNodes),g=r.props||rD,!s&&null!=i)for(g={},u=0;u<e.attributes.length;u++)g[(p=e.attributes[u]).name]=p.value;for(u in g)if(p=g[u],"children"==u);else if("dangerouslySetInnerHTML"==u)d=p;else if(!(u in y)){if("value"==u&&"defaultValue"in y||"checked"==u&&"defaultChecked"in y)continue;rz(e,u,null,p,a)}for(u in y)p=y[u],"children"==u?f=p:"dangerouslySetInnerHTML"==u?l=p:"value"==u?h=p:"checked"==u?b=p:s&&"function"!=typeof p||g[u]===p||rz(e,u,p,g[u],a);if(l)s||d&&(l.__html===d.__html||l.__html===e.innerHTML)||(e.innerHTML=l.__html),t.__k=[];else if(d&&(e.innerHTML=""),rV(e,r$(f)?f:[f],t,r,n,"foreignObject"===m?"http://www.w3.org/1999/xhtml":a,i,o,i?i[0]:r.__k&&rq(r,0),s,c),null!=i)for(u=i.length;u--;)rH(i[u]);s||(u="value","progress"===m&&null==h?e.removeAttribute("value"):void 0===h||h===e[u]&&("progress"!==m||h)&&("option"!==m||h===g[u])||rz(e,u,h,g[u],a),u="checked",void 0!==b&&b!==e[u]&&rz(e,u,b,g[u],a))}return e}(r.__e,t,r,n,a,i,o,c,u);(l=rA.diffed)&&l(t)}function rQ(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)r0(r[n],r[++n],r[++n]);rA.__c&&rA.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){rA.__e(e,t.__v)}})}function r0(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){rA.__e(e,r)}}function r1(e,t,r){return this.constructor(e,r)}function r2(e,t){var r,n,a,i,o;r=e,rA.__&&rA.__(r,t),a=(n="function"==typeof r2)?null:r2&&r2.__k||t.__k,i=[],o=[],rZ(t,r=(!n&&r2||t).__k=rW(rK,null,[r]),a||rD,rD,t.namespaceURI,!n&&r2?[r2]:a?null:t.firstChild?rR.call(t.childNodes):null,i,!n&&r2?r2:a?a.__e:t.firstChild,n,o),rQ(i,r,o)}rR=rM.slice,rA={__e:function(e,t,r,n){for(var a,i,o;t=t.__;)if((a=t.__c)&&!a.__)try{if((i=a.constructor)&&null!=i.getDerivedStateFromError&&(a.setState(i.getDerivedStateFromError(e)),o=a.__d),null!=a.componentDidCatch&&(a.componentDidCatch(e,n||{}),o=a.__d),o)return a.__E=a}catch(t){e=t}throw e}},rO=0,rF.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=rL({},this.state),"function"==typeof e&&(e=e(rL({},r),this.props)),e&&rL(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),rG(this))},rF.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),rG(this))},rF.prototype.render=rK,rP=[],rk="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,rC=function(e,t){return e.__v.__b-t.__v.__b},rJ.__r=0,rj=0,rN=rY(!1),rI=rY(!0);var r5=/[\s\n\\/='"\0<>]/,r6=/^(xlink|xmlns|xml)([A-Z])/,r3=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,r4=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,r8=new Set(["draggable","spellcheck"]),r9=/["&<]/;function r7(e){if(0===e.length||!1===r9.test(e))return e;for(var t=0,r=0,n="",a="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:a="&quot;";break;case 38:a="&amp;";break;case 60:a="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=a,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var ne={},nt=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),nr=/[A-Z]/g;function nn(){this.__d=!0}var na=null,ni,no,ns,nc,nu={},nl=[],nd=Array.isArray,nf=Object.assign;function np(e,t){var r,n=e.type,a=!0;return e.__c?(a=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=nu),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=nf({},r.state,n.getDerivedStateFromProps(r.props,r.state)):a&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!a&&r.componentWillUpdate&&r.componentWillUpdate(),ns&&ns(e),r.render(r.props,r.state,t)}var nh=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),nb=/["&<]/,ng=0;function ny(e,t,r,n,a,i){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var u={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--ng,__i:-1,__u:0,__source:a,__self:i};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return rA.vnode&&rA.vnode(u),u}async function nm(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),i().forEach(e=>{n.searchParams.append(e.name,e.value)});let a=await fetch(n);return a.ok?a.json():void console.error("Failed to fetch options",a)}function a(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function i(){return Array.from(a().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=a();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){i().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function u(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=a();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),u()}let nv={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},n_=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function nw({html:e,title:t,status:r,cookies:n,theme:a,headTags:i}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${n_}</style><title>${t}</title>${i??""}</head><body class="__next-auth-theme-${a?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=rA.__s;rA.__s=!0,ni=rA.__b,no=rA.diffed,ns=rA.__r,nc=rA.unmount;var a=rW(rK,null);a.__k=[e];try{var i=function e(t,r,n,a,i,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?r7(t):t+"";if(nd(t)){var u,l="";i.__k=t;for(var d=0;d<t.length;d++){var f=t[d];if(null!=f&&"boolean"!=typeof f){var p,h=e(f,r,n,a,i,o,s);"string"==typeof h?l+=h:(u||(u=[]),l&&u.push(l),l="",nd(h)?(p=u).push.apply(p,h):u.push(h))}}return u?(l&&u.push(l),u):l}if(void 0!==t.constructor)return"";t.__=i,ni&&ni(t);var b=t.type,g=t.props;if("function"==typeof b){var y,m,v,_=r;if(b===rK){if("tpl"in g){for(var w="",x=0;x<g.tpl.length;x++)if(w+=g.tpl[x],g.exprs&&x<g.exprs.length){var E=g.exprs[x];if(null==E)continue;"object"==typeof E&&(void 0===E.constructor||nd(E))?w+=e(E,r,n,a,t,o,s):w+=E}return w}if("UNSTABLE_comment"in g)return"\x3c!--"+r7(g.UNSTABLE_comment)+"--\x3e";m=g.children}else{if(null!=(y=b.contextType)){var S=r[y.__c];_=S?S.props.value:y.__}var R=b.prototype&&"function"==typeof b.prototype.render;if(R)m=np(t,_),v=t.__c;else{t.__c=v={__v:t,context:_,props:t.props,setState:nn,forceUpdate:nn,__d:!0,__h:[]};for(var A=0;v.__d&&A++<25;)v.__d=!1,ns&&ns(t),m=b.call(v,g,_);v.__d=!0}if(null!=v.getChildContext&&(r=nf({},r,v.getChildContext())),R&&rA.errorBoundaries&&(b.getDerivedStateFromError||v.componentDidCatch)){m=null!=m&&m.type===rK&&null==m.key&&null==m.props.tpl?m.props.children:m;try{return e(m,r,n,a,t,o,s)}catch(i){return b.getDerivedStateFromError&&(v.__s=b.getDerivedStateFromError(i)),v.componentDidCatch&&v.componentDidCatch(i,nu),v.__d?(m=np(t,r),null!=(v=t.__c).getChildContext&&(r=nf({},r,v.getChildContext())),e(m=null!=m&&m.type===rK&&null==m.key&&null==m.props.tpl?m.props.children:m,r,n,a,t,o,s)):""}finally{no&&no(t),t.__=null,nc&&nc(t)}}}m=null!=m&&m.type===rK&&null==m.key&&null==m.props.tpl?m.props.children:m;try{var O=e(m,r,n,a,t,o,s);return no&&no(t),t.__=null,rA.unmount&&rA.unmount(t),O}catch(i){if(!o&&s&&s.onError){var P=s.onError(i,t,function(i){return e(i,r,n,a,t,o,s)});if(void 0!==P)return P;var T=rA.__e;return T&&T(i,t),""}if(!o||!i||"function"!=typeof i.then)throw i;return i.then(function i(){try{return e(m,r,n,a,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(m,r,n,a,t,o,s)},i)}})}}var k,C="<"+b,j="";for(var N in g){var I=g[N];if("function"!=typeof I||"class"===N||"className"===N){switch(N){case"children":k=I;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in g)continue;N="for";break;case"className":if("class"in g)continue;N="class";break;case"defaultChecked":N="checked";break;case"defaultSelected":N="selected";break;case"defaultValue":case"value":switch(N="value",b){case"textarea":k=I;continue;case"select":a=I;continue;case"option":a!=I||"selected"in g||(C+=" selected")}break;case"dangerouslySetInnerHTML":j=I&&I.__html;continue;case"style":"object"==typeof I&&(I=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var a="-"==r[0]?r:ne[r]||(ne[r]=r.replace(nr,"-$&").toLowerCase()),i=";";"number"!=typeof n||a.startsWith("--")||nt.has(a)||(i="px;"),t=t+a+":"+n+i}}return t||void 0}(I));break;case"acceptCharset":N="accept-charset";break;case"httpEquiv":N="http-equiv";break;default:if(r6.test(N))N=N.replace(r6,"$1:$2").toLowerCase();else{if(r5.test(N))continue;("-"===N[4]||r8.has(N))&&null!=I?I+="":n?r4.test(N)&&(N="panose1"===N?"panose-1":N.replace(/([A-Z])/g,"-$1").toLowerCase()):r3.test(N)&&(N=N.toLowerCase())}}null!=I&&!1!==I&&(C=!0===I||""===I?C+" "+N:C+" "+N+'="'+("string"==typeof I?r7(I):I+"")+'"')}}if(r5.test(b))throw Error(b+" is not a valid HTML tag name in "+C+">");if(j||("string"==typeof k?j=r7(k):null!=k&&!1!==k&&!0!==k&&(j=e(k,r,"svg"===b||"foreignObject"!==b&&n,a,t,o,s))),no&&no(t),t.__=null,nc&&nc(t),!j&&nh.has(b))return C+"/>";var D="</"+b+">",M=C+">";return nd(j)?[M].concat(j,[D]):"string"!=typeof j?[M,j,D]:M+j+D}(e,nu,!1,void 0,a,!1,void 0);return nd(i)?i.join(""):i}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{rA.__c&&rA.__c(e,nl),rA.__s=n,nl.length=0}}(e)}</div></body></html>`}}function nx(e){let{url:t,theme:r,query:n,cookies:a,pages:i,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:a,callbackUrl:i})=>(e[t]={id:t,name:r,type:n,signinUrl:a,callbackUrl:i},e),{})}),signin(t,s){if(t)throw new U("Unsupported action");if(i?.signIn){let t=`${i.signIn}${i.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:a}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),u="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;u=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return nw({cookies:a,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:a,email:i,error:o}=e;"undefined"!=typeof document&&a?.brandColor&&document.documentElement.style.setProperty("--brand-color",a.brandColor),"undefined"!=typeof document&&a?.buttonText&&document.documentElement.style.setProperty("--button-text-color",a.buttonText);let s=o&&(nv[o]??nv.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return ny("div",{className:"signin",children:[a?.brandColor&&ny("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${a.brandColor}}`}}),a?.buttonText&&ny("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${a.buttonText}
        }
      `}}),ny("div",{className:"card",children:[s&&ny("div",{className:"error",children:ny("p",{children:s})}),a?.logo&&ny("img",{src:a.logo,alt:"Logo",className:"logo"}),r.map((e,a)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let u=s??o??"#fff";return ny("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?ny("form",{action:e.signinUrl,method:"POST",children:[ny("input",{type:"hidden",name:"csrfToken",value:t}),n&&ny("input",{type:"hidden",name:"callbackUrl",value:n}),ny("button",{type:"submit",className:"button",style:{"--provider-brand-color":u},tabIndex:0,children:[ny("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&ny("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a>0&&"email"!==r[a-1].type&&"credentials"!==r[a-1].type&&"webauthn"!==r[a-1].type&&ny("hr",{}),"email"===e.type&&ny("form",{action:e.signinUrl,method:"POST",children:[ny("input",{type:"hidden",name:"csrfToken",value:t}),ny("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),ny("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:i,placeholder:"<EMAIL>",required:!0}),ny("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&ny("form",{action:e.callbackUrl,method:"POST",children:[ny("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>ny("div",{children:[ny("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),ny("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),ny("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&ny("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[ny("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>ny("div",{children:[ny("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),ny("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),ny("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&a+1<r.length&&ny("hr",{})]},e.id)})]}),c&&ny(rK,{children:ny("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${nm})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:u})},signout:()=>i?.signOut?{redirect:i.signOut,cookies:a}:nw({cookies:a,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return ny("div",{className:"signout",children:[n?.brandColor&&ny("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&ny("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),ny("div",{className:"card",children:[n?.logo&&ny("img",{src:n.logo,alt:"Logo",className:"logo"}),ny("h1",{children:"Signout"}),ny("p",{children:"Are you sure you want to sign out?"}),ny("form",{action:t?.toString(),method:"POST",children:[ny("input",{type:"hidden",name:"csrfToken",value:r}),ny("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>i?.verifyRequest?{redirect:`${i.verifyRequest}${t?.search??""}`,cookies:a}:nw({cookies:a,theme:r,html:function(e){let{url:t,theme:r}=e;return ny("div",{className:"verify-request",children:[r.brandColor&&ny("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),ny("div",{className:"card",children:[r.logo&&ny("img",{src:r.logo,alt:"Logo",className:"logo"}),ny("h1",{children:"Check your email"}),ny("p",{children:"A sign in link has been sent to your email address."}),ny("p",{children:ny("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>i?.error?{redirect:`${i.error}${i.error.includes("?")?"&":"?"}error=${e}`,cookies:a}:nw({cookies:a,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,a=`${t}/signin`,i={default:{status:200,heading:"Error",message:ny("p",{children:ny("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:ny("div",{children:[ny("p",{children:"There is a problem with the server configuration."}),ny("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:ny("div",{children:[ny("p",{children:"You do not have permission to sign in."}),ny("p",{children:ny("a",{className:"button",href:a,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:ny("div",{children:[ny("p",{children:"The sign in link is no longer valid."}),ny("p",{children:"It may have been used already or it may have expired."})]}),signin:ny("a",{className:"button",href:a,children:"Sign in"})}},{status:o,heading:s,message:c,signin:u}=i[r]??i.default;return{status:o,html:ny("div",{className:"error",children:[n?.brandColor&&ny("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),ny("div",{className:"card",children:[n?.logo&&ny("img",{src:n?.logo,alt:"Logo",className:"logo"}),ny("h1",{children:s}),ny("div",{className:"message",children:c}),u]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function nE(e,t=Date.now()){return new Date(t+1e3*e)}async function nS(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:a,jwt:i,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!a)return{user:t,account:r};let u=r,{createUser:l,updateUser:d,getUser:f,getUserByAccount:p,getUserByEmail:h,linkAccount:b,createSession:g,getSessionAndUser:y,deleteSession:m}=a,v=null,_=null,w=!1,x="jwt"===s;if(e)if(x)try{let t=n.cookies.sessionToken.name;(v=await i.decode({...i,token:e,salt:t}))&&"sub"in v&&v.sub&&(_=await f(v.sub))}catch{}else{let t=await y(e);t&&(v=t.session,_=t.user)}if("email"===u.type){let r=await h(t.email);return r?(_?.id!==r.id&&!x&&e&&await m(e),_=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:_})):(_=await l({...t,emailVerified:new Date}),await o.createUser?.({user:_}),w=!0),{session:v=x?{}:await g({sessionToken:c(),userId:_.id,expires:nE(n.session.maxAge)}),user:_,isNewUser:w}}if("webauthn"===u.type){let e=await p({providerAccountId:u.providerAccountId,provider:u.provider});if(e){if(_){if(e.id===_.id){let e={...u,userId:_.id};return{session:v,user:_,isNewUser:w,account:e}}throw new J("The account is already associated with another user",{provider:u.provider})}v=x?{}:await g({sessionToken:c(),userId:e.id,expires:nE(n.session.maxAge)});let t={...u,userId:e.id};return{session:v,user:e,isNewUser:w,account:t}}{if(_){await b({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t});let e={...u,userId:_.id};return{session:v,user:_,isNewUser:w,account:e}}if(t.email?await h(t.email):null)throw new J("Another account already exists with the same e-mail address",{provider:u.provider});_=await l({...t}),await o.createUser?.({user:_}),await b({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),v=x?{}:await g({sessionToken:c(),userId:_.id,expires:nE(n.session.maxAge)});let e={...u,userId:_.id};return{session:v,user:_,isNewUser:!0,account:e}}}let E=await p({providerAccountId:u.providerAccountId,provider:u.provider});if(E){if(_){if(E.id===_.id)return{session:v,user:_,isNewUser:w};throw new k("The account is already associated with another user",{provider:u.provider})}return{session:v=x?{}:await g({sessionToken:c(),userId:E.id,expires:nE(n.session.maxAge)}),user:E,isNewUser:w}}{let{provider:e}=n,{type:r,provider:a,providerAccountId:i,userId:s,...d}=u;if(u=Object.assign(e.account(d)??{},{providerAccountId:i,provider:a,type:r,userId:s}),_)return await b({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),{session:v,user:_,isNewUser:w};let f=t.email?await h(t.email):null;if(f){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)_=f,w=!1;else throw new k("Another account already exists with the same e-mail address",{provider:u.provider})}else _=await l({...t,emailVerified:null}),w=!0;return await o.createUser?.({user:_}),await b({...u,userId:_.id}),await o.linkAccount?.({user:_,account:u,profile:t}),{session:v=x?{}:await g({sessionToken:c(),userId:_.id,expires:nE(n.session.maxAge)}),user:_,isNewUser:w}}}function nR(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(i="oauth4webapi/v3.5.1");let nA="ERR_INVALID_ARG_VALUE",nO="ERR_INVALID_ARG_TYPE";function nP(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let nT=Symbol(),nk=Symbol(),nC=Symbol(),nj=Symbol(),nN=Symbol(),nI=Symbol(),nD=Symbol(),nM=new TextEncoder,nU=new TextDecoder;function n$(e){return"string"==typeof e?nM.encode(e):nU.decode(e)}function nL(e){if("string"==typeof e)try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw nP("The input to be decoded is not correctly encoded.",nA,e)}var t=e;t instanceof ArrayBuffer&&(t=new Uint8Array(t));let r=[];for(let e=0;e<t.byteLength;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class nH extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aX,Error.captureStackTrace?.(this,this.constructor)}}class nW extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function nB(e,t,r){return new nW(e,{code:t,cause:r})}function nK(e,t){if(!(e instanceof CryptoKey))throw nP(`${t} must be a CryptoKey`,nO)}function nF(e,t){if(nK(e,t),"private"!==e.type)throw nP(`${t} must be a private CryptoKey`,nA)}function nq(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function nG(e){nR(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(i&&!t.has("user-agent")&&t.set("user-agent",i),t.has("authorization"))throw nP('"options.headers" must not include the "authorization" header name',nA);return t}function nJ(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw nP('"options.signal" must return or be an instance of AbortSignal',nO);return e}function nV(e){return e.includes("//")?e.replace("//","/"):e}async function nX(e,t,r,n){if(!(e instanceof URL))throw nP(`"${t}" must be an instance of URL`,nO);ai(e,n?.[nT]!==!0);let a=r(new URL(e.href)),i=nG(n?.headers);return i.set("accept","application/json"),(n?.[nj]||fetch)(a.href,{body:void 0,headers:Object.fromEntries(i.entries()),method:"GET",redirect:"manual",signal:n?.signal?nJ(n.signal):void 0})}async function nz(e,t){return nX(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=nV(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,n;n=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=n:r.pathname=nV(`${n}/${r.pathname}`);break;default:throw nP('"options.algorithm" must be "oidc" (default), or "oauth2"',nA)}return e},t)}function nY(e,t,r,n,a){try{if("number"!=typeof e||!Number.isFinite(e))throw nP(`${r} must be a number`,nO,a);if(e>0)return;if(t){if(0!==e)throw nP(`${r} must be a non-negative number`,nA,a);return}throw nP(`${r} must be a positive number`,nA,a)}catch(e){if(n)throw nB(e.message,n,a);throw e}}function nZ(e,t,r,n){try{if("string"!=typeof e)throw nP(`${t} must be a string`,nO,n);if(0===e.length)throw nP(`${t} must not be empty`,nA,n)}catch(e){if(r)throw nB(e.message,r,n);throw e}}async function nQ(e,t){if(!(e instanceof URL)&&e!==iR)throw nP('"expectedIssuerIdentifier" must be an instance of URL',nO);if(!nR(t,Response))throw nP('"response" must be an instance of Response',nO);if(200!==t.status)throw nB('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',a2,t);ir(t);let r=await iE(t);if(nZ(r.issuer,'"response" body "issuer" property',aQ,{body:r}),e!==iR&&new URL(r.issuer).href!==e.href)throw nB('"response" body "issuer" property does not match the expected value',a8,{expected:e.href,body:r,attribute:"issuer"});return r}function n0(e){var t=e,r="application/json";if(aR(t)!==r)throw n1(t,r)}function n1(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return nB(r,a1,e)}function n2(){return nL(crypto.getRandomValues(new Uint8Array(32)))}async function n5(e){return nZ(e,"codeVerifier"),nL(await crypto.subtle.digest("SHA-256",n$(e)))}function n6(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new nH("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new nH("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new nH("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nH("unsupported CryptoKey algorithm name",{cause:e})}}function n3(e){let t=e?.[nk];return"number"==typeof t&&Number.isFinite(t)?t:0}function n4(e){let t=e?.[nC];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function n8(){return Math.floor(Date.now()/1e3)}function n9(e){if("object"!=typeof e||null===e)throw nP('"as" must be an object',nO);nZ(e.issuer,'"as.issuer"')}function n7(e){if("object"!=typeof e||null===e)throw nP('"client" must be an object',nO);nZ(e.client_id,'"client.client_id"')}function ae(e,t){let r=n8()+n3(t);return{jti:n2(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function at(e,t,r){if(!r.usages.includes("sign"))throw nP('CryptoKey instances used for signing assertions must include "sign" in their "usages"',nA);let n=`${nL(n$(JSON.stringify(e)))}.${nL(n$(JSON.stringify(t)))}`,a=nL(await crypto.subtle.sign(ic(r),r,n$(n)));return`${n}.${a}`}async function ar(e){let{kty:t,e:r,n,x:a,y:i,crv:s}=await crypto.subtle.exportKey("jwk",e),c={kty:t,e:r,n,x:a,y:i,crv:s};return o.set(e,c),c}async function an(e){return(o||=new WeakMap).get(e)||ar(e)}let aa=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function ai(e,t){if(t&&"https:"!==e.protocol)throw nB("only requests to HTTPS are allowed",a5,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw nB("only HTTP and HTTPS requests are allowed",a6,e)}function ao(e,t,r,n){let a;if("string"!=typeof e||!(a=aa(e)))throw nB(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?a7:ie,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return ai(a,n),a}function as(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?ao(e.mtls_endpoint_aliases[t],t,r,n):ao(e[t],t,r,n)}class ac extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aV,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class au extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=az,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class al extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=aJ,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let ad="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",af=RegExp("^[,\\s]*("+ad+")\\s(.*)"),ap=RegExp("^[,\\s]*("+ad+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),ah=RegExp("^[,\\s]*"+("("+ad+")\\s*=\\s*(")+ad+")[,\\s]*(.*)"),ab=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function ag(e){if(e.status>399&&e.status<500){ir(e),n0(e);try{let t=await e.clone().json();if(nq(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function ay(e,t,r){if(e.status!==t){let t;if(t=await ag(e))throw await e.body?.cancel(),new ac("server responded with an error in the response body",{cause:t,response:e});throw nB(`"response" is not a conform ${r} response (unexpected HTTP status code)`,a2,e)}}function am(e){if(!a$.has(e))throw nP('"options.DPoP" is not a valid DPoPHandle',nA)}async function av(e,t,r,n,a,i){if(nZ(e,'"accessToken"'),!(r instanceof URL))throw nP('"url" must be an instance of URL',nO);ai(r,i?.[nT]!==!0),n=nG(n),i?.DPoP&&(am(i.DPoP),await i.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (i?.[nj]||fetch)(r.href,{body:a,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:i?.signal?nJ(i.signal):void 0});return i?.DPoP?.cacheNonce(o),o}async function a_(e,t,r,n){n9(e),n7(t);let a=as(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[nT]!==!0),i=nG(n?.headers);return t.userinfo_signed_response_alg?i.set("accept","application/jwt"):(i.set("accept","application/json"),i.append("accept","application/jwt")),av(r,"GET",a,i,null,{...n,[nk]:n3(t)})}function aw(e,t,r,n){(s||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return n8()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function ax(e,t){s?.delete(e),delete t?.jwks,delete t?.uat}async function aE(e,t,r){var n;let a,i,o,{alg:c,kid:u}=r;if(function(e){if(!io(e.alg))throw new nH('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!s?.has(e)&&!("object"!=typeof(n=t?.[nD])||null===n||!("uat"in n)||"number"!=typeof n.uat||n8()-n.uat>=300)&&"jwks"in n&&nq(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,nq)&&aw(e,t?.[nD].jwks,t?.[nD].uat),s?.has(e)){if({jwks:a,age:i}=s.get(e),i>=300)return ax(e,t?.[nD]),aE(e,t,r)}else a=await ia(e,t).then(ii),i=0,aw(e,a,n8(),t?.[nD]);switch(c.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new nH("unsupported JWS algorithm",{cause:{alg:c}})}let l=a.keys.filter(e=>{if(e.kty!==o||void 0!==u&&u!==e.kid||void 0!==e.alg&&c!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===c&&"P-256"!==e.crv:case"ES384"===c&&"P-384"!==e.crv:case"ES512"===c&&"P-521"!==e.crv:case"Ed25519"===c&&"Ed25519"!==e.crv:case"EdDSA"===c&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:f}=l;if(!f){if(i>=60)return ax(e,t?.[nD]),aE(e,t,r);throw nB("error when selecting a JWT verification key, no applicable keys found",a9,{header:r,candidates:l,jwks_uri:new URL(e.jwks_uri)})}if(1!==f)throw nB('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',a9,{header:r,candidates:l,jwks_uri:new URL(e.jwks_uri)});return iw(c,d)}let aS=Symbol();function aR(e){return e.headers.get("content-type")?.split(";")[0]}async function aA(e,t,r,n,a){let i;if(n9(e),n7(t),!nR(n,Response))throw nP('"response" must be an instance of Response',nO);if(aN(n),200!==n.status)throw nB('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',a2,n);if(ir(n),"application/jwt"===aR(n)){let{claims:r,jwt:o}=await il(await n.text(),ig.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),n3(t),n4(t),a?.[nI]).then(aI.bind(void 0,t.client_id)).then(aM.bind(void 0,e));ak.set(n,o),i=r}else{if(t.userinfo_signed_response_alg)throw nB("JWT UserInfo Response expected",aY,n);i=await iE(n)}if(nZ(i.sub,'"response" body "sub" property',aQ,{body:i}),r===aS);else if(nZ(r,'"expectedSubject"'),i.sub!==r)throw nB('unexpected "response" body "sub" property value',a8,{expected:r,body:i,attribute:"sub"});return i}async function aO(e,t,r,n,a,i,o){return await r(e,t,a,i),i.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[nj]||fetch)(n.href,{body:a,headers:Object.fromEntries(i.entries()),method:"POST",redirect:"manual",signal:o?.signal?nJ(o.signal):void 0})}async function aP(e,t,r,n,a,i){let o=as(e,"token_endpoint",t.use_mtls_endpoint_aliases,i?.[nT]!==!0);a.set("grant_type",n);let s=nG(i?.headers);s.set("accept","application/json"),i?.DPoP!==void 0&&(am(i.DPoP),await i.DPoP.addProof(o,s,"POST"));let c=await aO(e,t,r,o,a,s,i);return i?.DPoP?.cacheNonce(c),c}let aT=new WeakMap,ak=new WeakMap;function aC(e){if(!e.id_token)return;let t=aT.get(e);if(!t)throw nP('"ref" was already garbage collected or did not resolve from the proper sources',nA);return t}async function aj(e,t,r,n,a){if(n9(e),n7(t),!nR(r,Response))throw nP('"response" must be an instance of Response',nO);aN(r),await ay(r,200,"Token Endpoint"),ir(r);let i=await iE(r);if(nZ(i.access_token,'"response" body "access_token" property',aQ,{body:i}),nZ(i.token_type,'"response" body "token_type" property',aQ,{body:i}),i.token_type=i.token_type.toLowerCase(),"dpop"!==i.token_type&&"bearer"!==i.token_type)throw new nH("unsupported `token_type` value",{cause:{body:i}});if(void 0!==i.expires_in){let e="number"!=typeof i.expires_in?parseFloat(i.expires_in):i.expires_in;nY(e,!1,'"response" body "expires_in" property',aQ,{body:i}),i.expires_in=e}if(void 0!==i.refresh_token&&nZ(i.refresh_token,'"response" body "refresh_token" property',aQ,{body:i}),void 0!==i.scope&&"string"!=typeof i.scope)throw nB('"response" body "scope" property must be a string',aQ,{body:i});if(void 0!==i.id_token){nZ(i.id_token,'"response" body "id_token" property',aQ,{body:i});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(nY(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await il(i.id_token,ig.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),n3(t),n4(t),a?.[nI]).then(aW.bind(void 0,o)).then(aU.bind(void 0,e)).then(aD.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw nB('ID Token "aud" (audience) claim includes additional untrusted audiences',a4,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw nB('unexpected ID Token "azp" (authorized party) claim value',a4,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&nY(s.auth_time,!1,'ID Token "auth_time" (authentication time)',aQ,{claims:s}),ak.set(r,c),aT.set(i,s)}return i}function aN(e){let t;if(t=function(e){if(!nR(e,Response))throw nP('"response" must be an instance of Response',nO);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(af),a=t?.["1"].toLowerCase();if(n=t?.["2"],!a)return;let i={};for(;n;){let r,a;if(t=n.match(ap)){if([,r,a,n]=t,a.includes("\\"))try{a=JSON.parse(`"${a}"`)}catch{}i[r.toLowerCase()]=a;continue}if(t=n.match(ah)){[,r,a,n]=t,i[r.toLowerCase()]=a;continue}if(t=n.match(ab)){if(Object.keys(i).length)break;[,e,n]=t;break}return}let o={scheme:a,parameters:i};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new al("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function aI(e,t){return void 0!==t.claims.aud?aD(e,t):t}function aD(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw nB('unexpected JWT "aud" (audience) claim value',a4,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw nB('unexpected JWT "aud" (audience) claim value',a4,{expected:e,claims:t.claims,claim:"aud"});return t}function aM(e,t){return void 0!==t.claims.iss?aU(e,t):t}function aU(e,t){let r=e[iA]?.(t)??e.issuer;if(t.claims.iss!==r)throw nB('unexpected JWT "iss" (issuer) claim value',a4,{expected:r,claims:t.claims,claim:"iss"});return t}let a$=new WeakSet;async function aL(e,t,r,n,a,i,o){if(n9(e),n7(t),!a$.has(n))throw nP('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',nA);nZ(a,'"redirectUri"');let s=iy(n,"code");if(!s)throw nB('no authorization code in "callbackParameters"',aQ);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",a),c.set("code",s),i!==iS&&(nZ(i,'"codeVerifier"'),c.set("code_verifier",i)),aP(e,t,r,"authorization_code",c,o)}let aH={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function aW(e,t){for(let r of e)if(void 0===t.claims[r])throw nB(`JWT "${r}" (${aH[r]}) claim missing`,aQ,{claims:t.claims});return t}let aB=Symbol(),aK=Symbol();async function aF(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?aq(e,t,r,n.expectedNonce,n.maxAge,{[nI]:n[nI]}):aG(e,t,r,n)}async function aq(e,t,r,n,a,i){let o=[];switch(n){case void 0:n=aB;break;case aB:break;default:nZ(n,'"expectedNonce" argument'),o.push("nonce")}switch(a??=t.default_max_age){case void 0:a=aK;break;case aK:break;default:nY(a,!1,'"maxAge" argument'),o.push("auth_time")}let s=await aj(e,t,r,o,i);nZ(s.id_token,'"response" body "id_token" property',aQ,{body:s});let c=aC(s);if(a!==aK){let e=n8()+n3(t),r=n4(t);if(c.auth_time+a<e-r)throw nB("too much time has elapsed since the last End-User authentication",a3,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===aB){if(void 0!==c.nonce)throw nB('unexpected ID Token "nonce" claim value',a4,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw nB('unexpected ID Token "nonce" claim value',a4,{expected:n,claims:c,claim:"nonce"});return s}async function aG(e,t,r,n){let a=await aj(e,t,r,void 0,n),i=aC(a);if(i){if(void 0!==t.default_max_age){nY(t.default_max_age,!1,'"client.default_max_age"');let e=n8()+n3(t),r=n4(t);if(i.auth_time+t.default_max_age<e-r)throw nB("too much time has elapsed since the last End-User authentication",a3,{claims:i,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==i.nonce)throw nB('unexpected ID Token "nonce" claim value',a4,{expected:void 0,claims:i,claim:"nonce"})}return a}let aJ="OAUTH_WWW_AUTHENTICATE_CHALLENGE",aV="OAUTH_RESPONSE_BODY_ERROR",aX="OAUTH_UNSUPPORTED_OPERATION",az="OAUTH_AUTHORIZATION_RESPONSE_ERROR",aY="OAUTH_JWT_USERINFO_EXPECTED",aZ="OAUTH_PARSE_ERROR",aQ="OAUTH_INVALID_RESPONSE",a0="OAUTH_INVALID_REQUEST",a1="OAUTH_RESPONSE_IS_NOT_JSON",a2="OAUTH_RESPONSE_IS_NOT_CONFORM",a5="OAUTH_HTTP_REQUEST_FORBIDDEN",a6="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",a3="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",a4="OAUTH_JWT_CLAIM_COMPARISON_FAILED",a8="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",a9="OAUTH_KEY_SELECTION_FAILED",a7="OAUTH_MISSING_SERVER_METADATA",ie="OAUTH_INVALID_SERVER_METADATA";function it(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw nB('unexpected JWT "typ" header parameter value',aQ,{header:t.header});return t}function ir(e){if(e.bodyUsed)throw nP('"response" body has been used already',nA)}async function ia(e,t){n9(e);let r=as(e,"jwks_uri",!1,t?.[nT]!==!0),n=nG(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[nj]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?nJ(t.signal):void 0})}async function ii(e){if(!nR(e,Response))throw nP('"response" must be an instance of Response',nO);if(200!==e.status)throw nB('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',a2,e);ir(e);let t=await iE(e,e=>(function(e,...t){if(!t.includes(aR(e)))throw n1(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw nB('"response" body "keys" property must be an array',aQ,{body:t});if(!Array.prototype.every.call(t.keys,nq))throw nB('"response" body "keys" property members must be JWK formatted objects',aQ,{body:t});return t}function io(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function is(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new nH(`unsupported ${t.name} modulusLength`,{cause:e})}function ic(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new nH("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(is(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new nH("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return is(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new nH("unsupported CryptoKey algorithm name",{cause:e})}async function iu(e,t,r,n){let a=n$(`${e}.${t}`),i=ic(r);if(!await crypto.subtle.verify(i,r,n,a))throw nB("JWT signature verification failed",aQ,{key:r,data:a,signature:n,algorithm:i})}async function il(e,t,r,n,a){let i,o,{0:s,1:c,length:u}=e.split(".");if(5===u)if(void 0!==a)e=await a(e),{0:s,1:c,length:u}=e.split(".");else throw new nH("JWE decryption is not configured",{cause:e});if(3!==u)throw nB("Invalid JWT",aQ,e);try{i=JSON.parse(n$(nL(s)))}catch(e){throw nB("failed to parse JWT Header body as base64url encoded JSON",aZ,e)}if(!nq(i))throw nB("JWT Header must be a top level object",aQ,e);if(t(i),void 0!==i.crit)throw new nH('no JWT "crit" header parameter extensions are supported',{cause:{header:i}});try{o=JSON.parse(n$(nL(c)))}catch(e){throw nB("failed to parse JWT Payload body as base64url encoded JSON",aZ,e)}if(!nq(o))throw nB("JWT Payload must be a top level object",aQ,e);let l=n8()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw nB('unexpected JWT "exp" (expiration time) claim type',aQ,{claims:o});if(o.exp<=l-n)throw nB('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',a3,{claims:o,now:l,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw nB('unexpected JWT "iat" (issued at) claim type',aQ,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw nB('unexpected JWT "iss" (issuer) claim type',aQ,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw nB('unexpected JWT "nbf" (not before) claim type',aQ,{claims:o});if(o.nbf>l+n)throw nB('unexpected JWT "nbf" (not before) claim value',a3,{claims:o,now:l,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw nB('unexpected JWT "aud" (audience) claim type',aQ,{claims:o});return{header:i,claims:o,jwt:e}}async function id(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new nH(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let a=await crypto.subtle.digest(n,n$(e));return nL(a.slice(0,a.byteLength/2))}async function ip(e,t,r,n){return t===await id(e,r,n)}async function ih(e){if(e.bodyUsed)throw nP("form_post Request instances must contain a readable body",nA,{cause:e});return e.text()}async function ib(e){if("POST"!==e.method)throw nP("form_post responses are expected to use the POST method",nA,{cause:e});if("application/x-www-form-urlencoded"!==aR(e))throw nP("form_post responses are expected to use the application/x-www-form-urlencoded content-type",nA,{cause:e});return ih(e)}function ig(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw nB('unexpected JWT "alg" header parameter',aQ,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw nB('unexpected JWT "alg" header parameter',aQ,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw nB('unexpected JWT "alg" header parameter',aQ,{header:n,expected:r,reason:"default value"});return}throw nB('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function iy(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw nB(`"${t}" parameter must be provided only once`,aQ);return r}let im=Symbol(),iv=Symbol();function i_(e,t,r,n){var a;if(n9(e),n7(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw nP('"parameters" must be an instance of URLSearchParams, or URL',nO);if(iy(r,"response"))throw nB('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',aQ,{parameters:r});let i=iy(r,"iss"),o=iy(r,"state");if(!i&&e.authorization_response_iss_parameter_supported)throw nB('response parameter "iss" (issuer) missing',aQ,{parameters:r});if(i&&i!==e.issuer)throw nB('unexpected "iss" (issuer) response parameter value',aQ,{expected:e.issuer,parameters:r});switch(n){case void 0:case iv:if(void 0!==o)throw nB('unexpected "state" response parameter encountered',aQ,{expected:void 0,parameters:r});break;case im:break;default:if(nZ(n,'"expectedState" argument'),o!==n)throw nB(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',aQ,{expected:n,parameters:r})}if(iy(r,"error"))throw new au("authorization response from the server is an error",{cause:r});let s=iy(r,"id_token"),c=iy(r,"token");if(void 0!==s||void 0!==c)throw new nH("implicit and hybrid flows are not supported");return a=new URLSearchParams(r),a$.add(a),a}async function iw(e,t){let{ext:r,key_ops:n,use:a,...i}=t;return crypto.subtle.importKey("jwk",i,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new nH("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function ix(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function iE(e,t=n0){let r;try{r=await e.json()}catch(r){throw t(e),nB('failed to parse "response" body as JSON',aZ,r)}if(!nq(r))throw nB('"response" body must be a top level object',aQ,{body:r});return r}let iS=Symbol(),iR=Symbol(),iA=Symbol();async function iO(e,t,r){let{cookies:n,logger:a}=r,i=n[e],o=new Date;o.setTime(o.getTime()+9e5),a.debug(`CREATE_${e.toUpperCase()}`,{name:i.name,payload:t,COOKIE_TTL:900,expires:o});let s=await tQ({...r.jwt,maxAge:900,token:{value:t},salt:i.name}),c={...i.options,expires:o};return{name:i.name,value:s,options:c}}async function iP(e,t,r){try{let{logger:n,cookies:a,jwt:i}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new S(`${e} cookie was missing`);let o=await t0({...i,token:t,salt:a[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new S(`${e} value could not be parsed`,{cause:t})}}function iT(e,t,r){let{logger:n,cookies:a}=t,i=a[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:i}),r.push({name:i.name,value:"",options:{...a[e].options,maxAge:0}})}function ik(e,t){return async function(r,n,a){let{provider:i,logger:o}=a;if(!i?.checks?.includes(e))return;let s=r?.[a.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await iP(t,s,a);return iT(t,a,n),c}}let iC={async create(e){let t=n2(),r=await n5(t);return{cookie:await iO("pkceCodeVerifier",t,e),value:r}},use:ik("pkce","pkceCodeVerifier")},ij="encodedState",iN={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new S("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:n2()},a=await tQ({secret:e.jwt.secret,token:n,salt:ij,maxAge:900});return{cookie:await iO("state",a,e),value:a}},use:ik("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await t0({secret:t.jwt.secret,token:e,salt:ij});if(r)return r;throw Error("Invalid state")}catch(e){throw new S("State could not be decoded",{cause:e})}}},iI={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=n2();return{cookie:await iO("nonce",t,e),value:t}},use:ik("nonce","nonce")},iD="encodedWebauthnChallenge",iM={create:async(e,t,r)=>({cookie:await iO("webauthnChallenge",await tQ({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:iD,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],a=await iP("webauthnChallenge",n,e),i=await t0({secret:e.jwt.secret,token:a,salt:iD});if(iT("webauthnChallenge",e,r),!i)throw new S("WebAuthn challenge was missing");return i}};function iU(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function i$(e,t,r){let n,a,i,{logger:o,provider:s}=r,{token:c,userinfo:u}=s;if(c?.url&&"authjs.dev"!==c.url.host||u?.url&&"authjs.dev"!==u.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:u?.url.toString()};else{let e=new URL(s.issuer),t=await nz(e,{[nT]:!0,[nj]:s[rd]});if(!(n=await nQ(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let l={client_id:s.clientId,...s.client};switch(l.token_endpoint_auth_method){case void 0:case"client_secret_basic":a=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=iU(e),n=iU(t),a=btoa(`${r}:${n}`);return`Basic ${a}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;nZ(d=s.clientSecret,'"clientSecret"'),a=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":a=function(e,t){let r;nZ(e,'"clientSecret"');let n=void 0;return async(t,a,i,o)=>{r||=await crypto.subtle.importKey("raw",n$(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=ae(t,a);n?.(s,c);let u=`${nL(n$(JSON.stringify(s)))}.${nL(n$(JSON.stringify(c)))}`,l=await crypto.subtle.sign(r.algorithm,r,n$(u));i.set("client_id",a.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",`${u}.${nL(new Uint8Array(l))}`)}}(s.clientSecret);break;case"private_key_jwt":a=function(e,t){var r;let{key:n,kid:a}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&nZ(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return nF(n,'"clientPrivateKey.key"'),async(e,r,i,o)=>{let s={alg:n6(n),kid:a},c=ae(e,r);t?.[nN]?.(s,c),i.set("client_id",r.client_id),i.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),i.set("client_assertion",await at(s,c,n))}}(s.token.clientPrivateKey,{[nN](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":a=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let f=[],p=await iN.use(t,f,r);try{i=i_(n,l,new URLSearchParams(e),s.checks.includes("state")?p:im)}catch(e){if(e instanceof au){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new C("OAuth Provider returned an error",t)}throw e}let h=await iC.use(t,f,r),b=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(b=s.redirectProxyUrl);let g=await aL(n,l,a,i,b,h??"decoy",{[nT]:!0,[nj]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[rd]??fetch)(...e))});s.token?.conform&&(g=await s.token.conform(g.clone())??g);let y={},m="oidc"===s.type;if(s[rf])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await g.clone().json();if(e.error){let t={providerId:s.id,...e};throw new C(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new eS("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:a}=e.split(".");if(5===a)throw new eS("Only JWTs using Compact JWS serialization can be decoded");if(3!==a)throw new eS("Invalid JWT");if(!n)throw new eS("JWTs must contain a payload");try{t=eb(n)}catch{throw new eS("Failed to base64url decode the payload")}try{r=JSON.parse(el.decode(t))}catch{throw new eS("Failed to parse the decoded payload as JSON")}if(!eC(r))throw new eS("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),a=await nz(r,{[nj]:s[rd]});n=await nQ(r,a)}}}let v=await aF(n,l,g,{expectedNonce:await iI.use(t,f,r),requireIdToken:m});if(m){let t=aC(v);if(y=t,s[rf]&&"apple"===s.id)try{y.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await a_(n,l,v.access_token,{[nj]:s[rd],[nT]:!0});y=await aA(n,l,t.sub,e)}}else if(u?.request){let e=await u.request({tokens:v,provider:s});e instanceof Object&&(y=e)}else if(u?.url){let e=await a_(n,l,v.access_token,{[nj]:s[rd],[nT]:!0});y=await e.json()}else throw TypeError("No userinfo endpoint configured");return v.expires_in&&(v.expires_at=Math.floor(Date.now()/1e3)+Number(v.expires_in)),{...await iL(y,s,v,o),profile:y,cookies:f}}async function iL(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new j(r,{provider:t.id}))}}async function iH(e,t,r,n){let a=await iq(e,t,r),{cookie:i}=await iM.create(e,a.challenge,r);return{status:200,cookies:[...n??[],i],body:{action:"register",options:a},headers:{"Content-Type":"application/json"}}}async function iW(e,t,r,n){let a=await iF(e,t,r),{cookie:i}=await iM.create(e,a.challenge);return{status:200,cookies:[...n??[],i],body:{action:"authenticate",options:a},headers:{"Content-Type":"application/json"}}}async function iB(e,t,r){let n,{adapter:a,provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new h("Invalid WebAuthn Authentication response");let s=iV(iJ(o.id)),c=await a.getAuthenticator(s);if(!c)throw new h(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:u}=await iM.use(e,t.cookies,r);try{var l;let r=i.getRelayingParty(e,t);n=await i.simpleWebAuthn.verifyAuthenticationResponse({...i.verifyAuthenticationOptions,expectedChallenge:u,response:o,authenticator:{...l=c,credentialDeviceType:l.credentialDeviceType,transports:iX(l.transports),credentialID:iJ(l.credentialID),credentialPublicKey:iJ(l.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new G(e)}let{verified:d,authenticationInfo:f}=n;if(!d)throw new G("WebAuthn authentication response could not be verified");try{let{newCounter:e}=f;await a.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new g(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:f.newCounter})}`,e)}let p=await a.getAccount(c.providerAccountId,i.id);if(!p)throw new h(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let b=await a.getUser(p.userId);if(!b)throw new h(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:p.userId})}`);return{account:p,user:b}}async function iK(e,t,r){var n;let a,{provider:i}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new h("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await iM.use(e,t.cookies,r);if(!c)throw new h("Missing user registration data in WebAuthn challenge cookie");try{let r=i.getRelayingParty(e,t);a=await i.simpleWebAuthn.verifyRegistrationResponse({...i.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new G(e)}if(!a.verified||!a.registrationInfo)throw new G("WebAuthn registration response could not be verified");let u={providerAccountId:iV(a.registrationInfo.credentialID),provider:e.provider.id,type:i.type},l={providerAccountId:u.providerAccountId,counter:a.registrationInfo.counter,credentialID:iV(a.registrationInfo.credentialID),credentialPublicKey:iV(a.registrationInfo.credentialPublicKey),credentialBackedUp:a.registrationInfo.credentialBackedUp,credentialDeviceType:a.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:u,authenticator:l}}async function iF(e,t,r){let{provider:n,adapter:a}=e,i=r&&r.id?await a.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:i?.map(e=>({id:iJ(e.credentialID),type:"public-key",transports:iX(e.transports)}))})}async function iq(e,t,r){let{provider:n,adapter:a}=e,i=r.id?await a.listAuthenticatorsByUserId(r.id):null,o=ra(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:i?.map(e=>({id:iJ(e.credentialID),type:"public-key",transports:iX(e.transports)}))})}function iG(e){let{provider:t,adapter:r}=e;if(!r)throw new A("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new L("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function iJ(e){return new Uint8Array(Buffer.from(e,"base64"))}function iV(e){return Buffer.from(e).toString("base64")}function iX(e){return e?e.split(","):void 0}async function iz(e,t,r,n){if(!t.provider)throw new L("Callback route called without provider");let{query:a,body:i,method:o,headers:s}=e,{provider:c,adapter:u,url:l,callbackUrl:d,pages:f,jwt:p,events:b,callbacks:g,session:{strategy:y,maxAge:v},logger:_}=t,w="jwt"===y;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?i:a;if(t.isOnRedirectProxy&&s?.state){let e=await iN.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return _.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let h=await i$(s,e.cookies,t);h.cookies.length&&n.push(...h.cookies),_.debug("authorization result",h);let{user:y,account:m,profile:x}=h;if(!y||!m||!x)return{redirect:`${l}/signin`,cookies:n};if(u){let{getUserByAccount:e}=u;o=await e({providerAccountId:m.providerAccountId,provider:c.id})}let E=await iY({user:o??y,account:m,profile:x},t);if(E)return{redirect:E,cookies:n};let{user:S,session:R,isNewUser:A}=await nS(r.value,y,m,t);if(w){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},a=await g.jwt({token:e,user:S,account:m,profile:x,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await p.encode({...p,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*v);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:R.sessionToken,options:{...t.cookies.sessionToken.options,expires:R.expires}});if(await b.signIn?.({user:S,account:m,profile:x,isNewUser:A}),A&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=a?.token,i=a?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await u.useVerificationToken({identifier:i,token:await rn(`${e}${o}`)}),l=!!s,h=l&&s.expires.valueOf()<Date.now();if(!l||h||i&&s.identifier!==i)throw new W({hasInvite:l,expired:h});let{identifier:y}=s,m=await u.getUserByEmail(y)??{id:crypto.randomUUID(),email:y,emailVerified:null},_={providerAccountId:m.email,userId:m.id,type:"email",provider:c.id},x=await iY({user:m,account:_},t);if(x)return{redirect:x,cookies:n};let{user:E,session:S,isNewUser:R}=await nS(r.value,m,_,t);if(w){let e={name:E.name,email:E.email,picture:E.image,sub:E.id?.toString()},a=await g.jwt({token:e,user:E,account:_,isNewUser:R,trigger:R?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await p.encode({...p,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*v);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await b.signIn?.({user:E,account:_,isNewUser:R}),R&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=i??{};Object.entries(a??{}).forEach(([e,t])=>l.searchParams.set(e,t));let u=await c.authorize(e,new Request(l,{headers:s,method:o,body:JSON.stringify(i)}));if(u)u.id=u.id?.toString()??crypto.randomUUID();else throw new x;let f={providerAccountId:u.id,type:"credentials",provider:c.id},h=await iY({user:u,account:f,credentials:e},t);if(h)return{redirect:h,cookies:n};let y={name:u.name,email:u.email,picture:u.image,sub:u.id},m=await g.jwt({token:y,user:u,account:f,isNewUser:!1,trigger:"signIn"});if(null===m)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await p.encode({...p,token:m,salt:e}),i=new Date;i.setTime(i.getTime()+1e3*v);let o=r.chunk(a,{expires:i});n.push(...o)}return await b.signIn?.({user:u,account:f}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let a,i,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new h("Invalid action parameter");let c=iG(t);switch(s){case"authenticate":{let t=await iB(c,e,n);a=t.user,i=t.account;break}case"register":{let r=await iK(t,e,n);a=r.user,i=r.account,o=r.authenticator}}await iY({user:a,account:i},t);let{user:u,isNewUser:l,session:y,account:m}=await nS(r.value,a,i,t);if(!m)throw new h("Error creating or finding account");if(o&&u.id&&await c.adapter.createAuthenticator({...o,userId:u.id}),w){let e={name:u.name,email:u.email,picture:u.image,sub:u.id?.toString()},a=await g.jwt({token:e,user:u,account:m,isNewUser:l,trigger:l?"signUp":"signIn"});if(null===a)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await p.encode({...p,token:a,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*v);let s=r.chunk(i,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:y.sessionToken,options:{...t.cookies.sessionToken.options,expires:y.expires}});if(await b.signIn?.({user:u,account:m,isNewUser:l}),l&&f.newUser)return{redirect:`${f.newUser}${f.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new L(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof h)throw t;let e=new m(t,{provider:c.id});throw _.debug("callback route error details",{method:o,query:a,body:i}),e}}async function iY(e,t){let r,{signIn:n,redirect:a}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof h)throw e;throw new y(e)}if(!r)throw new y("AccessDenied");if("string"==typeof r)return await a({url:r,baseUrl:t.url.origin})}async function iZ(e,t,r,n,a){let{adapter:i,jwt:o,events:s,callbacks:c,logger:u,session:{strategy:l,maxAge:d}}=e,f={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},p=t.value;if(!p)return f;if("jwt"===l){try{let r=e.cookies.sessionToken.name,i=await o.decode({...o,token:p,salt:r});if(!i)throw Error("Invalid JWT");let u=await c.jwt({token:i,...n&&{trigger:"update"},session:a}),l=nE(d);if(null!==u){let e={user:{name:u.name,email:u.email,image:u.picture},expires:l.toISOString()},n=await c.session({session:e,token:u});f.body=n;let a=await o.encode({...o,token:u,salt:r}),i=t.chunk(a,{expires:l});f.cookies?.push(...i),await s.session?.({session:n,token:u})}else f.cookies?.push(...t.clean())}catch(e){u.error(new R(e)),f.cookies?.push(...t.clean())}return f}try{let{getSessionAndUser:r,deleteSession:o,updateSession:u}=i,l=await r(p);if(l&&l.session.expires.valueOf()<Date.now()&&(await o(p),l=null),l){let{user:t,session:r}=l,i=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*i,h=nE(d);o<=Date.now()&&await u({sessionToken:p,expires:h});let b=await c.session({session:{...r,user:t},user:t,newSession:a,...n?{trigger:"update"}:{}});f.body=b,f.cookies?.push({name:e.cookies.sessionToken.name,value:p,options:{...e.cookies.sessionToken.options,expires:h}}),await s.session?.({session:b})}else p&&f.cookies?.push(...t.clean())}catch(e){u.error(new N(e))}return f}async function iQ(e,t){let r,n,{logger:a,provider:i}=t,o=i.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(i.issuer),t=await nz(e,{[nj]:i[rd],[nT]:!0}),r=await nQ(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=i.callbackUrl;!t.isOnRedirectProxy&&i.redirectProxyUrl&&(c=i.redirectProxyUrl,n=i.callbackUrl,a.debug("using redirect proxy",{redirect_uri:c,data:n}));let u=Object.assign({response_type:"code",client_id:i.clientId,redirect_uri:c,...i.authorization?.params},Object.fromEntries(i.authorization?.url.searchParams??[]),e);for(let e in u)s.set(e,u[e]);let l=[];i.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await iN.create(t,n);if(d&&(s.set("state",d.value),l.push(d.cookie)),i.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===i.type&&(i.checks=["nonce"]);else{let{value:e,cookie:r}=await iC.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),l.push(r)}let f=await iI.create(t);return f&&(s.set("nonce",f.value),l.push(f.cookie)),"oidc"!==i.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),a.debug("authorization url is ready",{url:o,cookies:l,provider:i}),{redirect:o.toString(),cookies:l}}async function i0(e,t){let r,{body:n}=e,{provider:a,callbacks:i,adapter:o}=t,s=(a.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},u=await o.getUserByEmail(s)??c,l={providerAccountId:s,userId:u.id,type:"email",provider:a.id};try{r=await i.signIn({user:u,account:l,email:{verificationRequest:!0}})}catch(e){throw new y(e)}if(!r)throw new y("AccessDenied");if("string"==typeof r)return{redirect:await i.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:f}=t,p=await a.generateVerificationToken?.()??ra(32),h=new Date(Date.now()+(a.maxAge??86400)*1e3),b=a.secret??t.secret,g=new URL(t.basePath,t.url.origin),m=a.sendVerificationRequest({identifier:s,token:p,expires:h,url:`${g}/callback/${a.id}?${new URLSearchParams({callbackUrl:d,token:p,email:s})}`,provider:a,theme:f,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),v=o.createVerificationToken?.({identifier:s,token:await rn(`${p}${b}`),expires:h});return await Promise.all([m,v]),{redirect:`${g}/verify-request?${new URLSearchParams({provider:a.id,type:a.type})}`}}async function i1(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:a}=await iQ(e.query,r);return a&&t.push(...a),{redirect:n,cookies:t}}case"email":return{...await i0(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function i2(e,t,r){let{jwt:n,events:a,callbackUrl:i,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:i,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await a.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await a.signOut?.({session:e})}}catch(e){o.error(new M(e))}return e.push(...t.clean()),{redirect:i,cookies:e}}async function i5(e,t){let{adapter:r,jwt:n,session:{strategy:a}}=e,i=t.value;if(!i)return null;if("jwt"===a){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:i,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(i);if(e)return e.user}return null}async function i6(e,t,r,n){let a=iG(t),{provider:i}=a,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await i5(t,r),c=s?{user:s,exists:!0}:await i.getUserInfo(t,e),u=c?.user;switch(function(e,t,r){let{user:n,exists:a=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===a)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(a)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return iW(a,e,u,n);case"register":if("string"==typeof u?.email)return iH(a,e,u,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function i3(e,t){let{action:r,providerId:n,error:a,method:i}=e,o=t.skipCSRFCheck===ru,{options:s,cookies:c}=await rm({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===i,csrfDisabled:o}),u=new p(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===i){let t=nx({...s,query:e.query,cookies:c});switch(r){case"callback":return await iz(e,s,u,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(a);case"providers":return t.providers(s.providers);case"session":return await iZ(s,u,c);case"signin":return t.signin(n,a);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await i6(e,s,u,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&ro(r,t),await iz(e,s,u,c);case"session":return ro(r,t),await iZ(s,u,c,!0,e.body?.data);case"signin":return ro(r,t),await i1(e,c,s);case"signout":return ro(r,t),await i2(c,u,s)}}throw new U(`Cannot handle action: ${r}`)}function i4(e,t,r,n,a){let i,o=a?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)i=new URL(s),o&&"/"!==o&&"/"!==i.pathname&&(i.pathname!==o&&t4(a).warn("env-url-basepath-mismatch"),i.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",a=n.endsWith(":")?n:n+":";i=new URL(`${a}//${e}`)}let c=i.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function i8(e,t){let r=t4(t),n=await rt(e,t);if(!n)return Response.json("Bad request.",{status:400});let a=function(e,t){let{url:r}=e,n=[];if(!X&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new H(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new T("Please define a `secret`");let a=e.query?.callbackUrl;if(a&&!z(a,r.origin))return new w(`Invalid callback URL. Received: ${a}`);let{callbackUrl:i}=f(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??i.name];if(o&&!z(o,r.origin))return new w(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:a}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof a||a?.url||(e="userinfo"):e="token":e="authorization",e)return new E(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)Y=!0;else if("email"===t.type)Z=!0;else if("webauthn"===t.type){var c;if(Q=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new h(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new F("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new q(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(Y){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new $("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new P("Must define an authorize() handler to use credentials authentication provider")}let{adapter:u,session:l}=t,d=[];if(Z||l?.strategy==="database"||!l?.strategy&&u)if(Z){if(!u)return new A("Email login requires an adapter");d.push(...ee)}else{if(!u)return new A("Database session requires an adapter");d.push(...et)}if(Q){if(!t.experimental?.enableWebAuthn)return new V("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!u)return new A("WebAuthn requires an adapter");d.push(...er)}if(u){let e=d.filter(e=>!(e in u));if(e.length)return new O(`Required adapter methods were missing: ${e.join(", ")}`)}return X||(X=!0),n}(n,t);if(Array.isArray(a))a.forEach(r.warn);else if(a){if(r.error(a),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:i}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new v(`The error page ${e?.error} should not require authentication`)),rr(nx({theme:i}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let i=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===rl;try{let e=await i3(n,t);if(o)return e;let r=rr(e),a=r.headers.get("Location");if(!i||!a)return r;return Response.json({url:a},{headers:r.headers})}catch(d){r.error(d);let a=d instanceof h;if(a&&o&&!i)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof h&&K.has(d.type)?d.type:"Configuration"});d instanceof x&&s.set("code",d.code);let c=a&&d.kind||"error",u=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,l=`${n.url.origin}${u}?${s}`;if(i)return Response.json({url:l});return Response.redirect(l)}}var i9=r(23794);function i7(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:a}=e.nextUrl;return new i9.NextRequest(n.replace(a,r),e)}function oe(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||t4(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),a=e[`AUTH_${n}_ID`],i=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:a,clientSecret:i,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=a),c.clientSecret??(c.clientSecret=i),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}var ot=r(23874),or=r(79097);async function on(e,t){return i8(new Request(i4("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function oa(e){return"function"==typeof e}function oi(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await (0,or.b)(),n=await e(void 0);return t?.(n),on(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],a=r[1],i=await e(n);return t?.(i),oo([n,a],i)}if(oa(r[0])){let n=r[0];return async(...r)=>{let a=await e(r[0]);return t?.(a),oo(r,a,n)}}let n="req"in r[0]?r[0].req:r[0],a="res"in r[0]?r[0].res:r[1],i=await e(n);return t?.(i),on(new Headers(n.headers),i).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in a?a.headers.append("set-cookie",t):a.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve((0,or.b)()).then(t=>on(t,e).then(e=>e.json()));if(t[0]instanceof Request)return oo([t[0],t[1]],e);if(oa(t[0])){let r=t[0];return async(...t)=>oo(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return on(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function oo(e,t,r){let n=i7(e[0]),a=await on(n.headers,t),i=await a.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:i}));let s=i9.NextResponse.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),a=Object.values(r.pages??{});return(os.has(n)||a.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=i,s=await r(n,e[1])??i9.NextResponse.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=i9.NextResponse.redirect(t)}}let c=new Response(s?.body,s);for(let e of a.headers.getSetCookie())c.headers.append("set-cookie",e);return c}r(3582);let os=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var oc=r(64665);async function ou(e,t={},r,n){let a=new Headers(await (0,or.b)()),{redirect:i=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??a.get("Referer")??"/",u=i4("signin",a.get("x-forwarded-proto"),a,process.env,n);if(!e)return u.searchParams.append("callbackUrl",c),i&&(0,oc.redirect)(u.toString()),u.toString();let l=`${u}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,a=r?.id??n.id;if(a===e){d={id:a,type:r?.type??n.type};break}}if(!d.id){let e=`${u}?${new URLSearchParams({callbackUrl:c})}`;return i&&(0,oc.redirect)(e),e}"credentials"===d.type&&(l=l.replace("signin","callback")),a.set("Content-Type","application/x-www-form-urlencoded");let f=new Request(l,{method:"POST",headers:a,body:new URLSearchParams({...s,callbackUrl:c})}),p=await i8(f,{...n,raw:rl,skipCSRFCheck:ru}),h=await (0,ot.U)();for(let e of p?.cookies??[])h.set(e.name,e.value,e.options);let b=(p instanceof Response?p.headers.get("Location"):p.redirect)??l;return i?(0,oc.redirect)(b):b}async function ol(e,t){let r=new Headers(await (0,or.b)());r.set("Content-Type","application/x-www-form-urlencoded");let n=i4("signout",r.get("x-forwarded-proto"),r,process.env,t),a=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),i=new Request(n,{method:"POST",headers:r,body:a}),o=await i8(i,{...t,raw:rl,skipCSRFCheck:ru}),s=await (0,ot.U)();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?(0,oc.redirect)(o.redirect):o}async function od(e,t){let r=new Headers(await (0,or.b)());r.set("Content-Type","application/json");let n=new Request(i4("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),a=await i8(n,{...t,raw:rl,skipCSRFCheck:ru}),i=await (0,ot.U)();for(let e of a?.cookies??[])i.set(e.name,e.value,e.options);return a.body}var of=r(96330);function op(e){let t={};for(let r in e)void 0!==e[r]&&(t[r]=e[r]);return{data:t}}let oh=globalThis.prisma||new of.PrismaClient;var ob=null;function og(e,t){if("number"!=typeof(e=e||oP))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2b$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(oR(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return en.randomBytes(e)}catch{}if(!ob)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return ob(e)}(oO),oO)),r.join("")}function oy(e,t,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof e&&(r=e,e=void 0),void 0===e)e=oP;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function n(t){ow(function(){try{t(null,og(e))}catch(e){t(e)}})}if(!r)return new Promise(function(e,t){n(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)}function om(e,t){if(void 0===t&&(t=oP),"number"==typeof t&&(t=og(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return oM(e,t)}function ov(e,t,r,n){function a(r){"string"==typeof e&&"number"==typeof t?oy(t,function(t,a){oM(e,a,r,n)}):"string"==typeof e&&"string"==typeof t?oM(e,t,r,n):ow(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!r)return new Promise(function(e,t){a(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);a(r)}function o_(e,t){for(var r=e.length^t.length,n=0;n<e.length;++n)r|=e.charCodeAt(n)^t.charCodeAt(n);return 0===r}var ow="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function ox(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(n+1))==56320?(++n,t+=4):t+=3;return t}var oE="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),oS=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function oR(e,t){var r,n,a=0,i=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;a<t;){if(r=255&e[a++],i.push(oE[r>>2&63]),r=(3&r)<<4,a>=t||(r|=(n=255&e[a++])>>4&15,i.push(oE[63&r]),r=(15&n)<<2,a>=t)){i.push(oE[63&r]);break}r|=(n=255&e[a++])>>6&3,i.push(oE[63&r]),i.push(oE[63&n])}return i.join("")}function oA(e,t){var r,n,a,i,o,s=0,c=e.length,u=0,l=[];if(t<=0)throw Error("Illegal len: "+t);for(;s<c-1&&u<t&&(r=(o=e.charCodeAt(s++))<oS.length?oS[o]:-1,n=(o=e.charCodeAt(s++))<oS.length?oS[o]:-1,-1!=r&&-1!=n)&&(i=r<<2>>>0|(48&n)>>4,l.push(String.fromCharCode(i)),!(++u>=t||s>=c||-1==(a=(o=e.charCodeAt(s++))<oS.length?oS[o]:-1)||(i=(15&n)<<4>>>0|(60&a)>>2,l.push(String.fromCharCode(i)),++u>=t||s>=c)));){;i=(3&a)<<6>>>0|((o=e.charCodeAt(s++))<oS.length?oS[o]:-1),l.push(String.fromCharCode(i)),++u}var d=[];for(s=0;s<u;s++)d.push(l[s].charCodeAt(0));return d}var oO=16,oP=10,oT=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],ok=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],oC=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function oj(e,t,r,n){var a,i=e[t],o=e[t+1];return i^=r[0],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[1],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[2],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[3],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[4],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[5],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[6],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[7],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[8],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[9],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[10],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[11],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[12],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[13],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[14],o^=(n[i>>>24]+n[256|i>>16&255]^n[512|i>>8&255])+n[768|255&i]^r[15],i^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[16],e[t]=o^r[17],e[t+1]=i,e}function oN(e,t){for(var r=0,n=0;r<4;++r)n=n<<8|255&e[t],t=(t+1)%e.length;return{key:n,offp:t}}function oI(e,t,r){for(var n,a=0,i=[0,0],o=t.length,s=r.length,c=0;c<o;c++)a=(n=oN(e,a)).offp,t[c]=t[c]^n.key;for(c=0;c<o;c+=2)i=oj(i,0,t,r),t[c]=i[0],t[c+1]=i[1];for(c=0;c<s;c+=2)i=oj(i,0,t,r),r[c]=i[0],r[c+1]=i[1]}function oD(e,t,r,n,a){var i,o,s=oC.slice(),c=s.length;if(r<4||r>31){if(o=Error("Illegal number of rounds (4-31): "+r),n)return void ow(n.bind(this,o));throw o}if(t.length!==oO){if(o=Error("Illegal salt length: "+t.length+" != "+oO),n)return void ow(n.bind(this,o));throw o}r=1<<r>>>0;var u,l,d,f=0;function p(){if(a&&a(f/r),f<r)for(var i=Date.now();f<r&&(f+=1,oI(e,u,l),oI(t,u,l),!(Date.now()-i>100)););else{for(f=0;f<64;f++)for(d=0;d<c>>1;d++)oj(s,d<<1,u,l);var o=[];for(f=0;f<c;f++)o.push((s[f]>>24&255)>>>0),o.push((s[f]>>16&255)>>>0),o.push((s[f]>>8&255)>>>0),o.push((255&s[f])>>>0);return n?void n(null,o):o}n&&ow(p)}if("function"==typeof Int32Array?(u=new Int32Array(oT),l=new Int32Array(ok)):(u=oT.slice(),l=ok.slice()),!function(e,t,r,n){for(var a,i=0,o=[0,0],s=r.length,c=n.length,u=0;u<s;u++)i=(a=oN(t,i)).offp,r[u]=r[u]^a.key;for(u=0,i=0;u<s;u+=2)i=(a=oN(e,i)).offp,o[0]^=a.key,i=(a=oN(e,i)).offp,o[1]^=a.key,o=oj(o,0,r,n),r[u]=o[0],r[u+1]=o[1];for(u=0;u<c;u+=2)i=(a=oN(e,i)).offp,o[0]^=a.key,i=(a=oN(e,i)).offp,o[1]^=a.key,o=oj(o,0,r,n),n[u]=o[0],n[u+1]=o[1]}(t,e,u,l),void 0!==n)p();else for(;;)if(void 0!==(i=p()))return i||[]}function oM(e,t,r,n){if("string"!=typeof e||"string"!=typeof t){if(a=Error("Invalid string / salt: Not a string"),r)return void ow(r.bind(this,a));throw a}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(a=Error("Invalid salt version: "+t.substring(0,2)),r)return void ow(r.bind(this,a));throw a}if("$"===t.charAt(2))i="\0",o=3;else{if("a"!==(i=t.charAt(2))&&"b"!==i&&"y"!==i||"$"!==t.charAt(3)){if(a=Error("Invalid salt revision: "+t.substring(2,4)),r)return void ow(r.bind(this,a));throw a}o=4}if(t.charAt(o+2)>"$"){if(a=Error("Missing salt rounds"),r)return void ow(r.bind(this,a));throw a}var a,i,o,s=10*parseInt(t.substring(o,o+1),10)+parseInt(t.substring(o+1,o+2),10),c=t.substring(o+3,o+25),u=function(e){for(var t,r,n=0,a=Array(ox(e)),i=0,o=e.length;i<o;++i)(t=e.charCodeAt(i))<128?a[n++]=t:(t<2048?a[n++]=t>>6|192:((64512&t)==55296&&(64512&(r=e.charCodeAt(i+1)))==56320?(t=65536+((1023&t)<<10)+(1023&r),++i,a[n++]=t>>18|240,a[n++]=t>>12&63|128):a[n++]=t>>12|224,a[n++]=t>>6&63|128),a[n++]=63&t|128);return a}(e+=i>="a"?"\0":""),l=oA(c,oO);function d(e){var t=[];return t.push("$2"),i>="a"&&t.push(i),t.push("$"),s<10&&t.push("0"),t.push(s.toString()),t.push("$"),t.push(oR(l,l.length)),t.push(oR(e,4*oC.length-1)),t.join("")}if(void 0===r)return d(oD(u,l,s));oD(u,l,s,function(e,t){e?r(e,null):r(null,d(t))},n)}let oU={setRandomFallback:function(e){ob=e},genSaltSync:og,genSalt:oy,hashSync:om,hash:ov,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&o_(om(e,t.substring(0,t.length-31)),t)},compare:function(e,t,r,n){function a(r){return"string"!=typeof e||"string"!=typeof t?void ow(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t))):60!==t.length?void ow(r.bind(this,null,!1)):void ov(e,t.substring(0,29),function(e,n){e?r(e):r(null,o_(n,t))},n)}if(!r)return new Promise(function(e,t){a(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);a(r)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return ox(e)>72},encodeBase64:function(e,t){return oR(e,t)},decodeBase64:function(e,t){return oA(e,t)}},{handlers:o$,signIn:oL,signOut:oH,auth:oW}=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return oe(r),i8(i7(t),r)};return{handlers:{GET:t,POST:t},auth:oi(e,e=>oe(e)),signIn:async(t,r,n)=>{let a=await e(void 0);return oe(a),ou(t,r,n,a)},signOut:async t=>{let r=await e(void 0);return oe(r),ol(t,r)},unstable_update:async t=>{let r=await e(void 0);return oe(r),od(t,r)}}}oe(e);let t=t=>i8(i7(t),e);return{handlers:{GET:t,POST:t},auth:oi(e),signIn:(t,r,n)=>ou(t,r,n,e),signOut:t=>ol(t,e),unstable_update:t=>od(t,e)}}({adapter:function(e){return{createUser:({id:t,...r})=>e.user.create(op(r)),getUser:t=>e.user.findUnique({where:{id:t}}),getUserByEmail:t=>e.user.findUnique({where:{email:t}}),async getUserByAccount(t){let r=await e.account.findUnique({where:{provider_providerAccountId:t},include:{user:!0}});return r?.user??null},updateUser:({id:t,...r})=>e.user.update({where:{id:t},...op(r)}),deleteUser:t=>e.user.delete({where:{id:t}}),linkAccount:t=>e.account.create({data:t}),unlinkAccount:t=>e.account.delete({where:{provider_providerAccountId:t}}),async getSessionAndUser(t){let r=await e.session.findUnique({where:{sessionToken:t},include:{user:!0}});if(!r)return null;let{user:n,...a}=r;return{user:n,session:a}},createSession:t=>e.session.create(op(t)),updateSession:t=>e.session.update({where:{sessionToken:t.sessionToken},...op(t)}),deleteSession:t=>e.session.delete({where:{sessionToken:t}}),async createVerificationToken(t){let r=await e.verificationToken.create(op(t));return"id"in r&&r.id&&delete r.id,r},async useVerificationToken(t){try{let r=await e.verificationToken.delete({where:{identifier_token:t}});return"id"in r&&r.id&&delete r.id,r}catch(e){if(e instanceof of.Prisma.PrismaClientKnownRequestError&&"P2025"===e.code)return null;throw e}},getAccount:async(t,r)=>e.account.findFirst({where:{providerAccountId:t,provider:r}}),createAuthenticator:async t=>e.authenticator.create(op(t)),getAuthenticator:async t=>e.authenticator.findUnique({where:{credentialID:t}}),listAuthenticatorsByUserId:async t=>e.authenticator.findMany({where:{userId:t}}),updateAuthenticatorCounter:async(t,r)=>e.authenticator.update({where:{credentialID:t},data:{counter:r}})}}(oh),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[function(e){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:e}},{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:{credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await oh.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!t||!t.password||!await oU.compare(e.password,t.password))return null;return{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}}}],callbacks:{async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,n=t.pathname.startsWith("/dashboard"),a=t.pathname.startsWith("/manuscripts"),i=t.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",t.pathname),!n&&!a&&!i||r},session:async({session:e,token:t})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",t),t?.sub&&(e.user.id=t.sub,t.name&&(e.user.name=t.name),t.email&&(e.user.email=t.email),t.picture&&(e.user.image=t.picture),t.role&&(e.user.role=t.role)),e),jwt:async({token:e,user:t,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",t),console.log("\uD83D\uDD10 JWT callback - account:",r),t&&(e.sub=t.id,e.name=t.name,e.email=t.email,e.picture=t.image,e.role=t.role),e)}});var oB=r(7944);async function oK(e){let t=await oW();if(!t?.user?.id)throw Error("Unauthorized");try{return await oh.notification.updateMany({where:{id:e,userId:t.user.id},data:{isRead:!0}}),(0,oB.revalidatePath)("/dashboard/notifications"),(0,oB.revalidatePath)("/dashboard"),{success:!0}}catch(e){throw console.error("Failed to mark notification as read:",e),Error("Failed to update notification")}}async function oF(){let e=await oW();if(!e?.user?.id)throw Error("Unauthorized");try{return await oh.notification.updateMany({where:{userId:e.user.id,isRead:!1},data:{isRead:!0}}),(0,oB.revalidatePath)("/dashboard/notifications"),(0,oB.revalidatePath)("/dashboard"),{success:!0}}catch(e){throw console.error("Failed to mark all notifications as read:",e),Error("Failed to update notifications")}}async function oq(){let e=await oW();if(!e?.user?.id)return 0;try{return await oh.notification.count({where:{userId:e.user.id,isRead:!1}})}catch(e){return console.error("Failed to get unread notification count:",e),0}}(0,r(33331).D)([oK,oF,oq]),(0,u.A)(oK,"401017a333c37c4c5862c5d6321d7519f5e672aaaa",null),(0,u.A)(oF,"00b7f0d14f6ea68729c29c773d01151c448d4354ca",null),(0,u.A)(oq,"00fbe3ba0344bf0a5130669dd2edd62442ad81bf23",null)},70830:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{METADATA_BOUNDARY_NAME:function(){return r},OUTLET_BOUNDARY_NAME:function(){return a},VIEWPORT_BOUNDARY_NAME:function(){return n}});let r="__next_metadata_boundary__",n="__next_viewport_boundary__",a="__next_outlet_boundary__"},71490:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return s}});let n=r(25104),a=r(35333),i=r(222),o=r(66455);function s(e){let t=(0,o.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,n.removeTrailingSlash)(t)),e.buildId&&(t=(0,i.addPathSuffix)((0,a.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,a.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,i.addPathSuffix)(t,"/"):(0,n.removeTrailingSlash)(t)}},72150:(e,t)=>{"use strict";function r(e){if(!e.body)return[e,e];let[t,r]=e.body.tee(),n=new Response(t,{status:e.status,statusText:e.statusText,headers:e.headers});Object.defineProperty(n,"url",{value:e.url});let a=new Response(r,{status:e.status,statusText:e.statusText,headers:e.headers});return Object.defineProperty(a,"url",{value:e.url}),[n,a]}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"cloneResponse",{enumerable:!0,get:function(){return r}})},73214:e=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),a=r(172),i=r(930),o="context",s=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),a=r(912),i=r(957),o=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,o.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:i.DiagLogLevel.INFO})=>{var n,s,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let u=(0,o.getGlobal)("diag"),l=(0,a.createLogLevelDiagLogger)(null!=(s=r.logLevel)?s:i.DiagLogLevel.INFO,e);if(u&&!r.suppressOverrideMessage){let e=null!=(c=Error().stack)?c:"<failed to generate stacktrace>";u.warn(`Current logger will be overwritten from ${e}`),l.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",l,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),a=r(172),i=r(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),a=r(874),i=r(194),o=r(277),s=r(369),c=r(930),u="propagation",l=new a.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(u,e,c.DiagAPI.instance())}inject(e,t,r=i.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=i.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(u,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(u)||l}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),a=r(846),i=r(139),o=r(607),s=r(930),c="trace";class u{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider,this.wrapSpanContext=i.wrapSpanContext,this.isSpanContextValid=i.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,s.DiagAPI.instance()),this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=u},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),a=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function i(e){return e.getValue(a)||void 0}t.getBaggage=i,t.getActiveBaggage=function(){return i(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(a,t)},t.deleteBaggage=function(e){return e.deleteValue(a)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),a=r(993),i=r(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:i.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class a{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=a},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let a=new r(t._currentContext);return a._currentContext.set(e,n),a},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class a{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return i("debug",this._namespace,e)}error(...e){return i("error",this._namespace,e)}info(...e){return i("info",this._namespace,e)}warn(...e){return i("warn",this._namespace,e)}verbose(...e){return i("verbose",this._namespace,e)}}function i(e,t,r){let a=(0,n.getGlobal)("diag");if(a)return r.unshift(t),a[e](...r)}t.DiagComponentLogger=a},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let a=t[r];return"function"==typeof a&&e>=n?a.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),a=r(521),i=r(130),o=a.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var i;let o=c[s]=null!=(i=c[s])?i:{version:a.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(o.version!==a.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${a.VERSION}`);return r.error(t.stack||t.message),!1}return o[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=c[s])?void 0:t.version;if(n&&(0,i.isCompatible)(n))return null==(r=c[s])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);let r=c[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),a=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function i(e){let t=new Set([e]),r=new Set,n=e.match(a);if(!n)return()=>!1;let i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(a);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||i.major!==s.major)return o(e);if(0===i.major)return i.minor===s.minor&&i.patch<=s.patch?(t.add(e),!0):o(e);return i.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=i,t.isCompatible=i(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class a extends n{add(e,t){}}t.NoopCounterMetric=a;class i extends n{add(e,t){}}t.NoopUpDownCounterMetric=i;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class c extends s{}t.NoopObservableCounterMetric=c;class u extends s{}t.NoopObservableGaugeMetric=u;class l extends s{}t.NoopObservableUpDownCounterMetric=l,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new a,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new i,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new u,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new l,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class a{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=a,t.NOOP_METER_PROVIDER=new a},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:global},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class a{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=a},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),a=r(607),i=r(403),o=r(139),s=n.ContextAPI.getInstance();class c{startSpan(e,t,r=s.active()){var n;if(null==t?void 0:t.root)return new i.NonRecordingSpan;let c=r&&(0,a.getSpanContext)(r);return"object"==typeof(n=c)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(c)?new i.NonRecordingSpan(c):new i.NonRecordingSpan}startActiveSpan(e,t,r,n){let i,o,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(i=t,c=r):(i=t,o=r,c=n);let u=null!=o?o:s.active(),l=this.startSpan(e,i,u),d=(0,a.setSpan)(u,l);return s.with(d,c,void 0,l)}}t.NoopTracer=c},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class a{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=a},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class a{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=a},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),a=new(r(124)).NoopTracerProvider;class i{getTracer(e,t,r){var a;return null!=(a=this.getDelegateTracer(e,t,r))?a:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:a}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=i},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),a=r(403),i=r(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function c(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(i.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return c(e,new a.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class a{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),a=r.indexOf("=");if(-1!==a){let i=r.slice(0,a),o=r.slice(a+1,t.length);(0,n.validateKey)(i)&&(0,n.validateValue)(o)&&e.set(i,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new a;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=a},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,a=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,i=RegExp(`^(?:${n}|${a})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return i.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),a=r(403),i=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return i.test(e)&&e!==n.INVALID_TRACEID}function c(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=c,t.isSpanContextValid=function(e){return s(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new a.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var i=r[e]={exports:{}},o=!0;try{t[e].call(i.exports,i,i.exports,n),o=!1}finally{o&&delete r[e]}return i.exports}n.ab=__dirname+"/";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=n(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=n(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=n(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var i=n(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return i.DiagLogLevel}});var o=n(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=n(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var c=n(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var u=n(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return u.ProxyTracer}});var l=n(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return l.ProxyTracerProvider}});var d=n(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var f=n(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return f.SpanKind}});var p=n(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var h=n(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return h.TraceFlags}});var b=n(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return b.createTraceState}});var g=n(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return g.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return g.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return g.isValidSpanId}});var y=n(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return y.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return y.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return y.INVALID_SPAN_CONTEXT}});let m=n(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return m.context}});let v=n(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return v.diag}});let _=n(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return _.metrics}});let w=n(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return w.propagation}});let x=n(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return x.trace}}),a.default={context:m.context,diag:v.diag,metrics:_.metrics,propagation:w.propagation,trace:x.trace}})(),e.exports=a})()},74932:(e,t,r)=>{"use strict";e.exports=r(65239).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},75124:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Postpone:function(){return S},abortAndThrowOnSynchronousRequestDataAccess:function(){return x},abortOnSynchronousPlatformIOAccess:function(){return _},accessedDynamicData:function(){return j},annotateDynamicAccess:function(){return $},consumeDynamicAccess:function(){return N},createDynamicTrackingState:function(){return f},createDynamicValidationState:function(){return p},createHangingInputAbortSignal:function(){return U},createPostponedAbortSignal:function(){return M},formatDynamicAPIAccesses:function(){return I},getFirstDynamicReason:function(){return h},isDynamicPostpone:function(){return O},isPrerenderInterruptedError:function(){return C},markCurrentScopeAsDynamic:function(){return b},postponeWithTracking:function(){return R},throwIfDisallowedDynamic:function(){return q},throwToInterruptStaticGeneration:function(){return y},trackAllowedDynamicAccess:function(){return F},trackDynamicDataInDynamicRender:function(){return m},trackFallbackParamAccessed:function(){return g},trackSynchronousPlatformIOAccessInDev:function(){return w},trackSynchronousRequestDataAccessInDev:function(){return E},useDynamicRouteParams:function(){return L}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(7153)),a=r(38248),i=r(66224),o=r(63033),s=r(29294),c=r(37461),u=r(70830),l=r(97748),d="function"==typeof n.default.unstable_postpone;function f(e){return{isDebugDynamicAccesses:e,dynamicAccesses:[],syncDynamicExpression:void 0,syncDynamicErrorWithStack:null}}function p(){return{hasSuspendedDynamic:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasSyncDynamicErrors:!1,dynamicErrors:[]}}function h(e){var t;return null==(t=e.dynamicAccesses[0])?void 0:t.expression}function b(e,t,r){if((!t||"cache"!==t.type&&"unstable-cache"!==t.type)&&!e.forceDynamic&&!e.forceStatic){if(e.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${r}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(t){if("prerender-ppr"===t.type)R(e.route,r,t.dynamicTracking);else if("prerender-legacy"===t.type){t.revalidate=0;let n=Object.defineProperty(new a.DynamicServerError(`Route ${e.route} couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw e.dynamicUsageDescription=r,e.dynamicUsageStack=n.stack,n}}}}function g(e,t){let r=o.workUnitAsyncStorage.getStore();r&&"prerender-ppr"===r.type&&R(e.route,t,r.dynamicTracking)}function y(e,t,r){let n=Object.defineProperty(new a.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function m(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function v(e,t,r){let n=k(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let a=r.dynamicTracking;a&&a.dynamicAccesses.push({stack:a.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}function _(e,t,r,n){let a=n.dynamicTracking;a&&null===a.syncDynamicErrorWithStack&&(a.syncDynamicExpression=t,a.syncDynamicErrorWithStack=r),v(e,t,n)}function w(e){e.prerenderPhase=!1}function x(e,t,r,n){if(!1===n.controller.signal.aborted){let a=n.dynamicTracking;a&&null===a.syncDynamicErrorWithStack&&(a.syncDynamicExpression=t,a.syncDynamicErrorWithStack=r,!0===n.validating&&(a.syncDynamicLogged=!0)),v(e,t,n)}throw k(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}let E=w;function S({reason:e,route:t}){let r=o.workUnitAsyncStorage.getStore();R(t,e,r&&"prerender-ppr"===r.type?r.dynamicTracking:null)}function R(e,t,r){D(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.default.unstable_postpone(A(e,t))}function A(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function O(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&P(e.message)}function P(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===P(A("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let T="NEXT_PRERENDER_INTERRUPTED";function k(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest=T,t}function C(e){return"object"==typeof e&&null!==e&&e.digest===T&&"name"in e&&"message"in e&&e instanceof Error}function j(e){return e.length>0}function N(e,t){return e.dynamicAccesses.push(...t.dynamicAccesses),e.dynamicAccesses}function I(e){return e.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function D(){if(!d)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})}function M(e){D();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}function U(e){let t=new AbortController;return e.cacheSignal?e.cacheSignal.inputReady().then(()=>{t.abort()}):(0,l.scheduleOnNextTick)(()=>t.abort()),t.signal}function $(e,t){let r=t.dynamicTracking;r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:e})}function L(e){let t=s.workAsyncStorage.getStore();if(t&&t.isStaticGeneration&&t.fallbackRouteParams&&t.fallbackRouteParams.size>0){let r=o.workUnitAsyncStorage.getStore();r&&("prerender"===r.type?n.default.use((0,c.makeHangingPromise)(r.renderSignal,e)):"prerender-ppr"===r.type?R(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&y(e,t,r))}}let H=/\n\s+at Suspense \(<anonymous>\)/,W=RegExp(`\\n\\s+at ${u.METADATA_BOUNDARY_NAME}[\\n\\s]`),B=RegExp(`\\n\\s+at ${u.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),K=RegExp(`\\n\\s+at ${u.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function F(e,t,r,n,a){if(!K.test(t)){if(W.test(t)){r.hasDynamicMetadata=!0;return}if(B.test(t)){r.hasDynamicViewport=!0;return}if(H.test(t)){r.hasSuspendedDynamic=!0;return}else if(n.syncDynamicErrorWithStack||a.syncDynamicErrorWithStack){r.hasSyncDynamicErrors=!0;return}else{let n=function(e,t){let r=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r.stack="Error: "+e+t,r}(`Route "${e}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,t);r.dynamicErrors.push(n);return}}}function q(e,t,r,n){let a,o,s;if(r.syncDynamicErrorWithStack?(a=r.syncDynamicErrorWithStack,o=r.syncDynamicExpression,s=!0===r.syncDynamicLogged):n.syncDynamicErrorWithStack?(a=n.syncDynamicErrorWithStack,o=n.syncDynamicExpression,s=!0===n.syncDynamicLogged):(a=null,o=void 0,s=!1),t.hasSyncDynamicErrors&&a)throw s||console.error(a),new i.StaticGenBailoutError;let c=t.dynamicErrors;if(c.length){for(let e=0;e<c.length;e++)console.error(c[e]);throw new i.StaticGenBailoutError}if(!t.hasSuspendedDynamic){if(t.hasDynamicMetadata){if(a)throw console.error(a),Object.defineProperty(new i.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that could not finish rendering before ${o} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E608",enumerable:!1,configurable:!0});throw Object.defineProperty(new i.StaticGenBailoutError(`Route "${e}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateMetadata\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E534",enumerable:!1,configurable:!0})}else if(t.hasDynamicViewport){if(a)throw console.error(a),Object.defineProperty(new i.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that could not finish rendering before ${o} was used. Follow the instructions in the error for this expression to resolve.`),"__NEXT_ERROR_CODE",{value:"E573",enumerable:!1,configurable:!0});throw Object.defineProperty(new i.StaticGenBailoutError(`Route "${e}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or external data (\`fetch(...)\`, etc...) but the rest of the route was static or only used cached data (\`"use cache"\`). If you expected this route to be prerenderable update your \`generateViewport\` to not use Request data and only use cached external data. Otherwise, add \`await connection()\` somewhere within this route to indicate explicitly it should not be prerendered.`),"__NEXT_ERROR_CODE",{value:"E590",enumerable:!1,configurable:!0})}}}},75626:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(88870),a=r(97748),i=r(77777);!function(e,t){Object.keys(e).forEach(function(r){"default"===r||Object.prototype.hasOwnProperty.call(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})})}(r(14245),t);class o{constructor(e){this.batcher=n.Batcher.create({cacheKeyFn:({key:e,isOnDemandRevalidate:t})=>`${e}-${t?"1":"0"}`,schedulerFn:a.scheduleOnNextTick}),this.minimalMode=e}async get(e,t,r){if(!e)return t({hasResolved:!1,previousCacheEntry:null});let{incrementalCache:n,isOnDemandRevalidate:a=!1,isFallback:o=!1,isRoutePPREnabled:s=!1}=r,c=await this.batcher.batch({key:e,isOnDemandRevalidate:a},async(c,u)=>{var l;if(this.minimalMode&&(null==(l=this.previousCacheItem)?void 0:l.key)===c&&this.previousCacheItem.expiresAt>Date.now())return this.previousCacheItem.entry;let d=(0,i.routeKindToIncrementalCacheKind)(r.routeKind),f=!1,p=null;try{if((p=this.minimalMode?null:await n.get(e,{kind:d,isRoutePPREnabled:r.isRoutePPREnabled,isFallback:o}))&&!a&&(u(p),f=!0,!p.isStale||r.isPrefetch))return null;let l=await t({hasResolved:f,previousCacheEntry:p,isRevalidating:!0});if(!l)return this.minimalMode&&(this.previousCacheItem=void 0),null;let h=await (0,i.fromResponseCacheEntry)({...l,isMiss:!p});if(!h)return this.minimalMode&&(this.previousCacheItem=void 0),null;return a||f||(u(h),f=!0),h.cacheControl&&(this.minimalMode?this.previousCacheItem={key:c,entry:h,expiresAt:Date.now()+1e3}:await n.set(e,h.value,{cacheControl:h.cacheControl,isRoutePPREnabled:s,isFallback:o})),h}catch(t){if(null==p?void 0:p.cacheControl){let t=Math.min(Math.max(p.cacheControl.revalidate||3,3),30),r=void 0===p.cacheControl.expire?void 0:Math.max(t+3,p.cacheControl.expire);await n.set(e,p.value,{cacheControl:{revalidate:t,expire:r},isRoutePPREnabled:s,isFallback:o})}if(f)return console.error(t),null;throw t}});return(0,i.toResponseCacheEntry)(c)}}},75659:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(7153));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let i={current:null},o="function"==typeof n.cache?n.cache:e=>e,s=console.warn;function c(e){return function(...t){s(e(...t))}}o(e=>{try{s(i.current)}finally{i.current=null}})},75738:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NextRequestAdapter:function(){return d},ResponseAborted:function(){return c},ResponseAbortedName:function(){return s},createAbortController:function(){return u},signalFromNodeResponse:function(){return l}});let n=r(10920),a=r(40857),i=r(81661),o=r(44927),s="ResponseAborted";class c extends Error{constructor(...e){super(...e),this.name=s}}function u(e){let t=new AbortController;return e.once("close",()=>{e.writableFinished||t.abort(new c)}),t}function l(e){let{errored:t,destroyed:r}=e;if(t||r)return AbortSignal.abort(t??new c);let{signal:n}=u(e);return n}class d{static fromBaseNextRequest(e,t){if((0,o.isNodeNextRequest)(e))return d.fromNodeNextRequest(e,t);throw Object.defineProperty(Error("Invariant: Unsupported NextRequest type"),"__NEXT_ERROR_CODE",{value:"E345",enumerable:!1,configurable:!0})}static fromNodeNextRequest(e,t){let r,o=null;if("GET"!==e.method&&"HEAD"!==e.method&&e.body&&(o=e.body),e.url.startsWith("http"))r=new URL(e.url);else{let t=(0,n.getRequestMeta)(e,"initURL");r=t&&t.startsWith("http")?new URL(e.url,t):new URL(e.url,"http://n")}return new i.NextRequest(r,{method:e.method,headers:(0,a.fromNodeOutgoingHttpHeaders)(e.headers),duplex:"half",signal:t,...t.aborted?{}:{body:o}})}static fromWebNextRequest(e){let t=null;return"GET"!==e.method&&"HEAD"!==e.method&&(t=e.body),new i.NextRequest(e.url,{method:e.method,headers:(0,a.fromNodeOutgoingHttpHeaders)(e.headers),duplex:"half",signal:e.request.signal,...e.request.signal.aborted?{}:{body:t}})}}},77777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromResponseCacheEntry:function(){return o},routeKindToIncrementalCacheKind:function(){return c},toResponseCacheEntry:function(){return s}});let n=r(14245),a=function(e){return e&&e.__esModule?e:{default:e}}(r(95247)),i=r(46729);async function o(e){var t,r;return{...e,value:(null==(t=e.value)?void 0:t.kind)===n.CachedRouteKind.PAGES?{kind:n.CachedRouteKind.PAGES,html:await e.value.html.toUnchunkedString(!0),pageData:e.value.pageData,headers:e.value.headers,status:e.value.status}:(null==(r=e.value)?void 0:r.kind)===n.CachedRouteKind.APP_PAGE?{kind:n.CachedRouteKind.APP_PAGE,html:await e.value.html.toUnchunkedString(!0),postponed:e.value.postponed,rscData:e.value.rscData,headers:e.value.headers,status:e.value.status,segmentData:e.value.segmentData}:e.value}}async function s(e){var t,r;return e?{isMiss:e.isMiss,isStale:e.isStale,cacheControl:e.cacheControl,isFallback:e.isFallback,value:(null==(t=e.value)?void 0:t.kind)===n.CachedRouteKind.PAGES?{kind:n.CachedRouteKind.PAGES,html:a.default.fromStatic(e.value.html),pageData:e.value.pageData,headers:e.value.headers,status:e.value.status}:(null==(r=e.value)?void 0:r.kind)===n.CachedRouteKind.APP_PAGE?{kind:n.CachedRouteKind.APP_PAGE,html:a.default.fromStatic(e.value.html),rscData:e.value.rscData,headers:e.value.headers,status:e.value.status,postponed:e.value.postponed,segmentData:e.value.segmentData}:e.value}:null}function c(e){switch(e){case i.RouteKind.PAGES:return n.IncrementalCacheKind.PAGES;case i.RouteKind.APP_PAGE:return n.IncrementalCacheKind.APP_PAGE;case i.RouteKind.IMAGE:return n.IncrementalCacheKind.IMAGE;case i.RouteKind.APP_ROUTE:return n.IncrementalCacheKind.APP_ROUTE;default:throw Object.defineProperty(Error(`Unexpected route kind ${e}`),"__NEXT_ERROR_CODE",{value:"E64",enumerable:!1,configurable:!0})}}},78745:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return a}});let n=r(10492);class a extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new a}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,a){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return n.ReflectAdapter.get(t,o,a)},set(t,r,a,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,a,i);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return n.ReflectAdapter.set(t,s??r,a,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},79097:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let n=r(78745),a=r(29294),i=r(63033),o=r(75124),s=r(66224),c=r(37461),u=r(75659),l=(r(97748),r(16296));function d(){let e=a.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,u=t;let n=f.get(u);if(n)return n;let a=(0,c.makeHangingPromise)(u.renderSignal,"`headers()`");return f.set(u,a),Object.defineProperties(a,{append:{value:function(){let e=`\`headers().append(${h(arguments[0])}, ...)\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},delete:{value:function(){let e=`\`headers().delete(${h(arguments[0])})\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},get:{value:function(){let e=`\`headers().get(${h(arguments[0])})\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},has:{value:function(){let e=`\`headers().has(${h(arguments[0])})\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},set:{value:function(){let e=`\`headers().set(${h(arguments[0])}, ...)\``,t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},keys:{value:function(){let e="`headers().keys()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},values:{value:function(){let e="`headers().values()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},entries:{value:function(){let e="`headers().entries()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=g(r,e);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}}}),a}else"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t);(0,o.trackDynamicDataInDynamicRender)(e,t)}return p((0,i.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function h(e){return"string"==typeof e?`'${e}'`:"..."}let b=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},79535:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return o}});let n=r(90372),a=r(84212);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,a.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function o(e){return e.replace(/\.rsc($|\?)/,"$1")}},79551:e=>{"use strict";e.exports=require("url")},81452:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(4431).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81661:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERNALS:function(){return s},NextRequest:function(){return c}});let n=r(49761),a=r(40857),i=r(25220),o=r(14659),s=Symbol("internal request");class c extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,a.validateURL)(r),t.body&&"half"!==t.duplex&&(t.duplex="half"),e instanceof Request?super(e,t):super(r,t);let i=new n.NextURL(r,{headers:(0,a.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new o.RequestCookies(this.headers),nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get nextUrl(){return this[s].nextUrl}get page(){throw new i.RemovedPageError}get ua(){throw new i.RemovedUAError}get url(){return this[s].url}}},82362:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return d},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return l},responseCookiesToRequestCookies:function(){return g},wrapWithMutableAccessCheck:function(){return p}});let n=r(14659),a=r(10492),i=r(29294),o=r(63033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return a.ReflectAdapter.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function l(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=l(t);if(0===r.length)return!1;let a=new n.ResponseCookies(e),i=a.getAll();for(let e of r)a.set(e);for(let e of i)a.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],s=new Set,c=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of o){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},l=new Proxy(r,{get(e,t,r){switch(t){case u:return o;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),l}finally{c()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),l}finally{c()}};default:return a.ReflectAdapter.get(e,t,r)}}});return l}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return b("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return b("cookies().set"),e.set(...r),t};default:return a.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function b(e){if(!h((0,o.getExpectedRequestStore)(e)))throw new s}function g(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},83249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(37413),a=r(84168);async function i({children:e}){return(0,n.jsx)("div",{className:"bg-gray-100 pt-20",children:(0,n.jsxs)("div",{className:"flex min-h-[calc(100vh-5rem)]",children:[(0,n.jsx)(a.default,{}),(0,n.jsx)("div",{className:"flex-1 lg:ml-0 w-full lg:w-auto",children:e})]})})}},83409:(e,t,r)=>{"use strict";function n(e){throw Object.defineProperty(Error("cacheLife() is only available with the experimental.useCache config."),"__NEXT_ERROR_CODE",{value:"E627",enumerable:!1,configurable:!0})}Object.defineProperty(t,"F",{enumerable:!0,get:function(){return n}}),r(29294),r(63033)},84168:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\Sidebar.tsx","default")},84212:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}function n(e){return e.startsWith("@")&&"@children"!==e}function a(e,t){if(e.includes(i)){let e=JSON.stringify(t);return"{}"!==e?i+"?"+e:i}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return o},PAGE_SEGMENT_KEY:function(){return i},addSearchParamsIfPageSegment:function(){return a},isGroupSegment:function(){return r},isParallelRouteSegment:function(){return n}});let i="__PAGE__",o="__DEFAULT__"},86834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(32913).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87401:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationItem.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationItem.tsx","default")},88477:(e,t)=>{"use strict";function r(e,t){if(0===t.length)return 0;if(0===e.length||t.length>e.length)return -1;for(let r=0;r<=e.length-t.length;r++){let n=!0;for(let a=0;a<t.length;a++)if(e[r+a]!==t[a]){n=!1;break}if(n)return r}return -1}function n(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function a(e,t){let n=r(e,t);if(0===n)return e.subarray(t.length);if(!(n>-1))return e;{let r=new Uint8Array(e.length-t.length);return r.set(e.slice(0,n)),r.set(e.slice(n+t.length),n),r}}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{indexOfUint8Array:function(){return r},isEquivalentUint8Arrays:function(){return n},removeFromUint8Array:function(){return a}})},88870:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Batcher",{enumerable:!0,get:function(){return a}});let n=r(13944);class a{constructor(e,t=e=>e()){this.cacheKeyFn=e,this.schedulerFn=t,this.pending=new Map}static create(e){return new a(null==e?void 0:e.cacheKeyFn,null==e?void 0:e.schedulerFn)}async batch(e,t){let r=this.cacheKeyFn?await this.cacheKeyFn(e):e;if(null===r)return t(r,Promise.resolve);let a=this.pending.get(r);if(a)return a;let{promise:i,resolve:o,reject:s}=new n.DetachedPromise;return this.pending.set(r,i),this.schedulerFn(async()=>{try{let e=await t(r,o);o(e)}catch(e){s(e)}finally{this.pending.delete(r)}}),i}}},90036:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,i={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,a]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=a?a:"true"))}catch{}}return t}function c(e){if(!e)return;let[[t,r],...n]=s(e),{domain:a,expires:i,httponly:o,maxage:c,path:d,samesite:f,secure:p,partitioned:h,priority:b}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var g,y,m={name:t,value:decodeURIComponent(r),domain:a,...i&&{expires:new Date(i)},...o&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:d,...f&&{sameSite:u.includes(g=(g=f).toLowerCase())?g:void 0},...p&&{secure:!0},...b&&{priority:l.includes(y=(y=b).toLowerCase())?y:void 0},...h&&{partitioned:!0}};let e={};for(let t in m)m[t]&&(e[t]=m[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(i,{RequestCookies:()=>d,ResponseCookies:()=>f,parseCookie:()=>s,parseSetCookie:()=>c,stringifyCookie:()=>o}),e.exports=((e,i,o,s)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let c of n(i))a.call(e,c)||c===o||t(e,c,{get:()=>i[c],enumerable:!(s=r(i,c))||s.enumerable});return e})(t({},"__esModule",{value:!0}),i);var u=["strict","lax","none"],l=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},f=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let a=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(a)?a:function(e){if(!e)return[];var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(a)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,a=this._parsed;return a.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(a,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},90372:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},91199:(e,t,r)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n.registerServerReference}});let n=r(74932)},91339:(e,t)=>{"use strict";function r(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageResponse",{enumerable:!0,get:function(){return r}})},91777:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(32913).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94862:(e,t,r)=>{Promise.resolve().then(r.bind(r,8658))},95247:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(34360),a=r(19136);class i{static fromStatic(e){return new i(e,{metadata:{}})}constructor(e,{contentType:t,waitUntil:r,metadata:n}){this.response=e,this.contentType=t,this.metadata=n,this.waitUntil=r}assignMetadata(e){Object.assign(this.metadata,e)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedBuffer(e=!1){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be unchunked"),"__NEXT_ERROR_CODE",{value:"E274",enumerable:!1,configurable:!0});if("string"!=typeof this.response){if(!e)throw Object.defineProperty(Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E81",enumerable:!1,configurable:!0});return(0,n.streamToBuffer)(this.readable)}return Buffer.from(this.response)}toUnchunkedString(e=!1){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be unchunked"),"__NEXT_ERROR_CODE",{value:"E274",enumerable:!1,configurable:!0});if("string"!=typeof this.response){if(!e)throw Object.defineProperty(Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E81",enumerable:!1,configurable:!0});return(0,n.streamToString)(this.readable)}return this.response}get readable(){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be streamed"),"__NEXT_ERROR_CODE",{value:"E14",enumerable:!1,configurable:!0});if("string"==typeof this.response)throw Object.defineProperty(Error("Invariant: static responses cannot be streamed"),"__NEXT_ERROR_CODE",{value:"E151",enumerable:!1,configurable:!0});return Buffer.isBuffer(this.response)?(0,n.streamFromBuffer)(this.response):Array.isArray(this.response)?(0,n.chainStreams)(...this.response):this.response}chain(e){let t;if(null===this.response)throw Object.defineProperty(Error("Invariant: response is null. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E258",enumerable:!1,configurable:!0});(t="string"==typeof this.response?[(0,n.streamFromString)(this.response)]:Array.isArray(this.response)?this.response:Buffer.isBuffer(this.response)?[(0,n.streamFromBuffer)(this.response)]:[this.response]).push(e),this.response=t}async pipeTo(e){try{await this.readable.pipeTo(e,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await e.close()}catch(t){if((0,a.isAbortError)(t))return void await e.abort(t);throw t}}async pipeToNodeResponse(e){await (0,a.pipeToNodeResponse)(this.readable,e,this.waitUntil)}}},95521:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(60687),a=r(6475);let i=(0,a.createServerReference)("401017a333c37c4c5862c5d6321d7519f5e672aaaa",a.callServer,void 0,a.findSourceMapURL,"markNotificationAsRead");var o=r(16189),s=r(69587),c=r(43210);function u({notification:e}){let t=(0,o.useRouter)(),[r,a]=(0,c.useState)(!1),u=async()=>{a(!0);try{e.isRead||await i(e.id),"MANUSCRIPT_SUBMISSION"===e.type&&e.relatedId?t.push(`/manuscripts/${e.relatedId}`):t.push("/dashboard")}catch(e){console.error("Failed to handle notification click:",e)}finally{a(!1)}};return(0,n.jsx)("div",{onClick:u,className:`p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${!e.isRead?"bg-blue-50 border-blue-200 hover:bg-blue-100":"bg-white border-gray-200 hover:bg-gray-50"} ${r?"opacity-50":""}`,children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("div",{className:"text-2xl",children:(e=>{switch(e){case"MANUSCRIPT_SUBMISSION":return(0,n.jsx)(s.t69,{});case"REVIEW_COMPLETED":return(0,n.jsx)(s.CMH,{});case"REVIEW_REQUESTED":return(0,n.jsx)(s.Ny1,{});default:return(0,n.jsx)(s.sdT,{})}})(e.type)}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h3",{className:`font-medium truncate ${!e.isRead?"text-blue-900":"text-gray-900"}`,children:e.title}),!e.isRead&&(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full ml-2"})]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-1 line-clamp-2",children:e.message}),(0,n.jsx)("p",{className:"text-xs text-gray-400 mt-2",children:(e=>{let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/36e5);return r<1?"Just now":r<24?`${r}h ago`:r<48?"Yesterday":t.toLocaleDateString()})(e.createdAt)})]})]})})}},95616:(e,t,r)=>{Promise.resolve().then(r.bind(r,95521))},96197:(e,t)=>{"use strict";function r(e,t,r){if(e)for(let i of(r&&(r=r.toLowerCase()),e)){var n,a;if(t===(null==(n=i.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===i.defaultLocale.toLowerCase()||(null==(a=i.locales)?void 0:a.some(e=>e.toLowerCase()===r)))return i}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},96330:e=>{"use strict";e.exports=require("@prisma/client")},97683:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return a}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},97748:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{atLeastOneTask:function(){return a},scheduleImmediate:function(){return n},scheduleOnNextTick:function(){return r},waitAtLeastOneReactRenderTask:function(){return i}});let r=e=>{Promise.resolve().then(()=>{process.nextTick(e)})},n=e=>{setImmediate(e)};function a(){return new Promise(e=>n(e))}function i(){return new Promise(e=>setImmediate(e))}},98485:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupeFetch",{enumerable:!0,get:function(){return s}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var s=a?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(7153)),a=r(72150),i=r(23302);function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}function s(e){let t=n.cache(e=>[]);return function(r,n){let o,s;if(n&&n.signal)return e(r,n);if("string"!=typeof r||n){let t="string"==typeof r||r instanceof URL?new Request(r,n):r;if("GET"!==t.method&&"HEAD"!==t.method||t.keepalive)return e(r,n);s=JSON.stringify([t.method,Array.from(t.headers.entries()),t.mode,t.redirect,t.credentials,t.referrer,t.referrerPolicy,t.integrity]),o=t.url}else s='["GET",[],null,"follow",null,null,null,null]',o=r;let c=t(o);for(let e=0,t=c.length;e<t;e+=1){let[t,r]=c[e];if(t===s)return r.then(()=>{let t=c[e][2];if(!t)throw Object.defineProperty(new i.InvariantError("No cached response"),"__NEXT_ERROR_CODE",{value:"E579",enumerable:!1,configurable:!0});let[r,n]=(0,a.cloneResponse)(t);return c[e][2]=n,r})}let u=e(r,n),l=[s,u,null];return c.push(l),u.then(e=>{let[t,r]=(0,a.cloneResponse)(e);return l[2]=r,t})}}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,2190,5663,9404,7945,1658,1795],()=>r(42753));module.exports=n})();