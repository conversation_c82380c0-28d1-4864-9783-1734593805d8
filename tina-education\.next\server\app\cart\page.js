(()=>{var e={};e.id=4005,e.ids=[4005],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8569:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o={children:["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,35914)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\cart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\cart\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35914:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\cart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\cart\\page.tsx","default")},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},85192:(e,t,s)=>{Promise.resolve().then(s.bind(s,99724))},98344:(e,t,s)=>{Promise.resolve().then(s.bind(s,35914))},99724:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(60687),a=s(85814),i=s.n(a),n=s(30474),l=s(43210),c=s(99208),o=s(19089);let d=[{id:"1",publicationId:"pub1",title:"The Silent Patient",author:"Alex Michaelides",type:"EBOOK",price:2500,quantity:1,cover:"/images/book-placeholder.jpg"},{id:"2",publicationId:"pub2",title:"Gone Girl",author:"Gillian Flynn",type:"BOOK",price:3e3,quantity:2,cover:"/images/book-placeholder.jpg"},{id:"3",publicationId:"pub3",title:"The Girl with the Dragon Tattoo",author:"Stieg Larsson",type:"AUDIOBOOK",price:3500,quantity:1,cover:"/images/book-placeholder.jpg"}];function x(){let{data:e,status:t}=(0,c.wV)(),[s,a]=(0,l.useState)(d),x=(e,t)=>{t<1||a(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},h=e=>{a(t=>t.filter(t=>t.id!==e))},u=e=>{h(e)},p=e=>{switch(e){case"BOOK":return"Printed Copy";case"EBOOK":return"eBook";case"AUDIOBOOK":return"Audio book";default:return e}},m=s.reduce((e,t)=>e+t.price*t.quantity,0),b=500*!!s.some(e=>"BOOK"===e.type),g=m+b;return"loading"===t?(0,r.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})}):e?.user?.id?(0,r.jsxs)("div",{className:"min-h-screen bg-white mt-18",children:[(0,r.jsx)("div",{className:"w-full h-18 flex items-center justify-center",style:{backgroundColor:"#f0eded"},children:(0,r.jsxs)("nav",{className:"flex space-x-8",children:[(0,r.jsx)(i(),{href:"/books",className:"text-black text-xl hover:underline",children:"Books"}),(0,r.jsx)(i(),{href:"/authors",className:"text-black text-xl hover:underline",children:"Authors"}),(0,r.jsx)(i(),{href:"/journals",className:"text-black text-xl hover:underline",children:"Journals"})]})}),(0,r.jsx)("div",{className:"px-10 py-4 border-b border-blue-500",children:(0,r.jsxs)("nav",{className:"text-s text-black",children:[(0,r.jsx)(i(),{href:"/",className:"hover:underline",children:"Home"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)("span",{children:"Shopping Cart"})]})}),(0,r.jsxs)("div",{className:"px-10 py-10",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-black mb-8",children:"Shopping Cart"}),0===s.length?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDED2"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-black mb-4",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-gray-700 mb-8",children:"Add some books to get started!"}),(0,r.jsx)(i(),{href:"/books",className:"px-8 py-3 text-white rounded-lg hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:"Continue Shopping"})]}):(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsx)("div",{className:"space-y-6",children:s.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex gap-6",children:[(0,r.jsx)("div",{className:"w-24 h-36 flex-shrink-0",children:e.cover?(0,r.jsx)(n.default,{src:e.cover,alt:e.title,width:96,height:144,className:"w-full h-full object-cover border border-gray-300"}):(0,r.jsx)("div",{className:"w-full h-full bg-gray-200 border border-gray-300 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500 text-xs",children:"No Cover"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-black mb-1",children:(0,r.jsx)(i(),{href:`/repository/${e.publicationId}`,className:"hover:underline",children:e.title})}),(0,r.jsxs)("p",{className:"text-gray-700 mb-2",children:["By ",e.author]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:p(e.type)})]}),(0,r.jsx)("button",{onClick:()=>h(e.id),className:"text-gray-400 hover:text-red-500 transition-colors",children:(0,r.jsx)(o.yRo,{size:20})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("span",{className:"text-gray-700",children:"Quantity:"}),(0,r.jsxs)("div",{className:"flex items-center bg-gray-300 border border-blue-300",children:[(0,r.jsx)("button",{onClick:()=>x(e.id,e.quantity-1),className:"w-8 h-6 flex items-center justify-center border-r border-blue-300 text-gray-800 hover:bg-gray-400",children:"-"}),(0,r.jsx)("div",{className:"w-10 h-6 text-sm flex items-center justify-center border-r border-blue-300 text-gray-800",children:e.quantity}),(0,r.jsx)("button",{onClick:()=>x(e.id,e.quantity+1),className:"w-8 h-6 text-sm flex items-center justify-center text-gray-800 hover:bg-gray-400",children:"+"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("button",{onClick:()=>u(e.id),className:"flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors",children:[(0,r.jsx)(o.uoh,{size:16}),(0,r.jsx)("span",{className:"text-sm",children:"Move to Wishlist"})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-lg font-bold text-black",children:[(e.price*e.quantity).toLocaleString(),"frs"]}),e.quantity>1&&(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.price.toLocaleString(),"frs each"]})]})]})]})]})]})},e.id))}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)(i(),{href:"/books",className:"text-blue-600 hover:underline text-lg",children:"← Continue Shopping"})})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6 sticky top-24",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-black mb-6",children:"Order Summary"}),(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between text-gray-700",children:[(0,r.jsxs)("span",{children:["Subtotal (",s.length," items)"]}),(0,r.jsxs)("span",{children:[m.toLocaleString(),"frs"]})]}),b>0&&(0,r.jsxs)("div",{className:"flex justify-between text-gray-700",children:[(0,r.jsx)("span",{children:"Shipping"}),(0,r.jsxs)("span",{children:[b.toLocaleString(),"frs"]})]}),(0,r.jsx)("hr",{className:"border-gray-300"}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-bold text-black",children:[(0,r.jsx)("span",{children:"Total"}),(0,r.jsxs)("span",{children:[g.toLocaleString(),"frs"]})]})]}),(0,r.jsx)("button",{className:"w-full py-3 text-white text-lg font-medium rounded-lg hover:opacity-90 transition-opacity mb-4",style:{backgroundColor:"#0c0a46"},children:"Proceed to Checkout"}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Secure checkout with"}),(0,r.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,r.jsx)("span",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:"SSL"}),(0,r.jsx)("span",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:"256-bit"})]})]})]})})]})]})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-white mt-18",children:[(0,r.jsx)("div",{className:"w-full h-18 flex items-center justify-center",style:{backgroundColor:"#f0eded"},children:(0,r.jsxs)("nav",{className:"flex space-x-8",children:[(0,r.jsx)(i(),{href:"/books",className:"text-black text-xl hover:underline",children:"Books"}),(0,r.jsx)(i(),{href:"/authors",className:"text-black text-xl hover:underline",children:"Authors"}),(0,r.jsx)(i(),{href:"/journals",className:"text-black text-xl hover:underline",children:"Journals"})]})}),(0,r.jsx)("div",{className:"px-10 py-4 border-b border-blue-500",children:(0,r.jsxs)("nav",{className:"text-s text-black",children:[(0,r.jsx)(i(),{href:"/",className:"hover:underline",children:"Home"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)("span",{children:"Shopping Cart"})]})}),(0,r.jsx)("div",{className:"px-10 py-10",children:(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD12"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-black mb-4",children:"Sign in to view your cart"}),(0,r.jsx)("p",{className:"text-gray-700 mb-8",children:"You need to be signed in to access your shopping cart."}),(0,r.jsx)(i(),{href:"/auth/signin?callbackUrl=/cart",className:"px-8 py-3 text-white rounded-lg hover:opacity-90 transition-opacity",style:{backgroundColor:"#0c0a46"},children:"Sign In"})]})})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,7945,1658,4755,1795],()=>s(8569));module.exports=r})();