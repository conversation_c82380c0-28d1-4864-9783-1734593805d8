(()=>{var e={};e.id=4680,e.ids=[4680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8704:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return s},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return i},isHTTPAccessFallbackError:function(){return a}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},n=new Set(Object.values(r)),s="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===s&&n.has(Number(r))}function i(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15045:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(60687),s=r(99208),a=r(16189),i=r(43210),o=r(85814),l=r.n(o);function u(){let e=(0,a.useSearchParams)().get("callbackUrl")||"/dashboard",[t,r]=(0,i.useState)(null),[o,u]=(0,i.useState)(!1),[c,d]=(0,i.useState)({email:"",password:""}),f=async t=>{t.preventDefault(),u(!0);try{await (0,s.Jv)("credentials",{email:c.email,password:c.password,callbackUrl:e})}catch(e){console.error("Sign in error:",e)}finally{u(!1)}},p=async()=>{u(!0);try{await (0,s.Jv)("google",{callbackUrl:e})}catch(e){console.error("Google sign in error:",e)}finally{u(!1)}};return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,n.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),"/dashboard"!==e&&(0,n.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"You need to sign in to continue with your action"})]}),(0,n.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,n.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,n.jsxs)("form",{className:"space-y-6",onSubmit:f,children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,n.jsx)("div",{className:"mt-1",children:(0,n.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:c.email,onChange:e=>d({...c,email:e.target.value}),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your email address"})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,n.jsx)("div",{className:"mt-1",children:(0,n.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:c.password,onChange:e=>d({...c,password:e.target.value}),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your password"})})]}),(0,n.jsx)("div",{children:(0,n.jsx)("button",{type:"submit",disabled:o,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Signing in...":"Sign in"})})]}),(0,n.jsx)("div",{className:"mt-6",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,n.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,n.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,n.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]})}),t?.google&&(0,n.jsx)("div",{className:"mt-6",children:(0,n.jsxs)("button",{onClick:p,disabled:o,className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,n.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,n.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,n.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,n.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,n.jsx)("span",{className:"ml-2",children:"Sign in with Google"})]})}),(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,n.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,n.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,n.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Don't have an account?"})})]}),(0,n.jsx)("div",{className:"mt-6 text-center",children:(0,n.jsx)(l(),{href:"/auth/signup",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Create a new account"})})]}),(0,n.jsx)("div",{className:"mt-6 text-center",children:(0,n.jsx)(l(),{href:"/",className:"text-sm text-blue-600 hover:text-blue-500",children:"← Back to home"})})]})})]})}function c(){return(0,n.jsx)(i.Suspense,{fallback:(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:"Loading..."}),children:(0,n.jsx)(u,{})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21916:(e,t,r)=>{Promise.resolve().then(r.bind(r,54259))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31162:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return a}});let n=r(8704),s=r(49026);function a(e){return(0,s.isRedirectError)(e)||(0,n.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33873:e=>{"use strict";e.exports=require("path")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49026:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return s},RedirectType:function(){return a},isRedirectError:function(){return i}});let n=r(52836),s="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,i=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===s&&("replace"===a||"push"===a)&&"string"==typeof i&&!isNaN(o)&&o in n.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51846:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return s}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},52637:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},52836:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54259:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\auth\\signin\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69821:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>u});var n=r(65239),s=r(48088),a=r(88170),i=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,54259)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\auth\\signin\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return l}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(n,i,o):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}(r(61120));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}let a={current:null},i="function"==typeof n.cache?n.cache:e=>e,o=console.warn;function l(e){return function(...t){o(e(...t))}}i(e=>{try{o(a.current)}finally{a.current=null}})},79551:e=>{"use strict";e.exports=require("url")},80060:(e,t,r)=>{Promise.resolve().then(r.bind(r,15045))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,7945,1658,1795],()=>r(69821));module.exports=n})();