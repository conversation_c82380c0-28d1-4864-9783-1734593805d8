(()=>{var e={};e.id=7449,e.ids=[7449],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},13440:e=>{"use strict";e.exports=require("util/types")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},41204:e=>{"use strict";e.exports=require("string_decoder")},43470:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o});var s=r(88249),i=r(42335);async function o(e,t,r){try{console.log("\uD83D\uDCC4 Starting PDF generation...");let o=await s.PDFDocument.create(),u=o.addPage(),{width:c,height:p}=u.getSize(),d={regular:await o.embedFont(s.StandardFonts.Helvetica),bold:await o.embedFont(s.StandardFonts.HelveticaBold),italic:await o.embedFont(s.StandardFonts.HelveticaOblique),boldItalic:await o.embedFont(s.StandardFonts.HelveticaBoldOblique)},g={page:u,yPosition:p-50,fonts:d,margin:50,maxWidth:c-100,lineHeight:16,pdfDoc:o,pageWidth:c,pageHeight:p};for(let i of(t&&(g.yPosition=a(g,t,{font:d.bold,size:20,color:(0,s.rgb)(0,0,0)}),g.yPosition-=20),r&&(g.yPosition=a(g,`Author: ${r}`,{font:d.regular,size:12,color:(0,s.rgb)(.5,.5,.5)}),g.yPosition-=20),function(e){let t=[],r=e.replace(/&nbsp;/g," ").replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/<(strong|b)([^>]*)>(.*?)<\/(strong|b)>/gi,"**BOLD_START**$3**BOLD_END**").replace(/<(em|i)([^>]*)>(.*?)<\/(em|i)>/gi,"**ITALIC_START**$3**ITALIC_END**").split(/(<\/?(?:h[1-6]|p|ul|ol|li|br)[^>]*>)/gi),s=null,i=0;for(let e=0;e<r.length;e++){let o=r[e].trim();if(o)if(o.startsWith("<")){let e=o.match(/<\/?([a-z0-9]+)/i);if(!e)continue;let r=e[1].toLowerCase(),a=o.startsWith("</");r.match(/^h[1-6]$/)?a?s&&(s.content=n(s.content),t.push(s),s=null):s={type:"heading",content:"",level:parseInt(r.charAt(1))}:"p"===r?a?s&&(s.content=n(s.content),t.push(s),s=null):s={type:"paragraph",content:""}:"br"===r?s?s.content+="\n":t.push({type:"text",content:"\n"}):"ul"===r||"ol"===r?a?i=Math.max(0,i-1):i++:"li"===r&&(a?s&&(s.content=n(s.content),t.push(s),s=null):s={type:"list-item",content:"",level:i})}else if(s)s.content+=o;else{let e=n(o);t.push({type:"paragraph",content:e})}}return s&&(s.content=n(s.content),t.push(s)),t.filter(e=>e.content.trim().length>0)}(e)))g.yPosition=function(e,t){switch(t.type){case"heading":var r,i,o=e,n=t;let u=n.level||1,c=[18,16,14,13,12,11][Math.min(u-1,5)];return o.yPosition<o.pageHeight-100&&(o.yPosition-=o.lineHeight),o.yPosition=a(o,n.content,{font:o.fonts.bold,size:c,color:(0,s.rgb)(0,0,0)}),o.yPosition-=.5*o.lineHeight,o.yPosition;case"paragraph":default:return r=e,i=t,r.yPosition-=.3*r.lineHeight,r.yPosition=a(r,i.content,{font:r.fonts.regular,size:12,color:(0,s.rgb)(0,0,0)}),r.yPosition-=.7*r.lineHeight,r.yPosition;case"list-item":return function(e,t){let r=20*(t.level||1),i="• ";e.page.drawText(i,{x:e.margin+r,y:e.yPosition,size:12,font:e.fonts.regular,color:(0,s.rgb)(0,0,0)});let o=e.fonts.regular.widthOfTextAtSize(i,12),n=e.margin+r+o,a=e.maxWidth-r-o;return e.yPosition=l(e,t.content,{font:e.fonts.regular,size:12,color:(0,s.rgb)(0,0,0),x:n,maxWidth:a}),e.yPosition-=.3*e.lineHeight,e.yPosition}(e,t)}}(g,i);u=g.page;let m=await o.save();console.log("✅ PDF generated successfully");let h=`manuscript-${Date.now()}-${Math.random().toString(36).substring(7)}.pdf`;console.log(`📤 Uploading PDF to Vercel Blob: ${h}`);let f=await (0,i.yJ)(`manuscripts/${h}`,Buffer.from(m),{access:"public",contentType:"application/pdf"});return console.log(`✅ PDF uploaded successfully: ${f.url}`),f.url}catch(e){return console.error("❌ PDF generation/upload failed:",e),null}}function n(e){return e.replace(/\*\*BOLD_START\*\*/g,"").replace(/\*\*BOLD_END\*\*/g,"").replace(/\*\*ITALIC_START\*\*/g,"").replace(/\*\*ITALIC_END\*\*/g,"")}function a(e,t,r){let s=r.x||e.margin,i=r.maxWidth||e.maxWidth;return l(e,t,{...r,x:s,maxWidth:i})}function l(e,t,r){let s=t.trim().split(/\s+/),i="",o=e.yPosition;for(let t of s){let s=i+t+" ",n=r.font.widthOfTextAtSize(s,r.size);if(r.font.widthOfTextAtSize(t,r.size)>r.maxWidth){i.trim()&&(e.page.drawText(i.trim(),{x:r.x,y:o,size:r.size,font:r.font,color:r.color}),o-=e.lineHeight,i="",o<e.margin&&(e.page=e.pdfDoc.addPage(),o=e.pageHeight-e.margin));let s=t;for(;s.length>0;){let t="";for(let e=0;e<s.length;e++){let i=t+s[e];if(r.font.widthOfTextAtSize(i,r.size)>r.maxWidth)break;t=i}0===t.length&&(t=s[0]),e.page.drawText(t,{x:r.x,y:o,size:r.size,font:r.font,color:r.color}),o-=e.lineHeight,s=s.substring(t.length),o<e.margin&&s.length>0&&(e.page=e.pdfDoc.addPage(),o=e.pageHeight-e.margin)}}else n>r.maxWidth&&""!==i?(e.page.drawText(i.trim(),{x:r.x,y:o,size:r.size,font:r.font,color:r.color}),o-=e.lineHeight,i=t+" ",o<e.margin&&(e.page=e.pdfDoc.addPage(),o=e.pageHeight-e.margin)):i=s}return i.trim()&&(e.page.drawText(i.trim(),{x:r.x,y:o,size:r.size,font:r.font,color:r.color}),(o-=e.lineHeight)<e.margin&&(e.page=e.pdfDoc.addPage(),o=e.pageHeight-e.margin)),o}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54287:e=>{"use strict";e.exports=require("console")},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>u,j2:()=>d});var s=r(19443),i=r(16467),o=r(5956),n=r(10189),a=r(56056),l=r(85663);let{handlers:u,signIn:c,signOut:p,auth:d}=(0,s.Ay)({adapter:(0,i.y)(o.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[a.A,(0,n.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await o.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!t||!t.password||!await l.Ay.compare(e.password,t.password))return null;return{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,s=t.pathname.startsWith("/dashboard"),i=t.pathname.startsWith("/manuscripts"),o=t.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",t.pathname),!s&&!i&&!o||r},session:async({session:e,token:t})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",t),t?.sub&&(e.user.id=t.sub,t.name&&(e.user.name=t.name),t.email&&(e.user.email=t.email),t.picture&&(e.user.image=t.picture),t.role&&(e.user.role=t.role)),e),jwt:async({token:e,user:t,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",t),console.log("\uD83D\uDD10 JWT callback - account:",r),t&&(e.sub=t.id,e.name=t.name,e.email=t.email,e.picture=t.image,e.role=t.role),e)}})},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>d,PATCH:()=>p});var i=r(96559),o=r(48088),n=r(37719),a=r(56814),l=r(5956),u=r(32190),c=r(43470);async function p(e,{params:t}){let r=await (0,a.j2)();if(!r?.user?.id)return u.NextResponse.json({error:"Unauthorized"},{status:401});try{let{id:s}=await t,{title:i,abstract:o,content:n,keywords:a,uploadedFile:p,uploadedFileName:d}=await e.json(),g=await l.z.manuscript.findUnique({where:{id:s},select:{author_id:!0,title:!0}});if(!g)return u.NextResponse.json({error:"Manuscript not found"},{status:404});if(g.author_id!==r.user.id)return u.NextResponse.json({error:"You can only edit your own manuscripts"},{status:403});console.log("\uD83D\uDCC4 Starting manuscript update process..."),console.log(`📝 Title: ${i}`),console.log(`👤 Author: ${r.user?.name}`);let m=null;try{(m=await (0,c.Q)(n,i,r.user?.name||"Unknown Author"))||console.log("⚠️ PDF generation failed, continuing without PDF update")}catch(e){console.error("❌ PDF generation error:",e)}let h={title:i,abstract:o,content:n,keywords:a,uploadedFile:p||null,uploadedFileName:d||null,updatedAt:new Date};m&&(h.pdfFile=m);let f=await l.z.manuscript.update({where:{id:s},data:h});return console.log("✅ Manuscript updated successfully:",f.id),u.NextResponse.json({success:!0,message:"Manuscript updated successfully",manuscript:f})}catch(e){return console.error("❌ Manuscript update failed:",e),u.NextResponse.json({error:"Failed to update manuscript",details:e instanceof Error?e.message:String(e)},{status:500})}}async function d(e,{params:t}){let r=await (0,a.j2)();if(!r?.user?.id)return u.NextResponse.json({error:"Unauthorized"},{status:401});try{let{id:e}=await t,s=await l.z.manuscript.findUnique({where:{id:e},include:{user:{select:{name:!0,email:!0}},reviews:{include:{user:{select:{name:!0,email:!0}}}}}});if(!s)return u.NextResponse.json({error:"Manuscript not found"},{status:404});let i=s.author_id===r.user.id,o=s.reviews.some(e=>e.reviewer_id===r.user.id),n="ADMIN"===r.user.role;if(!i&&!o&&!n)return u.NextResponse.json({error:"You don't have permission to view this manuscript"},{status:403});return u.NextResponse.json({success:!0,manuscript:s})}catch(e){return console.error("❌ Failed to fetch manuscript:",e),u.NextResponse.json({error:"Failed to fetch manuscript"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/manuscripts/[id]/route",pathname:"/api/manuscripts/[id]",filename:"route",bundlePath:"app/api/manuscripts/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\manuscripts\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:f}=g;function x(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190,5663,9404,3285,8249],()=>r(69658));module.exports=s})();