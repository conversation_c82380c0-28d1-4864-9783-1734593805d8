"use strict";(()=>{var e={};e.id=7532,e.ids=[7532],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")},96330:e=>{e.exports=require("@prisma/client")},96559:(e,t,r)=>{e.exports=r(44870)},99697:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{PATCH:()=>c});var i=r(96559),n=r(48088),a=r(37719),o=r(56814),u=r(5956),p=r(32190);let d=new(r(20549)).u(process.env.RESEND_API_KEY);async function c(e,{params:t}){let r=await (0,o.j2)();if(!r?.user?.id)return p.NextResponse.json({error:"Unauthorized"},{status:401});try{let s=await e.json(),{id:i}=await t,n=await u.z.review.findUnique({where:{id:i},include:{manuscript:{include:{user:{select:{id:!0,name:!0,email:!0}}}},user:{select:{name:!0,email:!0}}}});if(!n)return p.NextResponse.json({error:"Review not found"},{status:404});if(n.reviewer_id!==r.user.id)return p.NextResponse.json({error:"Forbidden"},{status:403});if(!["ACCEPTED","IN_REVIEW"].includes(n.status))return p.NextResponse.json({error:"Review cannot be submitted in its current state"},{status:400});if(!s.recommendation||!s.contentEvaluation||!s.publicComments)return p.NextResponse.json({error:"Missing required fields: recommendation, content evaluation, and public comments are required"},{status:400});let a=await u.z.review.update({where:{id:i},data:{contentEvaluation:s.contentEvaluation,styleEvaluation:s.styleEvaluation,strengths:s.strengths,weaknesses:s.weaknesses,recommendation:s.recommendation,confidentialComments:s.confidentialComments,publicComments:s.publicComments,overallRating:s.overallRating,progressPercentage:100,timeSpent:s.timeSpent,status:"REVIEW_SUBMITTED",updatedAt:new Date}});if(await u.z.notification.create({data:{userId:n.manuscript.user.id,title:"Review Completed",message:`Your manuscript "${n.manuscript.title}" has been reviewed. The review is now complete.`,type:"REVIEW_COMPLETED",relatedId:i}}),process.env.RESEND_API_KEY)try{await d.emails.send({from:"<EMAIL>",to:n.manuscript.user.email,subject:`Review Completed: ${n.manuscript.title}`,html:`
            <h2>Review Completed</h2>
            <p>Dear ${n.manuscript.user.name},</p>
            <p>The review for your manuscript "<strong>${n.manuscript.title}</strong>" has been completed.</p>
            <p><strong>Reviewer Recommendation:</strong> ${s.recommendation.replace("_"," ")}</p>
            <p>Please log in to your dashboard to view the detailed review feedback.</p>
            <p>Best regards,<br>Tina Education Review Team</p>
          `})}catch(e){console.error("Failed to send email notification:",e)}return p.NextResponse.json({success:!0,message:"Review submitted successfully",review:a})}catch(e){return console.error("Failed to submit review:",e),p.NextResponse.json({error:"Failed to submit review"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reviews/[id]/submit/route",pathname:"/api/reviews/[id]/submit",filename:"route",bundlePath:"app/api/reviews/[id]/submit/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\submit\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:v,serverHooks:w}=l;function x(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:v})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190,5663,9404,9468],()=>r(99697));module.exports=s})();