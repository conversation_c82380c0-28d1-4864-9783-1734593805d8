"use strict";(()=>{var e={};e.id=3495,e.ids=[3495],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25949:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>c,tree:()=>p});var o=t(65239),n=t(48088),a=t(88170),i=t.n(a),s=t(30893),d={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);t.d(r,d);let p={children:["",{children:["books",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54480)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\books\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\books\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/books/page",pathname:"/books",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},54480:(e,r,t)=>{t.r(r),t.d(r,{default:()=>a});var o=t(37413),n=t(89454);function a(){return(0,o.jsx)(n.default,{apiEndpoint:"/api/repository/books",title:"Books Repository",description:"Explore our collection of books, e-books, and audiobooks from various authors and disciplines.",emptyStateMessage:"No books found. Try adjusting your search criteria or check back later for new publications.",emptyStateIcon:"\uD83D\uDCDA"})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4447,7945,1658,1795,7980],()=>t(25949));module.exports=o})();