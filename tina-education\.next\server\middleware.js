(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[751],{16:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,h:()=>a});let n="DYNAMIC_SERVER_USAGE";class i extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}},35:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,a=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator,g=Object.prototype.hasOwnProperty,b=Object.assign;function m(e,t,r,n,i,o){return{$$typeof:a,type:e,key:t,ref:void 0!==(r=o.ref)?r:null,props:o}}function y(e){return"object"==typeof e&&null!==e&&e.$$typeof===a}var w=/\/+/g;function v(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function x(){}function _(e,t,r){if(null==e)return e;var s=[],c=0;return!function e(t,r,s,c,l){var u,d,p,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var b=!1;if(null===t)b=!0;else switch(g){case"bigint":case"string":case"number":b=!0;break;case"object":switch(t.$$typeof){case a:case o:b=!0;break;case f:return e((b=t._init)(t._payload),r,s,c,l)}}if(b)return l=l(t),b=""===c?"."+v(t,0):c,i(l)?(s="",null!=b&&(s=b.replace(w,"$&/")+"/"),e(l,r,s,"",function(e){return e})):null!=l&&(y(l)&&(u=l,d=s+(null==l.key||t&&t.key===l.key?"":(""+l.key).replace(w,"$&/")+"/")+b,l=m(u.type,d,void 0,void 0,void 0,u.props)),r.push(l)),1;b=0;var _=""===c?".":c+":";if(i(t))for(var E=0;E<t.length;E++)g=_+v(c=t[E],E),b+=e(c,r,s,g,l);else if("function"==typeof(E=null===(p=t)||"object"!=typeof p?null:"function"==typeof(p=h&&p[h]||p["@@iterator"])?p:null))for(t=E.call(t),E=0;!(c=t.next()).done;)g=_+v(c=c.value,E++),b+=e(c,r,s,g,l);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(x,x):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,s,c,l);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return b}(e,s,"","",function(e){return t.call(r,e,c++)}),s}function E(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function S(){return new WeakMap}function k(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:_,forEach:function(e,t,r){_(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return _(e,function(){t++}),t},toArray:function(e){return _(e,function(e){return e})||[]},only:function(e){if(!y(e))throw Error(n(143));return e}},t.Fragment=s,t.Profiler=l,t.StrictMode=c,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(S);void 0===(t=n.get(e))&&(t=k(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var a=arguments[n];if("function"==typeof a||"object"==typeof a&&null!==a){var o=t.o;null===o&&(t.o=o=new WeakMap),void 0===(t=o.get(a))&&(t=k(),o.set(a,t))}else null===(o=t.p)&&(t.p=o=new Map),void 0===(t=o.get(a))&&(t=k(),o.set(a,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var s=e.apply(null,arguments);return(n=t).s=1,n.v=s}catch(e){throw(s=t).s=2,s.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=b({},e.props),a=e.key,o=void 0;if(null!=t)for(s in void 0!==t.ref&&(o=void 0),void 0!==t.key&&(a=""+t.key),t)g.call(t,s)&&"key"!==s&&"__self"!==s&&"__source"!==s&&("ref"!==s||void 0!==t.ref)&&(i[s]=t[s]);var s=arguments.length-2;if(1===s)i.children=r;else if(1<s){for(var c=Array(s),l=0;l<s;l++)c[l]=arguments[l+2];i.children=c}return m(e.type,a,void 0,void 0,o,i)},t.createElement=function(e,t,r){var n,i={},a=null;if(null!=t)for(n in void 0!==t.key&&(a=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var s=Array(o),c=0;c<o;c++)s[c]=arguments[c+2];i.children=s}if(e&&e.defaultProps)for(n in o=e.defaultProps)void 0===i[n]&&(i[n]=o[n]);return m(e,a,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=y,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:E}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},58:(e,t,r)=>{"use strict";r.d(t,{xl:()=>o});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let a="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function o(){return a?new a:new i}},115:(e,t,r)=>{"use strict";r.d(t,{XN:()=>i,FP:()=>n});let n=(0,r(58).xl)();function i(e){let t=n.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},158:(e,t,r)=>{e.exports={...r(266)}},159:(e,t,r)=>{"use strict";r.d(t,{RM:()=>a,s8:()=>i});let n=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401})),i="NEXT_HTTP_ERROR_FALLBACK";function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}},167:(e,t,r)=>{"use strict";r.d(t,{nJ:()=>o,oJ:()=>i,zB:()=>a});var n=r(821);let i="NEXT_REDIRECT";var a=function(e){return e.push="push",e.replace="replace",e}({});function o(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,a]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===i&&("replace"===a||"push"===a)&&"string"==typeof o&&!isNaN(s)&&s in n.Q}},199:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var n=r(159),i=r(167);function a(e){return(0,i.nJ)(e)||(0,n.RM)(e)}},201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return o},withRequest:function(){return a}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function a(e,t,r){let a=i(e,t);return a?n.run(a,r):r()}function o(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},266:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let{Decimal:n,objectEnumValues:i,makeStrictEnum:a,Public:o,getRuntime:s,skip:c}=r(629),l={};t.Prisma=l,t.$Enums={},l.prismaVersion={client:"6.8.2",engine:"2060c79ba17c6bb9f5823312b6f6b7f4a845738e"},l.PrismaClientKnownRequestError=()=>{let e=s().prettyName;throw Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.PrismaClientUnknownRequestError=()=>{let e=s().prettyName;throw Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.PrismaClientRustPanicError=()=>{let e=s().prettyName;throw Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.PrismaClientInitializationError=()=>{let e=s().prettyName;throw Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.PrismaClientValidationError=()=>{let e=s().prettyName;throw Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.Decimal=n,l.sql=()=>{let e=s().prettyName;throw Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.empty=()=>{let e=s().prettyName;throw Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.join=()=>{let e=s().prettyName;throw Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.raw=()=>{let e=s().prettyName;throw Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.validator=o.validator,l.getExtensionContext=()=>{let e=s().prettyName;throw Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.defineExtension=()=>{let e=s().prettyName;throw Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${e}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`)},l.DbNull=i.instances.DbNull,l.JsonNull=i.instances.JsonNull,l.AnyNull=i.instances.AnyNull,l.NullTypes={DbNull:i.classes.DbNull,JsonNull:i.classes.JsonNull,AnyNull:i.classes.AnyNull},t.Prisma.TransactionIsolationLevel=a({ReadUncommitted:"ReadUncommitted",ReadCommitted:"ReadCommitted",RepeatableRead:"RepeatableRead",Serializable:"Serializable"}),t.Prisma.UserScalarFieldEnum={id:"id",createdAt:"createdAt",email:"email",updatedAt:"updatedAt",image:"image",emailVerified:"emailVerified",role:"role",name:"name",password:"password",bio:"bio",affiliation:"affiliation",expertise:"expertise",phone:"phone",website:"website",orcid:"orcid"},t.Prisma.ManuscriptScalarFieldEnum={id:"id",createdAt:"createdAt",updatedAt:"updatedAt",title:"title",abstract:"abstract",content:"content",status:"status",type:"type",keywords:"keywords",author_id:"author_id",pdfFile:"pdfFile",uploadedFile:"uploadedFile",uploadedFileName:"uploadedFileName"},t.Prisma.ReviewScalarFieldEnum={id:"id",createdAt:"createdAt",updatedAt:"updatedAt",content:"content",manuscript_id:"manuscript_id",reviewer_id:"reviewer_id",feedback:"feedback",score:"score",status:"status",contentEvaluation:"contentEvaluation",styleEvaluation:"styleEvaluation",strengths:"strengths",weaknesses:"weaknesses",recommendation:"recommendation",confidentialComments:"confidentialComments",publicComments:"publicComments",overallRating:"overallRating",progressPercentage:"progressPercentage",timeSpent:"timeSpent",revisionRound:"revisionRound",previousReviewId:"previousReviewId"},t.Prisma.ReviewMessageScalarFieldEnum={id:"id",createdAt:"createdAt",updatedAt:"updatedAt",content:"content",sender:"sender",reviewId:"reviewId",userId:"userId"},t.Prisma.PublicationScalarFieldEnum={id:"id",createdAt:"createdAt",updatedAt:"updatedAt",title:"title",abstract:"abstract",content:"content",keywords:"keywords",cover:"cover",fileUrl:"fileUrl",fileName:"fileName",author_id:"author_id",type:"type",genreId:"genreId"},t.Prisma.WishlistItemScalarFieldEnum={id:"id",createdAt:"createdAt",updatedAt:"updatedAt",userId:"userId",publicationId:"publicationId",selectedType:"selectedType"},t.Prisma.GenreScalarFieldEnum={id:"id",name:"name",slug:"slug",description:"description",parentId:"parentId",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.AccountScalarFieldEnum={userId:"userId",type:"type",provider:"provider",providerAccountId:"providerAccountId",refresh_token:"refresh_token",access_token:"access_token",expires_at:"expires_at",token_type:"token_type",scope:"scope",id_token:"id_token",session_state:"session_state",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.SessionScalarFieldEnum={sessionToken:"sessionToken",userId:"userId",expires:"expires",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.VerificationTokenScalarFieldEnum={identifier:"identifier",token:"token",expires:"expires"},t.Prisma.AuthenticatorScalarFieldEnum={credentialID:"credentialID",userId:"userId",providerAccountId:"providerAccountId",credentialPublicKey:"credentialPublicKey",counter:"counter",credentialDeviceType:"credentialDeviceType",credentialBackedUp:"credentialBackedUp",transports:"transports"},t.Prisma.NotificationScalarFieldEnum={id:"id",userId:"userId",title:"title",message:"message",isRead:"isRead",type:"type",relatedId:"relatedId",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.ReviewerApplicationScalarFieldEnum={id:"id",userId:"userId",createdAt:"createdAt",updatedAt:"updatedAt",status:"status",motivation:"motivation",expertise:"expertise",experience:"experience",qualifications:"qualifications",availability:"availability",adminNotes:"adminNotes",reviewedBy:"reviewedBy",reviewedAt:"reviewedAt"},t.Prisma.SortOrder={asc:"asc",desc:"desc"},t.Prisma.QueryMode={default:"default",insensitive:"insensitive"},t.Prisma.NullsOrder={first:"first",last:"last"},t.Role=t.$Enums.Role={USER:"USER",ADMIN:"ADMIN",REVIEWER:"REVIEWER"},t.ManuscriptStatus=t.$Enums.ManuscriptStatus={DRAFT:"DRAFT",SUBMITTED:"SUBMITTED",UNDER_REVIEW:"UNDER_REVIEW",ACCEPTED:"ACCEPTED",REJECTED:"REJECTED"},t.Pub_type=t.$Enums.Pub_type={JOURNAL:"JOURNAL",ARTICLE:"ARTICLE",BOOK:"BOOK",EBOOK:"EBOOK",AUDIOBOOK:"AUDIOBOOK"},t.ReviewStatus=t.$Enums.ReviewStatus={PENDING:"PENDING",ACCEPTED:"ACCEPTED",DECLINED:"DECLINED",IN_REVIEW:"IN_REVIEW",REVIEW_SUBMITTED:"REVIEW_SUBMITTED"},t.ReviewRecommendation=t.$Enums.ReviewRecommendation={ACCEPT:"ACCEPT",MINOR_REVISIONS:"MINOR_REVISIONS",MAJOR_REVISIONS:"MAJOR_REVISIONS",REJECT:"REJECT"},t.MessageSender=t.$Enums.MessageSender={REVIEWER:"REVIEWER",EDITOR:"EDITOR",AUTHOR:"AUTHOR"},t.ReviewerApplicationStatus=t.$Enums.ReviewerApplicationStatus={PENDING:"PENDING",APPROVED:"APPROVED",REJECTED:"REJECTED",UNDER_REVIEW:"UNDER_REVIEW"},t.Prisma.ModelName={User:"User",Manuscript:"Manuscript",Review:"Review",ReviewMessage:"ReviewMessage",Publication:"Publication",WishlistItem:"WishlistItem",Genre:"Genre",Account:"Account",Session:"Session",VerificationToken:"VerificationToken",Authenticator:"Authenticator",Notification:"Notification",ReviewerApplication:"ReviewerApplication"};class u{constructor(){return new Proxy(this,{get(e,t){let r,n=s();throw Error((n.isEdge?`PrismaClient is not configured to run in ${n.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`:"PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `"+n.prettyName+"`).")+`
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`)}})}}t.PrismaClient=u,Object.assign(t,l)},280:(e,t,r)=>{var n;(()=>{var i={226:function(i,a){!function(o,s){"use strict";var c="function",l="undefined",u="object",d="string",p="major",f="model",h="name",g="type",b="vendor",m="version",y="architecture",w="console",v="mobile",x="tablet",_="smarttv",E="wearable",S="embedded",k="Amazon",A="Apple",R="ASUS",T="BlackBerry",P="Browser",C="Chrome",O="Firefox",N="Google",I="Huawei",U="Microsoft",j="Motorola",D="Opera",M="Samsung",$="Sharp",L="Sony",H="Xiaomi",W="Zebra",K="Facebook",q="Chromium OS",B="Mac OS",J=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},V=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},z=function(e,t){return typeof e===d&&-1!==F(t).indexOf(F(e))},F=function(e){return e.toLowerCase()},G=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===l?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,a,o,l,d=0;d<t.length&&!o;){var p=t[d],f=t[d+1];for(r=n=0;r<p.length&&!o&&p[r];)if(o=p[r++].exec(e))for(i=0;i<f.length;i++)l=o[++n],typeof(a=f[i])===u&&a.length>0?2===a.length?typeof a[1]==c?this[a[0]]=a[1].call(this,l):this[a[0]]=a[1]:3===a.length?typeof a[1]!==c||a[1].exec&&a[1].test?this[a[0]]=l?l.replace(a[1],a[2]):void 0:this[a[0]]=l?a[1].call(this,l,a[2]):void 0:4===a.length&&(this[a[0]]=l?a[3].call(this,l.replace(a[1],a[2])):s):this[a]=l||s;d+=2}},Z=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(z(t[r][n],e))return"?"===r?s:r}else if(z(t[r],e))return"?"===r?s:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,m],[/opios[\/ ]+([\w\.]+)/i],[m,[h,D+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[h,D]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[h,"UC"+P]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[m,[h,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[h,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+P],m],[/\bfocus\/([\w\.]+)/i],[m,[h,O+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[h,D+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[h,D+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[h,"MIUI "+P]],[/fxios\/([-\w\.]+)/i],[m,[h,O]],[/\bqihu|(qi?ho?o?|360)browser/i],[[h,"360 "+P]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1 "+P],m],[/(comodo_dragon)\/([\w\.]+)/i],[[h,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[h,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,K],m],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[h,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[h,C+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,C+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[h,"Android "+P]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[m,Z,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[h,O+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[h,m],[/(cobalt)\/([\w\.]+)/i],[h,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[y,"amd64"]],[/(ia32(?=;))/i],[[y,F]],[/((?:i[346]|x)86)[;\)]/i],[[y,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[y,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[y,"armhf"]],[/windows (ce|mobile); ppc;/i],[[y,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[y,/ower/,"",F]],[/(sun4\w)[;\)]/i],[[y,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[y,F]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[b,M],[g,x]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[b,M],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[b,A],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[b,A],[g,x]],[/(macintosh);/i],[f,[b,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[b,$],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[b,I],[g,x]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[b,I],[g,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[b,H],[g,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[b,H],[g,x]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[b,"OPPO"],[g,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[b,"Vivo"],[g,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[b,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[b,j],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[b,j],[g,x]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[b,"LG"],[g,x]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[b,"LG"],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[b,"Lenovo"],[g,x]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[b,"Nokia"],[g,v]],[/(pixel c)\b/i],[f,[b,N],[g,x]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[b,N],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[b,L],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[b,L],[g,x]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[b,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[b,k],[g,x]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[b,k],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[f,b,[g,x]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[b,T],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[b,R],[g,x]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[b,R],[g,v]],[/(nexus 9)/i],[f,[b,"HTC"],[g,x]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[b,[f,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[b,"Acer"],[g,x]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[b,"Meizu"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[b,f,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[b,f,[g,x]],[/(surface duo)/i],[f,[b,U],[g,x]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[b,"Fairphone"],[g,v]],[/(u304aa)/i],[f,[b,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[f,[b,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[f,[b,"RCA"],[g,x]],[/\b(venue[\d ]{2,7}) b/i],[f,[b,"Dell"],[g,x]],[/\b(q(?:mv|ta)\w+) b/i],[f,[b,"Verizon"],[g,x]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[b,"Barnes & Noble"],[g,x]],[/\b(tm\d{3}\w+) b/i],[f,[b,"NuVision"],[g,x]],[/\b(k88) b/i],[f,[b,"ZTE"],[g,x]],[/\b(nx\d{3}j) b/i],[f,[b,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[f,[b,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[f,[b,"Swiss"],[g,x]],[/\b((zeki)?tb.*\b) b/i],[f,[b,"Zeki"],[g,x]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[b,"Dragon Touch"],f,[g,x]],[/\b(ns-?\w{0,9}) b/i],[f,[b,"Insignia"],[g,x]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[b,"NextBook"],[g,x]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[b,"Voice"],f,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[b,"LvTel"],f,[g,v]],[/\b(ph-1) /i],[f,[b,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[b,"Envizen"],[g,x]],[/\b(trio[-\w\. ]+) b/i],[f,[b,"MachSpeed"],[g,x]],[/\btu_(1491) b/i],[f,[b,"Rotor"],[g,x]],[/(shield[\w ]+) b/i],[f,[b,"Nvidia"],[g,x]],[/(sprint) (\w+)/i],[b,f,[g,v]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[b,U],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[b,W],[g,x]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[b,W],[g,v]],[/smart-tv.+(samsung)/i],[b,[g,_]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[b,M],[g,_]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[b,"LG"],[g,_]],[/(apple) ?tv/i],[b,[f,A+" TV"],[g,_]],[/crkey/i],[[f,C+"cast"],[b,N],[g,_]],[/droid.+aft(\w)( bui|\))/i],[f,[b,k],[g,_]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[b,$],[g,_]],[/(bravia[\w ]+)( bui|\))/i],[f,[b,L],[g,_]],[/(mitv-\w{5}) bui/i],[f,[b,H],[g,_]],[/Hbbtv.*(technisat) (.*);/i],[b,f,[g,_]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[b,G],[f,G],[g,_]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,_]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[b,f,[g,w]],[/droid.+; (shield) bui/i],[f,[b,"Nvidia"],[g,w]],[/(playstation [345portablevi]+)/i],[f,[b,L],[g,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[b,U],[g,w]],[/((pebble))app/i],[b,f,[g,E]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[b,A],[g,E]],[/droid.+; (glass) \d/i],[f,[b,N],[g,E]],[/droid.+; (wt63?0{2,3})\)/i],[f,[b,W],[g,E]],[/(quest( 2| pro)?)/i],[f,[b,K],[g,E]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[b,[g,S]],[/(aeobc)\b/i],[f,[b,k],[g,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[g,x]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,x]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[b,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[h,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[h,[m,Z,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[h,"Windows"],[m,Z,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,B],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,m],[/\(bb(10);/i],[m,[h,T]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[h,O+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[h,C+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,q],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,m],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,m]]},ee=function(e,t){if(typeof e===u&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==l&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:s,a=t?J(Q,t):Q,w=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[h]=s,t[m]=s,X.call(t,n,a.browser),t[p]=typeof(e=t[m])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,w&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[y]=s,X.call(e,n,a.cpu),e},this.getDevice=function(){var e={};return e[b]=s,e[f]=s,e[g]=s,X.call(e,n,a.device),w&&!e[g]&&i&&i.mobile&&(e[g]=v),w&&"Macintosh"==e[f]&&r&&typeof r.standalone!==l&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[g]=x),e},this.getEngine=function(){var e={};return e[h]=s,e[m]=s,X.call(e,n,a.engine),e},this.getOS=function(){var e={};return e[h]=s,e[m]=s,X.call(e,n,a.os),w&&!e[h]&&i&&"Unknown"!=i.platform&&(e[h]=i.platform.replace(/chrome os/i,q).replace(/macos/i,B)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?G(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=V([h,m,p]),ee.CPU=V([y]),ee.DEVICE=V([f,b,g,w,v,_,x,E,S]),ee.ENGINE=ee.OS=V([h,m]),typeof a!==l?(i.exports&&(a=i.exports=ee),a.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof o!==l&&(o.UAParser=ee);var et=typeof o!==l&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},a={};function o(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete a[e]}return r.exports}o.ab="//",e.exports=o(226)})()},356:e=>{"use strict";e.exports=require("node:buffer")},388:(e,t,r)=>{e.exports={...r(158)}},521:e=>{"use strict";e.exports=require("node:async_hooks")},535:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=(0,r(58).xl)()},552:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return s},interceptFetch:function(){return c},reader:function(){return a}});let i=r(201),a={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function o(e,t){let{url:r,method:i,headers:a,body:o,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:f}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(a),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:o?n.from(await t.arrayBuffer()).toString("base64"):null,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:f}}}async function s(e,t){let r=(0,i.getTestReqInfo)(t,a);if(!r)return e(t);let{testData:s,proxyPort:c}=r,l=await o(s,t),u=await e(`http://localhost:${c}`,{method:"POST",body:JSON.stringify(l),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:p}=d;switch(p){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:f,headers:h,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:f,headers:new Headers(h)})}function c(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):s(e,new Request(t,r))},()=>{r.g.fetch=e}}},557:(e,t,r)=>{"use strict";r.d(t,{t3:()=>c,I3:()=>d,Ui:()=>l,xI:()=>o,Pk:()=>s});var n=r(815),i=r(16);r(602),r(115),r(535),r(801);let a="function"==typeof n.unstable_postpone;function o(e,t,r){let n=Object.defineProperty(new i.F(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function s(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function c(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=f(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n)}throw f(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function l(e,t,r){(function(){if(!a)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.unstable_postpone(u(e,t))}function u(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&p(e.message)}function p(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===p(u("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function f(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},602:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});class n extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}},629:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a=e=>{throw TypeError(e)},o=(e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})},s=(e,t,r)=>t.has(e)?a("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),c={};o(c,{Decimal:()=>tt,Public:()=>l,getRuntime:()=>N,makeStrictEnum:()=>S,objectEnumValues:()=>x}),e.exports=((e,a,o,s)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let c of n(a))i.call(e,c)||c===o||t(e,c,{get:()=>a[c],enumerable:!(s=r(a,c))||s.enumerable});return e})(t({},"__esModule",{value:!0}),c);var l={};function u(...e){return e=>e}o(l,{validator:()=>u});var d,p=Symbol(),f=new WeakMap,h=class{constructor(e){e===p?f.set(this,"Prisma.".concat(this._getName())):f.set(this,"new Prisma.".concat(this._getNamespace(),".").concat(this._getName(),"()"))}_getName(){return this.constructor.name}toString(){return f.get(this)}},g=class extends h{_getNamespace(){return"NullTypes"}},b=class extends g{constructor(){super(...arguments),s(this,d)}};d=new WeakMap,_(b,"DbNull");var m,y=class extends g{constructor(){super(...arguments),s(this,m)}};m=new WeakMap,_(y,"JsonNull");var w,v=class extends g{constructor(){super(...arguments),s(this,w)}};w=new WeakMap,_(v,"AnyNull");var x={classes:{DbNull:b,JsonNull:y,AnyNull:v},instances:{DbNull:new b(p),JsonNull:new y(p),AnyNull:new v(p)}};function _(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var E=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function S(e){return new Proxy(e,{get(e,t){if(t in e)return e[t];if(!E.has(t))throw TypeError("Invalid enum value: ".concat(String(t)))}})}var k=()=>{var e,t;return(null==(t=null==(e=globalThis.process)?void 0:e.release)?void 0:t.name)==="node"},A=()=>{var e,t;return!!globalThis.Bun||!!(null!=(t=null==(e=globalThis.process)?void 0:e.versions)&&t.bun)},R=()=>!!globalThis.Deno,T=()=>"object"==typeof globalThis.Netlify,P=()=>"object"==typeof globalThis.EdgeRuntime,C=()=>{var e;return(null==(e=globalThis.navigator)?void 0:e.userAgent)==="Cloudflare-Workers"},O={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function N(){var e;let t=null!=(e=[[T,"netlify"],[P,"edge-light"],[C,"workerd"],[R,"deno"],[A,"bun"],[k,"node"]].flatMap(e=>e[0]()?[e[1]]:[]).at(0))?e:"";return{id:t,prettyName:O[t]||t,isEdge:["workerd","deno","netlify","edge-light"].includes(t)}}var I,U,j="0123456789abcdef",D="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",M="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",$={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},L=!0,H="[DecimalError] ",W=H+"Invalid argument: ",K=H+"Precision limit exceeded",q=H+"crypto unavailable",B="[object Decimal]",J=Math.floor,V=Math.pow,z=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,F=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,G=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,X=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Z=D.length-1,Y=M.length-1,Q={toStringTag:B};function ee(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=ed(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=ed(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}function et(e,t,r){if(e!==~~e||e<t||e>r)throw Error(W+e)}function er(e,t,r,n){var i,a,o,s;for(a=e[0];a>=10;a/=10)--t;return--t<0?(t+=7,i=0):(i=Math.ceil((t+1)/7),t%=7),a=V(10,7-t),s=e[i]%a|0,null==n?t<3?(0==t?s=s/100|0:1==t&&(s=s/10|0),o=r<4&&99999==s||r>3&&49999==s||5e4==s||0==s):o=(r<4&&s+1==a||r>3&&s+1==a/2)&&(e[i+1]/a/100|0)==V(10,t-2)-1||(s==a/2||0==s)&&(e[i+1]/a/100|0)==0:t<4?(0==t?s=s/1e3|0:1==t?s=s/100|0:2==t&&(s=s/10|0),o=(n||r<4)&&9999==s||!n&&r>3&&4999==s):o=((n||r<4)&&s+1==a||!n&&r>3&&s+1==a/2)&&(e[i+1]/a/1e3|0)==V(10,t-3)-1,o}function en(e,t,r){for(var n,i,a=[0],o=0,s=e.length;o<s;){for(i=a.length;i--;)a[i]*=t;for(a[0]+=j.indexOf(e.charAt(o++)),n=0;n<a.length;n++)a[n]>r-1&&(void 0===a[n+1]&&(a[n+1]=0),a[n+1]+=a[n]/r|0,a[n]%=r)}return a.reverse()}Q.absoluteValue=Q.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),ea(e)},Q.ceil=function(){return ea(new this.constructor(this),this.e+1,2)},Q.clampedTo=Q.clamp=function(e,t){var r=this.constructor;if(e=new r(e),t=new r(t),!e.s||!t.s)return new r(NaN);if(e.gt(t))throw Error(W+t);return 0>this.cmp(e)?e:this.cmp(t)>0?t:new r(this)},Q.comparedTo=Q.cmp=function(e){var t,r,n,i,a=this.d,o=(e=new this.constructor(e)).d,s=this.s,c=e.s;if(!a||!o)return s&&c?s!==c?s:a===o?0:!a^s<0?1:-1:NaN;if(!a[0]||!o[0])return a[0]?s:o[0]?-c:0;if(s!==c)return s;if(this.e!==e.e)return this.e>e.e^s<0?1:-1;for(n=a.length,i=o.length,t=0,r=n<i?n:i;t<r;++t)if(a[t]!==o[t])return a[t]>o[t]^s<0?1:-1;return n===i?0:n>i^s<0?1:-1},Q.cosine=Q.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+7,n.rounding=1,r=function(e,t){var r,n,i;if(t.isZero())return t;(n=t.d.length)<32?i=(1/ev(4,r=Math.ceil(n/3))).toString():(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=ew(e,1,t.times(i),new e(1));for(var a=r;a--;){var o=t.times(t);t=o.times(o).minus(o).times(8).plus(1)}return e.precision-=r,t}(n,ex(n,r)),n.precision=e,n.rounding=t,ea(2==U||3==U?r.neg():r,e,t,!0)):new n(1):new n(NaN)},Q.cubeRoot=Q.cbrt=function(){var e,t,r,n,i,a,o,s,c,l,u=this.constructor;if(!this.isFinite()||this.isZero())return new u(this);for(L=!1,(a=this.s*V(this.s*this,1/3))&&Math.abs(a)!=1/0?n=new u(a.toString()):(r=ee(this.d),(a=((e=this.e)-r.length+1)%3)&&(r+=1==a||-2==a?"0":"00"),a=V(r,1/3),e=J((e+1)/3)-(e%3==(e<0?-1:2)),(n=new u(r=a==1/0?"5e"+e:(r=a.toExponential()).slice(0,r.indexOf("e")+1)+e)).s=this.s),o=(e=u.precision)+3;;)if(n=ei((l=(c=(s=n).times(s).times(s)).plus(this)).plus(this).times(s),l.plus(c),o+2,1),ee(s.d).slice(0,o)===(r=ee(n.d)).slice(0,o))if("9999"!=(r=r.slice(o-3,o+1))&&(i||"4999"!=r)){+r&&(+r.slice(1)||"5"!=r.charAt(0))||(ea(n,e+1,1),t=!n.times(n).times(n).eq(this));break}else{if(!i&&(ea(s,e+1,0),s.times(s).times(s).eq(this))){n=s;break}o+=4,i=1}return L=!0,ea(n,e,u.rounding,t)},Q.decimalPlaces=Q.dp=function(){var e,t=this.d,r=NaN;if(t){if(r=((e=t.length-1)-J(this.e/7))*7,e=t[e])for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r},Q.dividedBy=Q.div=function(e){return ei(this,new this.constructor(e))},Q.dividedToIntegerBy=Q.divToInt=function(e){var t=this.constructor;return ea(ei(this,new t(e),0,1,1),t.precision,t.rounding)},Q.equals=Q.eq=function(e){return 0===this.cmp(e)},Q.floor=function(){return ea(new this.constructor(this),this.e+1,3)},Q.greaterThan=Q.gt=function(e){return this.cmp(e)>0},Q.greaterThanOrEqualTo=Q.gte=function(e){var t=this.cmp(e);return 1==t||0===t},Q.hyperbolicCosine=Q.cosh=function(){var e,t,r,n,i,a=this,o=a.constructor,s=new o(1);if(!a.isFinite())return new o(a.s?1/0:NaN);if(a.isZero())return s;r=o.precision,n=o.rounding,o.precision=r+Math.max(a.e,a.sd())+4,o.rounding=1,(i=a.d.length)<32?t=(1/ev(4,e=Math.ceil(i/3))).toString():(e=16,t="2.3283064365386962890625e-10"),a=ew(o,1,a.times(t),new o(1),!0);for(var c,l=e,u=new o(8);l--;)c=a.times(a),a=s.minus(c.times(u.minus(c.times(u))));return ea(a,o.precision=r,o.rounding=n,!0)},Q.hyperbolicSine=Q.sinh=function(){var e,t,r,n,i=this,a=i.constructor;if(!i.isFinite()||i.isZero())return new a(i);if(t=a.precision,r=a.rounding,a.precision=t+Math.max(i.e,i.sd())+4,a.rounding=1,(n=i.d.length)<3)i=ew(a,2,i,i,!0);else{e=(e=1.4*Math.sqrt(n))>16?16:0|e,i=ew(a,2,i=i.times(1/ev(5,e)),i,!0);for(var o,s=new a(5),c=new a(16),l=new a(20);e--;)o=i.times(i),i=i.times(s.plus(o.times(c.times(o).plus(l))))}return a.precision=t,a.rounding=r,ea(i,t,r,!0)},Q.hyperbolicTangent=Q.tanh=function(){var e,t,r=this.constructor;return this.isFinite()?this.isZero()?new r(this):(e=r.precision,t=r.rounding,r.precision=e+7,r.rounding=1,ei(this.sinh(),this.cosh(),r.precision=e,r.rounding=t)):new r(this.s)},Q.inverseCosine=Q.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),n=t.precision,i=t.rounding;return -1!==r?0===r?e.isNeg()?el(t,n,i):new t(0):new t(NaN):e.isZero()?el(t,n+4,i).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=i,e.times(2))},Q.inverseHyperbolicCosine=Q.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,L=!1,r=r.times(r).minus(1).sqrt().plus(r),L=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)},Q.inverseHyperbolicSine=Q.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,L=!1,r=r.times(r).plus(1).sqrt().plus(r),L=!0,n.precision=e,n.rounding=t,r.ln())},Q.inverseHyperbolicTangent=Q.atanh=function(){var e,t,r,n,i=this,a=i.constructor;return i.isFinite()?i.e>=0?new a(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=a.precision,t=a.rounding,Math.max(n=i.sd(),e)<-(2*i.e)-1?ea(new a(i),e,t,!0):(a.precision=r=n-i.e,i=ei(i.plus(1),new a(1).minus(i),r+e,1),a.precision=e+4,a.rounding=1,i=i.ln(),a.precision=e,a.rounding=t,i.times(.5))):new a(NaN)},Q.inverseSine=Q.asin=function(){var e,t,r,n,i=this,a=i.constructor;return i.isZero()?new a(i):(t=i.abs().cmp(1),r=a.precision,n=a.rounding,-1!==t?0===t?((e=el(a,r+4,n).times(.5)).s=i.s,e):new a(NaN):(a.precision=r+6,a.rounding=1,i=i.div(new a(1).minus(i.times(i)).sqrt().plus(1)).atan(),a.precision=r,a.rounding=n,i.times(2)))},Q.inverseTangent=Q.atan=function(){var e,t,r,n,i,a,o,s,c,l=this,u=l.constructor,d=u.precision,p=u.rounding;if(l.isFinite()){if(l.isZero())return new u(l);if(l.abs().eq(1)&&d+4<=Y)return(o=el(u,d+4,p).times(.25)).s=l.s,o}else{if(!l.s)return new u(NaN);if(d+4<=Y)return(o=el(u,d+4,p).times(.5)).s=l.s,o}for(u.precision=s=d+10,u.rounding=1,e=r=Math.min(28,s/7+2|0);e;--e)l=l.div(l.times(l).plus(1).sqrt().plus(1));for(L=!1,t=Math.ceil(s/7),n=1,c=l.times(l),o=new u(l),i=l;-1!==e;)if(i=i.times(c),a=o.minus(i.div(n+=2)),i=i.times(c),void 0!==(o=a.plus(i.div(n+=2))).d[t])for(e=t;o.d[e]===a.d[e]&&e--;);return r&&(o=o.times(2<<r-1)),L=!0,ea(o,u.precision=d,u.rounding=p,!0)},Q.isFinite=function(){return!!this.d},Q.isInteger=Q.isInt=function(){return!!this.d&&J(this.e/7)>this.d.length-2},Q.isNaN=function(){return!this.s},Q.isNegative=Q.isNeg=function(){return this.s<0},Q.isPositive=Q.isPos=function(){return this.s>0},Q.isZero=function(){return!!this.d&&0===this.d[0]},Q.lessThan=Q.lt=function(e){return 0>this.cmp(e)},Q.lessThanOrEqualTo=Q.lte=function(e){return 1>this.cmp(e)},Q.logarithm=Q.log=function(e){var t,r,n,i,a,o,s,c,l=this.constructor,u=l.precision,d=l.rounding;if(null==e)e=new l(10),t=!0;else{if(r=(e=new l(e)).d,e.s<0||!r||!r[0]||e.eq(1))return new l(NaN);t=e.eq(10)}if(r=this.d,this.s<0||!r||!r[0]||this.eq(1))return new l(r&&!r[0]?-1/0:1!=this.s?NaN:r?0:1/0);if(t)if(r.length>1)a=!0;else{for(i=r[0];i%10==0;)i/=10;a=1!==i}if(L=!1,er((c=ei(o=eb(this,s=u+5),t?ec(l,s+10):eb(e,s),s,1)).d,i=u,d))do if(s+=10,c=ei(o=eb(this,s),t?ec(l,s+10):eb(e,s),s,1),!a){+ee(c.d).slice(i+1,i+15)+1==1e14&&(c=ea(c,u+1,0));break}while(er(c.d,i+=10,d));return L=!0,ea(c,u,d)},Q.minus=Q.sub=function(e){var t,r,n,i,a,o,s,c,l,u,d,p,f=this.constructor;if(e=new f(e),!this.d||!e.d)return this.s&&e.s?this.d?e.s=-e.s:e=new f(e.d||this.s!==e.s?this:NaN):e=new f(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.plus(e);if(l=this.d,p=e.d,s=f.precision,c=f.rounding,!l[0]||!p[0]){if(p[0])e.s=-e.s;else{if(!l[0])return new f(3===c?-0:0);e=new f(this)}return L?ea(e,s,c):e}if(r=J(e.e/7),u=J(this.e/7),l=l.slice(),a=u-r){for((d=a<0)?(t=l,a=-a,o=p.length):(t=p,r=u,o=l.length),a>(n=Math.max(Math.ceil(s/7),o)+2)&&(a=n,t.length=1),t.reverse(),n=a;n--;)t.push(0);t.reverse()}else{for((d=(n=l.length)<(o=p.length))&&(o=n),n=0;n<o;n++)if(l[n]!=p[n]){d=l[n]<p[n];break}a=0}for(d&&(t=l,l=p,p=t,e.s=-e.s),o=l.length,n=p.length-o;n>0;--n)l[o++]=0;for(n=p.length;n>a;){if(l[--n]<p[n]){for(i=n;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[n]+=1e7}l[n]-=p[n]}for(;0===l[--o];)l.pop();for(;0===l[0];l.shift())--r;return l[0]?(e.d=l,e.e=es(l,r),L?ea(e,s,c):e):new f(3===c?-0:0)},Q.modulo=Q.mod=function(e){var t,r=this.constructor;return e=new r(e),this.d&&e.s&&(!e.d||e.d[0])?e.d&&(!this.d||this.d[0])?(L=!1,9==r.modulo?(t=ei(this,e.abs(),0,3,1),t.s*=e.s):t=ei(this,e,0,r.modulo,1),t=t.times(e),L=!0,this.minus(t)):ea(new r(this),r.precision,r.rounding):new r(NaN)},Q.naturalExponential=Q.exp=function(){return eg(this)},Q.naturalLogarithm=Q.ln=function(){return eb(this)},Q.negated=Q.neg=function(){var e=new this.constructor(this);return e.s=-e.s,ea(e)},Q.plus=Q.add=function(e){var t,r,n,i,a,o,s,c,l,u,d=this.constructor;if(e=new d(e),!this.d||!e.d)return this.s&&e.s?this.d||(e=new d(e.d||this.s===e.s?this:NaN)):e=new d(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.minus(e);if(l=this.d,u=e.d,s=d.precision,c=d.rounding,!l[0]||!u[0])return u[0]||(e=new d(this)),L?ea(e,s,c):e;if(a=J(this.e/7),n=J(e.e/7),l=l.slice(),i=a-n){for(i<0?(r=l,i=-i,o=u.length):(r=u,n=a,o=l.length),i>(o=(a=Math.ceil(s/7))>o?a+1:o+1)&&(i=o,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for((o=l.length)-(i=u.length)<0&&(i=o,r=u,u=l,l=r),t=0;i;)t=(l[--i]=l[i]+u[i]+t)/1e7|0,l[i]%=1e7;for(t&&(l.unshift(t),++n),o=l.length;0==l[--o];)l.pop();return e.d=l,e.e=es(l,n),L?ea(e,s,c):e},Q.precision=Q.sd=function(e){var t;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(W+e);return this.d?(t=eu(this.d),e&&this.e+1>t&&(t=this.e+1)):t=NaN,t},Q.round=function(){var e=this.constructor;return ea(new e(this),this.e+1,e.rounding)},Q.sine=Q.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+7,n.rounding=1,r=function(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:ew(e,2,t,t);r=(r=1.4*Math.sqrt(n))>16?16:0|r,t=ew(e,2,t=t.times(1/ev(5,r)),t);for(var i,a=new e(5),o=new e(16),s=new e(20);r--;)i=t.times(t),t=t.times(a.plus(i.times(o.times(i).minus(s))));return t}(n,ex(n,r)),n.precision=e,n.rounding=t,ea(U>2?r.neg():r,e,t,!0)):new n(NaN)},Q.squareRoot=Q.sqrt=function(){var e,t,r,n,i,a,o=this.d,s=this.e,c=this.s,l=this.constructor;if(1!==c||!o||!o[0])return new l(!c||c<0&&(!o||o[0])?NaN:o?this:1/0);for(L=!1,0==(c=Math.sqrt(+this))||c==1/0?(((t=ee(o)).length+s)%2==0&&(t+="0"),c=Math.sqrt(t),s=J((s+1)/2)-(s<0||s%2),n=new l(t=c==1/0?"5e"+s:(t=c.toExponential()).slice(0,t.indexOf("e")+1)+s)):n=new l(c.toString()),r=(s=l.precision)+3;;)if(n=(a=n).plus(ei(this,a,r+2,1)).times(.5),ee(a.d).slice(0,r)===(t=ee(n.d)).slice(0,r))if("9999"!=(t=t.slice(r-3,r+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(ea(n,s+1,1),e=!n.times(n).eq(this));break}else{if(!i&&(ea(a,s+1,0),a.times(a).eq(this))){n=a;break}r+=4,i=1}return L=!0,ea(n,s,l.rounding,e)},Q.tangent=Q.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,(r=r.sin()).s=1,r=ei(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,ea(2==U||4==U?r.neg():r,e,t,!0)):new n(NaN)},Q.times=Q.mul=function(e){var t,r,n,i,a,o,s,c,l,u=this.constructor,d=this.d,p=(e=new u(e)).d;if(e.s*=this.s,!d||!d[0]||!p||!p[0])return new u(!e.s||d&&!d[0]&&!p||p&&!p[0]&&!d?NaN:!d||!p?e.s/0:0*e.s);for(r=J(this.e/7)+J(e.e/7),(c=d.length)<(l=p.length)&&(a=d,d=p,p=a,o=c,c=l,l=o),a=[],n=o=c+l;n--;)a.push(0);for(n=l;--n>=0;){for(t=0,i=c+n;i>n;)s=a[i]+p[n]*d[i-n-1]+t,a[i--]=s%1e7|0,t=s/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=es(a,r),L?ea(e,u.precision,u.rounding):e},Q.toBinary=function(e,t){return e_(this,2,e,t)},Q.toDecimalPlaces=Q.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),void 0===e?r:(et(e,0,1e9),void 0===t?t=n.rounding:et(t,0,8),ea(r,e+r.e+1,t))},Q.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=eo(n,!0):(et(e,0,1e9),void 0===t?t=i.rounding:et(t,0,8),r=eo(n=ea(new i(n),e+1,t),!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r},Q.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?r=eo(this):(et(e,0,1e9),void 0===t?t=i.rounding:et(t,0,8),r=eo(n=ea(new i(this),e+this.e+1,t),!1,e+n.e+1)),this.isNeg()&&!this.isZero()?"-"+r:r},Q.toFraction=function(e){var t,r,n,i,a,o,s,c,l,u,d,p,f=this.d,h=this.constructor;if(!f)return new h(this);if(l=r=new h(1),n=c=new h(0),o=(a=(t=new h(n)).e=eu(f)-this.e-1)%7,t.d[0]=V(10,o<0?7+o:o),null==e)e=a>0?t:l;else{if(!(s=new h(e)).isInt()||s.lt(l))throw Error(W+s);e=s.gt(t)?a>0?t:l:s}for(L=!1,s=new h(ee(f)),u=h.precision,h.precision=a=7*f.length*2;d=ei(s,t,0,1,1),1!=(i=r.plus(d.times(n))).cmp(e);)r=n,n=i,i=l,l=c.plus(d.times(i)),c=i,i=t,t=s.minus(d.times(i)),s=i;return i=ei(e.minus(r),n,0,1,1),c=c.plus(i.times(l)),r=r.plus(i.times(n)),c.s=l.s=this.s,p=1>ei(l,n,a,1).minus(this).abs().cmp(ei(c,r,a,1).minus(this).abs())?[l,n]:[c,r],h.precision=u,L=!0,p},Q.toHexadecimal=Q.toHex=function(e,t){return e_(this,16,e,t)},Q.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),null==e){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),void 0===t?t=n.rounding:et(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(L=!1,r=ei(r,e,0,t,1).times(e),L=!0,ea(r)):(e.s=r.s,r=e),r},Q.toNumber=function(){return+this},Q.toOctal=function(e,t){return e_(this,8,e,t)},Q.toPower=Q.pow=function(e){var t,r,n,i,a,o,s=this,c=s.constructor,l=+(e=new c(e));if(!s.d||!e.d||!s.d[0]||!e.d[0])return new c(V(+s,l));if((s=new c(s)).eq(1))return s;if(n=c.precision,a=c.rounding,e.eq(1))return ea(s,n,a);if((t=J(e.e/7))>=e.d.length-1&&(r=l<0?-l:l)<=0x1fffffffffffff)return i=ep(c,s,r,n),e.s<0?new c(1).div(i):ea(i,n,a);if((o=s.s)<0){if(t<e.d.length-1)return new c(NaN);if((1&e.d[t])==0&&(o=1),0==s.e&&1==s.d[0]&&1==s.d.length)return s.s=o,s}return(t=0!=(r=V(+s,l))&&isFinite(r)?new c(r+"").e:J(l*(Math.log("0."+ee(s.d))/Math.LN10+s.e+1)))>c.maxE+1||t<c.minE-1?new c(t>0?o/0:0):(L=!1,c.rounding=s.s=1,r=Math.min(12,(t+"").length),(i=eg(e.times(eb(s,n+r)),n)).d&&er((i=ea(i,n+5,1)).d,n,a)&&(t=n+10,+ee((i=ea(eg(e.times(eb(s,t+r)),t),t+5,1)).d).slice(n+1,n+15)+1==1e14&&(i=ea(i,n+1,0))),i.s=o,L=!0,c.rounding=a,ea(i,n,a))},Q.toPrecision=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=eo(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(et(e,1,1e9),void 0===t?t=i.rounding:et(t,0,8),r=eo(n=ea(new i(n),e,t),e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r},Q.toSignificantDigits=Q.toSD=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(et(e,1,1e9),void 0===t?t=r.rounding:et(t,0,8)),ea(new r(this),e,t)},Q.toString=function(){var e=this.constructor,t=eo(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()&&!this.isZero()?"-"+t:t},Q.truncated=Q.trunc=function(){return ea(new this.constructor(this),this.e+1,1)},Q.valueOf=Q.toJSON=function(){var e=this.constructor,t=eo(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()?"-"+t:t};var ei=function(){function e(e,t,r){var n,i=0,a=e.length;for(e=e.slice();a--;)n=e[a]*t+i,e[a]=n%r|0,i=n/r|0;return i&&e.unshift(i),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r,n){for(var i=0;r--;)e[r]-=i,i=+(e[r]<t[r]),e[r]=i*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o,s,c){var l,u,d,p,f,h,g,b,m,y,w,v,x,_,E,S,k,A,R,T,P=n.constructor,C=n.s==i.s?1:-1,O=n.d,N=i.d;if(!O||!O[0]||!N||!N[0])return new P(!n.s||!i.s||(O?N&&O[0]==N[0]:!N)?NaN:O&&0==O[0]||!N?0*C:C/0);for(c?(f=1,u=n.e-i.e):(c=1e7,f=7,u=J(n.e/f)-J(i.e/f)),R=N.length,k=O.length,y=(m=new P(C)).d=[],d=0;N[d]==(O[d]||0);d++);if(N[d]>(O[d]||0)&&u--,null==a?(_=a=P.precision,o=P.rounding):_=s?a+(n.e-i.e)+1:a,_<0)y.push(1),h=!0;else{if(_=_/f+2|0,d=0,1==R){for(p=0,N=N[0],_++;(d<k||p)&&_--;d++)E=p*c+(O[d]||0),y[d]=E/N|0,p=E%N|0;h=p||d<k}else{for((p=c/(N[0]+1)|0)>1&&(N=e(N,p,c),O=e(O,p,c),R=N.length,k=O.length),S=R,v=(w=O.slice(0,R)).length;v<R;)w[v++]=0;(T=N.slice()).unshift(0),A=N[0],N[1]>=c/2&&++A;do p=0,(l=t(N,w,R,v))<0?(x=w[0],R!=v&&(x=x*c+(w[1]||0)),(p=x/A|0)>1?(p>=c&&(p=c-1),b=(g=e(N,p,c)).length,v=w.length,1==(l=t(g,w,b,v))&&(p--,r(g,R<b?T:N,b,c))):(0==p&&(l=p=1),g=N.slice()),(b=g.length)<v&&g.unshift(0),r(w,g,v,c),-1==l&&(v=w.length,(l=t(N,w,R,v))<1&&(p++,r(w,R<v?T:N,v,c))),v=w.length):0===l&&(p++,w=[0]),y[d++]=p,l&&w[0]?w[v++]=O[S]||0:(w=[O[S]],v=1);while((S++<k||void 0!==w[0])&&_--);h=void 0!==w[0]}y[0]||y.shift()}if(1==f)m.e=u,I=h;else{for(d=1,p=y[0];p>=10;p/=10)d++;m.e=d+u*f-1,ea(m,s?a+m.e+1:a,o,h)}return m}}();function ea(e,t,r,n){var i,a,o,s,c,l,u,d,p,f=e.constructor;e:if(null!=t){if(!(d=e.d))return e;for(i=1,s=d[0];s>=10;s/=10)i++;if((a=t-i)<0)a+=7,o=t,c=(u=d[p=0])/V(10,i-o-1)%10|0;else if((p=Math.ceil((a+1)/7))>=(s=d.length))if(n){for(;s++<=p;)d.push(0);u=c=0,i=1,a%=7,o=a-7+1}else break e;else{for(u=s=d[p],i=1;s>=10;s/=10)i++;a%=7,c=(o=a-7+i)<0?0:u/V(10,i-o-1)%10|0}if(n=n||t<0||void 0!==d[p+1]||(o<0?u:u%V(10,i-o-1)),l=r<4?(c||n)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||n||6==r&&(a>0?o>0?u/V(10,i-o):0:d[p-1])%10&1||r==(e.s<0?8:7)),t<1||!d[0])return d.length=0,l?(t-=e.e+1,d[0]=V(10,(7-t%7)%7),e.e=-t||0):d[0]=e.e=0,e;if(0==a?(d.length=p,s=1,p--):(d.length=p+1,s=V(10,7-a),d[p]=o>0?(u/V(10,i-o)%V(10,o)|0)*s:0),l)for(;;)if(0==p){for(a=1,o=d[0];o>=10;o/=10)a++;for(o=d[0]+=s,s=1;o>=10;o/=10)s++;a!=s&&(e.e++,1e7==d[0]&&(d[0]=1));break}else{if(d[p]+=s,1e7!=d[p])break;d[p--]=0,s=1}for(a=d.length;0===d[--a];)d.pop()}return L&&(e.e>f.maxE?(e.d=null,e.e=NaN):e.e<f.minE&&(e.e=0,e.d=[0])),e}function eo(e,t,r){if(!e.isFinite())return em(e);var n,i=e.e,a=ee(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+ed(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(e.e<0?"e":"e+")+e.e):i<0?(a="0."+ed(-i-1)+a,r&&(n=r-o)>0&&(a+=ed(n))):i>=o?(a+=ed(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+ed(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=ed(n))),a}function es(e,t){var r=e[0];for(t*=7;r>=10;r/=10)t++;return t}function ec(e,t,r){if(t>Z)throw L=!0,r&&(e.precision=r),Error(K);return ea(new e(D),t,1,!0)}function el(e,t,r){if(t>Y)throw Error(K);return ea(new e(M),t,r,!0)}function eu(e){var t=e.length-1,r=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function ed(e){for(var t="";e--;)t+="0";return t}function ep(e,t,r,n){var i,a=new e(1),o=Math.ceil(n/7+4);for(L=!1;;){if(r%2&&eE((a=a.times(t)).d,o)&&(i=!0),0===(r=J(r/2))){r=a.d.length-1,i&&0===a.d[r]&&++a.d[r];break}eE((t=t.times(t)).d,o)}return L=!0,a}function ef(e){return 1&e.d[e.d.length-1]}function eh(e,t,r){for(var n,i,a=new e(t[0]),o=0;++o<t.length;){if(!(i=new e(t[o])).s){a=i;break}((n=a.cmp(i))===r||0===n&&a.s===r)&&(a=i)}return a}function eg(e,t){var r,n,i,a,o,s,c,l=0,u=0,d=0,p=e.constructor,f=p.rounding,h=p.precision;if(!e.d||!e.d[0]||e.e>17)return new p(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(L=!1,c=h):c=t,s=new p(.03125);e.e>-2;)e=e.times(s),d+=5;for(c+=n=Math.log(V(2,d))/Math.LN10*2+5|0,r=a=o=new p(1),p.precision=c;;){if(a=ea(a.times(e),c,1),r=r.times(++u),ee((s=o.plus(ei(a,r,c,1))).d).slice(0,c)===ee(o.d).slice(0,c)){for(i=d;i--;)o=ea(o.times(o),c,1);if(null!=t)return p.precision=h,o;if(!(l<3&&er(o.d,c-n,f,l)))return ea(o,p.precision=h,f,L=!0);p.precision=c+=10,r=a=s=new p(1),u=0,l++}o=s}}function eb(e,t){var r,n,i,a,o,s,c,l,u,d,p,f=1,h=e,g=h.d,b=h.constructor,m=b.rounding,y=b.precision;if(h.s<0||!g||!g[0]||!h.e&&1==g[0]&&1==g.length)return new b(g&&!g[0]?-1/0:1!=h.s?NaN:g?0:h);if(null==t?(L=!1,u=y):u=t,b.precision=u+=10,n=(r=ee(g)).charAt(0),!(15e14>Math.abs(a=h.e)))return l=ec(b,u+2,y).times(a+""),h=eb(new b(n+"."+r.slice(1)),u-10).plus(l),b.precision=y,null==t?ea(h,y,m,L=!0):h;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=ee((h=h.times(e)).d)).charAt(0),f++;for(a=h.e,n>1?(h=new b("0."+r),a++):h=new b(n+"."+r.slice(1)),d=h,c=o=h=ei(h.minus(1),h.plus(1),u,1),p=ea(h.times(h),u,1),i=3;;){if(o=ea(o.times(p),u,1),ee((l=c.plus(ei(o,new b(i),u,1))).d).slice(0,u)===ee(c.d).slice(0,u))if(c=c.times(2),0!==a&&(c=c.plus(ec(b,u+2,y).times(a+""))),c=ei(c,new b(f),u,1),null!=t)return b.precision=y,c;else{if(!er(c.d,u-10,m,s))return ea(c,b.precision=y,m,L=!0);b.precision=u+=10,l=o=h=ei(d.minus(1),d.plus(1),u,1),p=ea(h.times(h),u,1),i=s=1}c=l,i+=2}}function em(e){return String(e.s*e.s/0)}function ey(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);n++);for(i=t.length;48===t.charCodeAt(i-1);--i);if(t=t.slice(n,i)){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";e.d.push(+t),L&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function ew(e,t,r,n,i){var a,o,s,c,l=e.precision,u=Math.ceil(l/7);for(L=!1,c=r.times(r),s=new e(n);;){if(o=ei(s.times(c),new e(t++*t++),l,1),s=i?n.plus(o):n.minus(o),n=ei(o.times(c),new e(t++*t++),l,1),void 0!==(o=s.plus(n)).d[u]){for(a=u;o.d[a]===s.d[a]&&a--;);if(-1==a)break}a=s,s=n,n=o,o=a}return L=!0,o.d.length=u+1,o}function ev(e,t){for(var r=e;--t;)r*=e;return r}function ex(e,t){var r,n=t.s<0,i=el(e,e.precision,1),a=i.times(.5);if((t=t.abs()).lte(a))return U=n?4:1,t;if((r=t.divToInt(i)).isZero())U=n?3:2;else{if((t=t.minus(r.times(i))).lte(a))return U=ef(r)?n?2:3:n?4:1,t;U=ef(r)?n?1:4:n?3:2}return t.minus(i).abs()}function e_(e,t,r,n){var i,a,o,s,c,l,u,d,p,f=e.constructor,h=void 0!==r;if(h?(et(r,1,1e9),void 0===n?n=f.rounding:et(n,0,8)):(r=f.precision,n=f.rounding),e.isFinite()){for(o=(u=eo(e)).indexOf("."),h?(i=2,16==t?r=4*r-3:8==t&&(r=3*r-2)):i=t,o>=0&&(u=u.replace(".",""),(p=new f(1)).e=u.length-o,p.d=en(eo(p),10,i),p.e=p.d.length),a=c=(d=en(u,10,i)).length;0==d[--c];)d.pop();if(d[0]){if(o<0?a--:((e=new f(e)).d=d,e.e=a,d=(e=ei(e,p,r,n,0,i)).d,a=e.e,l=I),o=d[r],s=i/2,l=l||void 0!==d[r+1],l=n<4?(void 0!==o||l)&&(0===n||n===(e.s<0?3:2)):o>s||o===s&&(4===n||l||6===n&&1&d[r-1]||n===(e.s<0?8:7)),d.length=r,l)for(;++d[--r]>i-1;)d[r]=0,r||(++a,d.unshift(1));for(c=d.length;!d[c-1];--c);for(o=0,u="";o<c;o++)u+=j.charAt(d[o]);if(h){if(c>1)if(16==t||8==t){for(o=16==t?4:3,--c;c%o;c++)u+="0";for(c=(d=en(u,i,t)).length;!d[c-1];--c);for(o=1,u="1.";o<c;o++)u+=j.charAt(d[o])}else u=u.charAt(0)+"."+u.slice(1);u=u+(a<0?"p":"p+")+a}else if(a<0){for(;++a;)u="0"+u;u="0."+u}else if(++a>c)for(a-=c;a--;)u+="0";else a<c&&(u=u.slice(0,a)+"."+u.slice(a))}else u=h?"0p+0":"0";u=(16==t?"0x":2==t?"0b":8==t?"0o":"")+u}else u=em(e);return e.s<0?"-"+u:u}function eE(e,t){if(e.length>t)return e.length=t,!0}function eS(e){return new this(e).abs()}function ek(e){return new this(e).acos()}function eA(e){return new this(e).acosh()}function eR(e,t){return new this(e).plus(t)}function eT(e){return new this(e).asin()}function eP(e){return new this(e).asinh()}function eC(e){return new this(e).atan()}function eO(e){return new this(e).atanh()}function eN(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,a=n+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(r=t.s<0?el(this,n,i):new this(0)).s=e.s:!e.d||t.isZero()?(r=el(this,a,1).times(.5)).s=e.s:t.s<0?(this.precision=a,this.rounding=1,r=this.atan(ei(e,t,a,1)),t=el(this,a,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(ei(e,t,a,1)):(r=el(this,a,1).times(t.s>0?.25:.75)).s=e.s:r=new this(NaN),r}function eI(e){return new this(e).cbrt()}function eU(e){return ea(e=new this(e),e.e+1,2)}function ej(e,t,r){return new this(e).clamp(t,r)}function eD(e){if(!e||"object"!=typeof e)throw Error(H+"Object expected");var t,r,n,i=!0===e.defaults,a=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(t=0;t<a.length;t+=3)if(r=a[t],i&&(this[r]=$[r]),void 0!==(n=e[r]))if(J(n)===n&&n>=a[t+1]&&n<=a[t+2])this[r]=n;else throw Error(W+r+": "+n);if(r="crypto",i&&(this[r]=$[r]),void 0!==(n=e[r]))if(!0===n||!1===n||0===n||1===n)if(n)if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(q);else this[r]=!1;else throw Error(W+r+": "+n);return this}function eM(e){return new this(e).cos()}function e$(e){return new this(e).cosh()}function eL(e,t){return new this(e).div(t)}function eH(e){return new this(e).exp()}function eW(e){return ea(e=new this(e),e.e+1,3)}function eK(){var e,t,r=new this(0);for(L=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return L=!0,new this(1/0);r=t}return L=!0,r.sqrt()}function eq(e){return e instanceof te||e&&e.toStringTag===B||!1}function eB(e){return new this(e).ln()}function eJ(e,t){return new this(e).log(t)}function eV(e){return new this(e).log(2)}function ez(e){return new this(e).log(10)}function eF(){return eh(this,arguments,-1)}function eG(){return eh(this,arguments,1)}function eX(e,t){return new this(e).mod(t)}function eZ(e,t){return new this(e).mul(t)}function eY(e,t){return new this(e).pow(t)}function eQ(e){var t,r,n,i,a=0,o=new this(1),s=[];if(void 0===e?e=this.precision:et(e,1,1e9),n=Math.ceil(e/7),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));a<n;)(i=t[a])>=429e7?t[a]=crypto.getRandomValues(new Uint32Array(1))[0]:s[a++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);a<n;)(i=t[a]+(t[a+1]<<8)+(t[a+2]<<16)+((127&t[a+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,a):(s.push(i%1e7),a+=4);a=n/4}else throw Error(q);else for(;a<n;)s[a++]=1e7*Math.random()|0;for(n=s[--a],e%=7,n&&e&&(i=V(10,7-e),s[a]=(n/i|0)*i);0===s[a];a--)s.pop();if(a<0)r=0,s=[0];else{for(r=-1;0===s[0];r-=7)s.shift();for(n=1,i=s[0];i>=10;i/=10)n++;n<7&&(r-=7-n)}return o.e=r,o.d=s,o}function e0(e){return ea(e=new this(e),e.e+1,this.rounding)}function e1(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function e2(e){return new this(e).sin()}function e5(e){return new this(e).sinh()}function e6(e){return new this(e).sqrt()}function e3(e,t){return new this(e).sub(t)}function e8(){var e=0,t=arguments,r=new this(t[0]);for(L=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return L=!0,ea(r,this.precision,this.rounding)}function e4(e){return new this(e).tan()}function e9(e){return new this(e).tanh()}function e7(e){return ea(e=new this(e),e.e+1,1)}Q[Symbol.for("nodejs.util.inspect.custom")]=Q.toString,Q[Symbol.toStringTag]="Decimal";var te=Q.constructor=function e(t){var r,n,i;function a(e){var t,r,n;if(!(this instanceof a))return new a(e);if(this.constructor=a,eq(e)){this.s=e.s,L?!e.d||e.e>a.maxE?(this.e=NaN,this.d=null):e.e<a.minE?(this.e=0,this.d=[0]):(this.e=e.e,this.d=e.d.slice()):(this.e=e.e,this.d=e.d?e.d.slice():e.d);return}if("number"==(n=typeof e)){if(0===e){this.s=1/e<0?-1:1,this.e=0,this.d=[0];return}if(e<0?(e=-e,this.s=-1):this.s=1,e===~~e&&e<1e7){for(t=0,r=e;r>=10;r/=10)t++;L?t>a.maxE?(this.e=NaN,this.d=null):t<a.minE?(this.e=0,this.d=[0]):(this.e=t,this.d=[e]):(this.e=t,this.d=[e]);return}if(0*e!=0){e||(this.s=NaN),this.e=NaN,this.d=null;return}return ey(this,e.toString())}if("string"===n)return 45===(r=e.charCodeAt(0))?(e=e.slice(1),this.s=-1):(43===r&&(e=e.slice(1)),this.s=1),X.test(e)?ey(this,e):function(e,t){var r,n,i,a,o,s,c,l,u;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),X.test(t))return ey(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(F.test(t))r=16,t=t.toLowerCase();else if(z.test(t))r=2;else if(G.test(t))r=8;else throw Error(W+t);for((a=t.search(/p/i))>0?(c=+t.slice(a+1),t=t.substring(2,a)):t=t.slice(2),o=(a=t.indexOf("."))>=0,n=e.constructor,o&&(a=(s=(t=t.replace(".","")).length)-a,i=ep(n,new n(r),a,2*a)),a=u=(l=en(t,r,1e7)).length-1;0===l[a];--a)l.pop();return a<0?new n(0*e.s):(e.e=es(l,u),e.d=l,L=!1,o&&(e=ei(e,i,4*s)),c&&(e=e.times(54>Math.abs(c)?V(2,c):te.pow(2,c))),L=!0,e)}(this,e);if("bigint"===n)return e<0?(e=-e,this.s=-1):this.s=1,ey(this,e.toString());throw Error(W+e)}if(a.prototype=Q,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.EUCLID=9,a.config=a.set=eD,a.clone=e,a.isDecimal=eq,a.abs=eS,a.acos=ek,a.acosh=eA,a.add=eR,a.asin=eT,a.asinh=eP,a.atan=eC,a.atanh=eO,a.atan2=eN,a.cbrt=eI,a.ceil=eU,a.clamp=ej,a.cos=eM,a.cosh=e$,a.div=eL,a.exp=eH,a.floor=eW,a.hypot=eK,a.ln=eB,a.log=eJ,a.log10=ez,a.log2=eV,a.max=eF,a.min=eG,a.mod=eX,a.mul=eZ,a.pow=eY,a.random=eQ,a.round=e0,a.sign=e1,a.sin=e2,a.sinh=e5,a.sqrt=e6,a.sub=e3,a.sum=e8,a.tan=e4,a.tanh=e9,a.trunc=e7,void 0===t&&(t={}),t&&!0!==t.defaults)for(i=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],r=0;r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}($);D=new te(D),M=new te(M);var tt=te},724:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function c(e){if(!e)return;let[[t,r],...n]=s(e),{domain:i,expires:a,httponly:o,maxage:c,path:d,samesite:p,secure:f,partitioned:h,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var b,m,y={name:t,value:decodeURIComponent(r),domain:i,...a&&{expires:new Date(a)},...o&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:d,...p&&{sameSite:l.includes(b=(b=p).toLowerCase())?b:void 0},...f&&{secure:!0},...g&&{priority:u.includes(m=(m=g).toLowerCase())?m:void 0},...h&&{partitioned:!0}};let e={};for(let t in y)y[t]&&(e[t]=y[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(a,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>s,parseSetCookie:()=>c,stringifyCookie:()=>o}),e.exports=((e,a,o,s)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let c of n(a))i.call(e,c)||c===o||t(e,c,{get:()=>a[c],enumerable:!(s=r(a,c))||s.enumerable});return e})(t({},"__esModule",{value:!0}),a);var l=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,a,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=i,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!a||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(i)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},752:(e,t,r)=>{"use strict";let n,i,a,o,s;r.r(t),r.d(t,{default:()=>lr});var c={};r.r(c),r.d(c,{q:()=>nO,l:()=>nU});var l={};async function u(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(l),r.d(l,{config:()=>c9,default:()=>c4});let d=null;async function p(){if("phase-production-build"===process.env.NEXT_PHASE)return;d||(d=u());let e=await d;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function f(...e){let t=await u();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let h=null;function g(){return h||(h=p()),h}function b(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(b(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),g();class m extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class y extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class w extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let v="_N_T_",x={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function _(e){var t,r,n,i,a,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=i,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!a||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function E(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(..._(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function S(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...x,GROUP:{builtinReact:[x.reactServerComponents,x.actionBrowser],serverOnly:[x.reactServerComponents,x.actionBrowser,x.instrument,x.middleware],neutralTarget:[x.apiNode,x.apiEdge],clientOnly:[x.serverSideRendering,x.appPagesBrowser],bundled:[x.reactServerComponents,x.actionBrowser,x.serverSideRendering,x.appPagesBrowser,x.shared,x.instrument,x.middleware],appPages:[x.reactServerComponents,x.serverSideRendering,x.appPagesBrowser,x.actionBrowser]}});let k=Symbol("response"),A=Symbol("passThrough"),R=Symbol("waitUntil");class T{constructor(e,t){this[A]=!1,this[R]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[k]||(this[k]=Promise.resolve(e))}passThroughOnException(){this[A]=!0}waitUntil(e){if("external"===this[R].kind)return(0,this[R].function)(e);this[R].promises.push(e)}}class P extends T{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function C(e){return e.replace(/\/$/,"")||"/"}function O(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function N(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=O(e);return""+t+r+n+i}function I(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=O(e);return""+r+t+n+i}function U(e,t){if("string"!=typeof e)return!1;let{pathname:r}=O(e);return r===t||r.startsWith(t+"/")}let j=new WeakMap;function D(e,t){let r;if(!t)return{pathname:e};let n=j.get(t);n||(n=t.map(e=>e.toLowerCase()),j.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let a=i[1].toLowerCase(),o=n.indexOf(a);return o<0?{pathname:e}:(r=t[o],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let M=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function $(e,t){return new URL(String(e).replace(M,"localhost"),t&&String(t).replace(M,"localhost"))}let L=Symbol("NextURLInternal");class H{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[L]={url:$(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let a=function(e,t){var r,n;let{basePath:i,i18n:a,trailingSlash:o}=null!=(r=t.nextConfig)?r:{},s={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):o};i&&U(s.pathname,i)&&(s.pathname=function(e,t){if(!U(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(s.pathname,i),s.basePath=i);let c=s.pathname;if(s.pathname.startsWith("/_next/data/")&&s.pathname.endsWith(".json")){let e=s.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");s.buildId=e[0],c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(s.pathname=c)}if(a){let e=t.i18nProvider?t.i18nProvider.analyze(s.pathname):D(s.pathname,a.locales);s.locale=e.detectedLocale,s.pathname=null!=(n=e.pathname)?n:s.pathname,!e.detectedLocale&&s.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):D(c,a.locales)).detectedLocale&&(s.locale=e.detectedLocale)}return s}(this[L].url.pathname,{nextConfig:this[L].options.nextConfig,parseData:!0,i18nProvider:this[L].options.i18nProvider}),o=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[L].url,this[L].options.headers);this[L].domainLocale=this[L].options.i18nProvider?this[L].options.i18nProvider.detectDomainLocale(o):function(e,t,r){if(e)for(let a of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=a.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===a.defaultLocale.toLowerCase()||(null==(i=a.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return a}}(null==(t=this[L].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,o);let s=(null==(r=this[L].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[L].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[L].url.pathname=a.pathname,this[L].defaultLocale=s,this[L].basePath=a.basePath??"",this[L].buildId=a.buildId,this[L].locale=a.locale??s,this[L].trailingSlash=a.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(U(i,"/api")||U(i,"/"+t.toLowerCase()))?e:N(e,"/"+t)}((e={basePath:this[L].basePath,buildId:this[L].buildId,defaultLocale:this[L].options.forceLocale?void 0:this[L].defaultLocale,locale:this[L].locale,pathname:this[L].url.pathname,trailingSlash:this[L].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=C(t)),e.buildId&&(t=I(N(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=N(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:I(t,"/"):C(t)}formatSearch(){return this[L].url.search}get buildId(){return this[L].buildId}set buildId(e){this[L].buildId=e}get locale(){return this[L].locale??""}set locale(e){var t,r;if(!this[L].locale||!(null==(r=this[L].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[L].locale=e}get defaultLocale(){return this[L].defaultLocale}get domainLocale(){return this[L].domainLocale}get searchParams(){return this[L].url.searchParams}get host(){return this[L].url.host}set host(e){this[L].url.host=e}get hostname(){return this[L].url.hostname}set hostname(e){this[L].url.hostname=e}get port(){return this[L].url.port}set port(e){this[L].url.port=e}get protocol(){return this[L].url.protocol}set protocol(e){this[L].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[L].url=$(e),this.analyze()}get origin(){return this[L].url.origin}get pathname(){return this[L].url.pathname}set pathname(e){this[L].url.pathname=e}get hash(){return this[L].url.hash}set hash(e){this[L].url.hash=e}get search(){return this[L].url.search}set search(e){this[L].url.search=e}get password(){return this[L].url.password}set password(e){this[L].url.password=e}get username(){return this[L].url.username}set username(e){this[L].url.username=e}get basePath(){return this[L].basePath}set basePath(e){this[L].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new H(String(this),this[L].options)}}var W=r(724);let K=Symbol("internal request");class q extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);S(r),e instanceof Request?super(e,t):super(r,t);let n=new H(r,{headers:E(this.headers),nextConfig:t.nextConfig});this[K]={cookies:new W.RequestCookies(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[K].cookies}get nextUrl(){return this[K].nextUrl}get page(){throw new y}get ua(){throw new w}get url(){return this[K].url}}class B{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let J=Symbol("internal response"),V=new Set([301,302,303,307,308]);function z(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class F extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new W.ResponseCookies(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let a=Reflect.apply(e[n],e,i),o=new Headers(r);return a instanceof W.ResponseCookies&&r.set("x-middleware-set-cookie",a.getAll().map(e=>(0,W.stringifyCookie)(e)).join(",")),z(t,o),a};default:return B.get(e,n,i)}}});this[J]={cookies:n,url:t.url?new H(t.url,{headers:E(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[J].cookies}static json(e,t){let r=Response.json(e,t);return new F(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!V.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",S(e)),new F(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",S(e)),z(t,r),new F(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),z(e,t),new F(null,{...e,headers:t})}}function G(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let X="Next-Router-Prefetch",Z=["RSC","Next-Router-State-Tree",X,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],Y="_rsc";class Q extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new Q}}class ee extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return B.get(t,r,n);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return B.get(t,a,n)},set(t,r,n,i){if("symbol"==typeof r)return B.set(t,r,n,i);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return B.set(t,o??r,n,i)},has(t,r){if("symbol"==typeof r)return B.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&B.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return B.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||B.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return Q.callable;default:return B.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new ee(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}var et=r(535),er=r(115);class en extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new en}}class ei{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return en.callable;default:return B.get(e,t,r)}}})}}let ea=Symbol.for("next.mutated.cookies");class eo{static wrap(e,t){let r=new W.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,a=()=>{let e=et.J.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new W.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},o=new Proxy(r,{get(e,t,r){switch(t){case ea:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),o}finally{a()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),o}finally{a()}};default:return B.get(e,t,r)}}});return o}}function es(e){return"action"===e.phase}function ec(e){if(!es((0,er.XN)(e)))throw new en}var el=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(el||{}),eu=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(eu||{}),ed=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(ed||{}),ep=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(ep||{}),ef=function(e){return e.startServer="startServer.startServer",e}(ef||{}),eh=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(eh||{}),eg=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(eg||{}),eb=function(e){return e.executeRoute="Router.executeRoute",e}(eb||{}),em=function(e){return e.runHandler="Node.runHandler",e}(em||{}),ey=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(ey||{}),ew=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(ew||{}),ev=function(e){return e.execute="Middleware.execute",e}(ev||{});let ex=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],e_=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eE(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:eS,propagation:ek,trace:eA,SpanStatusCode:eR,SpanKind:eT,ROOT_CONTEXT:eP}=n=r(956);class eC extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eO=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof eC})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eR.ERROR,message:null==t?void 0:t.message})),e.end()},eN=new Map,eI=n.createContextKey("next.rootSpanId"),eU=0,ej=()=>eU++,eD={set(e,t,r){e.push({key:t,value:r})}};class eM{getTracerInstance(){return eA.getTracer("next.js","0.0.1")}getContext(){return eS}getTracePropagationData(){let e=eS.active(),t=[];return ek.inject(e,t,eD),t}getActiveScopeSpan(){return eA.getSpan(null==eS?void 0:eS.active())}withPropagatedContext(e,t,r){let n=eS.active();if(eA.getSpanContext(n))return t();let i=ek.extract(n,e,r);return eS.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:a,options:o}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},s=o.spanName??r;if(!ex.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||o.hideSpan)return a();let c=this.getSpanContext((null==o?void 0:o.parentSpan)??this.getActiveScopeSpan()),l=!1;c?(null==(t=eA.getSpanContext(c))?void 0:t.isRemote)&&(l=!0):(c=(null==eS?void 0:eS.active())??eP,l=!0);let u=ej();return o.attributes={"next.span_name":s,"next.span_type":r,...o.attributes},eS.with(c.setValue(eI,u),()=>this.getTracerInstance().startActiveSpan(s,o,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{eN.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&e_.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};l&&eN.set(u,new Map(Object.entries(o.attributes??{})));try{if(a.length>1)return a(e,t=>eO(e,t));let t=a(e);if(eE(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eO(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eO(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return ex.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let a=arguments.length-1,o=arguments[a];if("function"!=typeof o)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(eS.active(),o);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?eA.setSpan(eS.active(),e):void 0}getRootSpanAttributes(){let e=eS.active().getValue(eI);return eN.get(e)}setRootSpanAttribute(e,t){let r=eS.active().getValue(eI),n=eN.get(r);n&&n.set(e,t)}}let e$=(()=>{let e=new eM;return()=>e})(),eL="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eL);class eH{constructor(e,t,r,n){var i;let a=e&&function(e,t){let r=ee.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,o=null==(i=r.get(eL))?void 0:i.value;this._isEnabled=!!(!a&&o&&e&&o===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:eL,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:eL,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eW(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of _(r))n.append("set-cookie",e);for(let e of new W.ResponseCookies(n).getAll())t.set(e)}}var eK=r(802),eq=r.n(eK);class eB extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}class eJ{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new eJ(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let eV=Symbol.for("@next/cache-handlers-map"),ez=Symbol.for("@next/cache-handlers-set"),eF=globalThis;function eG(){if(eF[eV])return eF[eV].entries()}async function eX(e,t){if(!e)return t();let r=eZ(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eZ(e));await eQ(e,t)}}function eZ(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function eY(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eF[ez])return eF[ez].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function eQ(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([eY(r,e.incrementalCache),...Object.values(n),...i])}let e0=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class e1{disable(){throw e0}getStore(){}run(){throw e0}exit(){throw e0}enterWith(){throw e0}static bind(e){return e}}let e2="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage,e5=e2?new e2:new e1;class e6{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eq()),this.callbackQueue.pause()}after(e){if(eE(e))this.waitUntil||e3(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){var t;this.waitUntil||e3();let r=er.FP.getStore();r&&this.workUnitStores.add(r);let n=e5.getStore(),i=n?n.rootTaskSpawnPhase:null==r?void 0:r.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let a=(t=async()=>{try{await e5.run({rootTaskSpawnPhase:i},()=>e())}catch(e){this.reportTaskError("function",e)}},e2?e2.bind(t):e1.bind(t));this.callbackQueue.add(a)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=et.J.getStore();if(!e)throw Object.defineProperty(new eB("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return eX(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eB("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function e3(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function e8(e){let t,r={then:(n,i)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,i))};return r}class e4{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function e9(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let e7=Symbol.for("@next/request-context"),te=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function tt(e,t,r){let n=[],i=r&&r.size>0;for(let t of te(e))t=`${v}${t}`,n.push(t);if(t.pathname&&!i){let e=`${v}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=eG();if(r)for(let[n,i]of r)"getExpiration"in i&&t.set(n,e8(async()=>i.getExpiration(...e)));return t}(n)}}class tr extends q{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new m({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let tn={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},ti=(e,t)=>e$().withPropagatedContext(e.headers,t,tn),ta=!1;async function to(e){var t;let n,i;if(!ta&&(ta=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(905);e(),ti=t(ti)}await g();let a=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let o=new H(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...o.searchParams.keys()]){let t=o.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(o.searchParams.delete(r),t))o.searchParams.append(r,e);o.searchParams.delete(e)}}let s=o.buildId;o.buildId="";let c=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),l=c.has("x-nextjs-data"),u="1"===c.get("RSC");l&&"/index"===o.pathname&&(o.pathname="/");let d=new Map;if(!a)for(let e of Z){let t=e.toLowerCase(),r=c.get(t);null!==r&&(d.set(t,r),c.delete(t))}let p=new tr({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(Y),t?r.toString():r})(o).toString(),init:{body:e.request.body,headers:c,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});l&&Object.defineProperty(p,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:e9()})}));let f=e.request.waitUntil??(null==(t=function(){let e=globalThis[e7];return null==e?void 0:e.get()}())?void 0:t.waitUntil),h=new P({request:p,page:e.page,context:f?{waitUntil:f}:void 0});if((n=await ti(p,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=h.waitUntil.bind(h),r=new e4;return e$().trace(ev.execute,{spanName:`middleware ${p.method} ${p.nextUrl.pathname}`,attributes:{"http.target":p.nextUrl.pathname,"http.method":p.method}},async()=>{try{var n,a,o,c,l,u;let d=e9(),f=await tt("/",p.nextUrl,null),g=(l=p.nextUrl,u=e=>{i=e},function(e,t,r,n,i,a,o,s,c,l,u){function d(e){r&&r.setHeader("Set-Cookie",e)}let p={};return{type:"request",phase:e,implicitTags:a,url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return p.headers||(p.headers=function(e){let t=ee.from(e);for(let e of Z)t.delete(e.toLowerCase());return ee.seal(t)}(t.headers)),p.headers},get cookies(){if(!p.cookies){let e=new W.RequestCookies(ee.from(t.headers));eW(t,e),p.cookies=ei.seal(e)}return p.cookies},set cookies(value){p.cookies=value},get mutableCookies(){if(!p.mutableCookies){let e=function(e,t){let r=new W.RequestCookies(ee.from(e));return eo.wrap(r,t)}(t.headers,o||(r?d:void 0));eW(t,e),p.mutableCookies=e}return p.mutableCookies},get userspaceMutableCookies(){return p.userspaceMutableCookies||(p.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return ec("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return ec("cookies().set"),e.set(...r),t};default:return B.get(e,r,n)}}});return t}(this.mutableCookies)),p.userspaceMutableCookies},get draftMode(){return p.draftMode||(p.draftMode=new eH(c,t,this.cookies,this.mutableCookies)),p.draftMode},renderResumeDataCache:s??null,isHmrRefresh:l,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",p,void 0,l,{},f,u,void 0,d,!1,void 0)),b=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:a,previouslyRevalidatedTags:o}){var s;let c={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(s=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?s:"/"+s,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:a,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new e6({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:o,refreshTagsByCacheKind:function(){let e=new Map,t=eG();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,e8(async()=>n.refreshTags()));return e}()};return r.store=c,c}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(a=e.request.nextConfig)||null==(n=a.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(c=e.request.nextConfig)||null==(o=c.experimental)?void 0:o.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:p.headers.has(X),buildId:s??"",previouslyRevalidatedTags:[]});return await et.J.run(b,()=>er.FP.run(g,e.handler,p,h))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(p,h)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let b=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&b&&(u||!a)){let t=new H(b,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});a||t.host!==p.nextUrl.host||(t.buildId=s||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=G(t.toString(),o.toString());!a&&l&&n.headers.set("x-nextjs-rewrite",r),u&&i&&(o.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),o.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let m=null==n?void 0:n.headers.get("Location");if(n&&m&&!a){let t=new H(m,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===o.host&&(t.buildId=s||t.buildId,n.headers.set("Location",t.toString())),l&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",G(t.toString(),o.toString()).url))}let y=n||F.next(),w=y.headers.get("x-middleware-override-headers"),v=[];if(w){for(let[e,t]of d)y.headers.set(`x-middleware-request-${e}`,t),v.push(e);v.length>0&&y.headers.set("x-middleware-override-headers",w+","+v.join(","))}return{response:y,waitUntil:("internal"===h[R].kind?Promise.all(h[R].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:p.fetchMetrics}}var ts=function(e,t,r,n,i){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!i)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(e,r):i?i.value=r:t.set(e,r),r},tc=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};function tl(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}authjs.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}authjs.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}authjs.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}authjs.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}authjs.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}authjs.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},webauthnChallenge:{name:`${t}authjs.challenge`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}}}}class tu{constructor(e,t,r){if(io.add(this),is.set(this,{}),ic.set(this,void 0),il.set(this,void 0),ts(this,il,r,"f"),ts(this,ic,e,"f"),!t)return;let{name:n}=e;for(let[e,r]of Object.entries(t))e.startsWith(n)&&r&&(tc(this,is,"f")[e]=r)}get value(){return Object.keys(tc(this,is,"f")).sort((e,t)=>parseInt(e.split(".").pop()||"0")-parseInt(t.split(".").pop()||"0")).map(e=>tc(this,is,"f")[e]).join("")}chunk(e,t){let r=tc(this,io,"m",id).call(this);for(let n of tc(this,io,"m",iu).call(this,{name:tc(this,ic,"f").name,value:e,options:{...tc(this,ic,"f").options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(tc(this,io,"m",id).call(this))}}is=new WeakMap,ic=new WeakMap,il=new WeakMap,io=new WeakSet,iu=function(e){let t=Math.ceil(e.value.length/3936);if(1===t)return tc(this,is,"f")[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3936*n,3936);r.push({...e,name:t,value:i}),tc(this,is,"f")[t]=i}return tc(this,il,"f").debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:160,valueSize:e.value.length,chunks:r.map(e=>e.value.length+160)}),r},id=function(){let e={};for(let t in tc(this,is,"f"))delete tc(this,is,"f")?.[t],e[t]={name:t,value:"",options:{...tc(this,ic,"f").options,maxAge:0}};return e};class td extends Error{constructor(e,t){e instanceof Error?super(void 0,{cause:{err:e,...e.cause,...t}}):"string"==typeof e?(t instanceof Error&&(t={err:t,...t.cause}),super(e,t)):super(void 0,e),this.name=this.constructor.name,this.type=this.constructor.type??"AuthError",this.kind=this.constructor.kind??"error",Error.captureStackTrace?.(this,this.constructor);let r=`https://errors.authjs.dev#${this.type.toLowerCase()}`;this.message+=`${this.message?". ":""}Read more at ${r}`}}class tp extends td{}tp.kind="signIn";class tf extends td{}tf.type="AdapterError";class th extends td{}th.type="AccessDenied";class tg extends td{}tg.type="CallbackRouteError";class tb extends td{}tb.type="ErrorPageLoop";class tm extends td{}tm.type="EventError";class ty extends td{}ty.type="InvalidCallbackUrl";class tw extends tp{constructor(){super(...arguments),this.code="credentials"}}tw.type="CredentialsSignin";class tv extends td{}tv.type="InvalidEndpoints";class tx extends td{}tx.type="InvalidCheck";class t_ extends td{}t_.type="JWTSessionError";class tE extends td{}tE.type="MissingAdapter";class tS extends td{}tS.type="MissingAdapterMethods";class tk extends td{}tk.type="MissingAuthorize";class tA extends td{}tA.type="MissingSecret";class tR extends tp{}tR.type="OAuthAccountNotLinked";class tT extends tp{}tT.type="OAuthCallbackError";class tP extends td{}tP.type="OAuthProfileParseError";class tC extends td{}tC.type="SessionTokenError";class tO extends tp{}tO.type="OAuthSignInError";class tN extends tp{}tN.type="EmailSignInError";class tI extends td{}tI.type="SignOutError";class tU extends td{}tU.type="UnknownAction";class tj extends td{}tj.type="UnsupportedStrategy";class tD extends td{}tD.type="InvalidProvider";class tM extends td{}tM.type="UntrustedHost";class t$ extends td{}t$.type="Verification";class tL extends tp{}tL.type="MissingCSRF";let tH=new Set(["CredentialsSignin","OAuthAccountNotLinked","OAuthCallbackError","AccessDenied","Verification","MissingCSRF","AccountNotLinked","WebAuthnVerificationError"]);class tW extends td{}tW.type="DuplicateConditionalUI";class tK extends td{}tK.type="MissingWebAuthnAutocomplete";class tq extends td{}tq.type="WebAuthnVerificationError";class tB extends tp{}tB.type="AccountNotLinked";class tJ extends td{}tJ.type="ExperimentalFeatureNotEnabled";let tV=!1;function tz(e,t){try{return/^https?:/.test(new URL(e,e.startsWith("/")?t:void 0).protocol)}catch{return!1}}let tF=!1,tG=!1,tX=!1,tZ=["createVerificationToken","useVerificationToken","getUserByEmail"],tY=["createUser","getUser","getUserByEmail","getUserByAccount","updateUser","linkAccount","createSession","getSessionAndUser","updateSession","deleteSession"],tQ=["createUser","getUser","linkAccount","getAccount","getAuthenticator","createAuthenticator","listAuthenticatorsByUserId","updateAuthenticatorCounter"],t0=()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")},t1=async(e,t,r,n,i)=>{let{crypto:{subtle:a}}=t0();return new Uint8Array(await a.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:n},await a.importKey("raw",t,"HKDF",!1,["deriveBits"]),i<<3))};function t2(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function t5(e,t,r,n,i){return t1(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=t2(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),t2(r,"salt"),function(e){let t=t2(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(i,e))}let t6=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await crypto.subtle.digest(r,t))},t3=new TextEncoder,t8=new TextDecoder;function t4(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}function t9(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function t7(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return t9(r,t,0),t9(r,e%0x100000000,4),r}function re(e){let t=new Uint8Array(4);return t9(t,e),t}function rt(e){if(Uint8Array.fromBase64)return Uint8Array.fromBase64("string"==typeof e?e:t8.decode(e),{alphabet:"base64url"});let t=e;t instanceof Uint8Array&&(t=t8.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{var r=t;if(Uint8Array.fromBase64)return Uint8Array.fromBase64(r);let e=atob(r),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n}catch{throw TypeError("The input to be decoded is not correctly encoded.")}}function rr(e){let t=e;return("string"==typeof t&&(t=t3.encode(t)),Uint8Array.prototype.toBase64)?t.toBase64({alphabet:"base64url",omitPadding:!0}):(function(e){if(Uint8Array.prototype.toBase64)return e.toBase64();let t=[];for(let r=0;r<e.length;r+=32768)t.push(String.fromCharCode.apply(null,e.subarray(r,r+32768)));return btoa(t.join(""))})(t).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class rn extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class ri extends rn{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ra extends rn{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",n="unspecified"){super(e,{cause:{claim:r,reason:n,payload:t}}),this.claim=r,this.reason=n,this.payload=t}}class ro extends rn{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class rs extends rn{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class rc extends rn{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class rl extends rn{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class ru extends rn{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class rd extends rn{static code="ERR_JWK_INVALID";code="ERR_JWK_INVALID"}class rp extends rn{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}function rf(e){if(!rh(e))throw Error("CryptoKey instance expected")}function rh(e){return e?.[Symbol.toStringTag]==="CryptoKey"}function rg(e){return e?.[Symbol.toStringTag]==="KeyObject"}let rb=e=>rh(e)||rg(e),rm=e=>{if(!function(e){return"object"==typeof e&&null!==e}(e)||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t};function ry(e){return rm(e)&&"string"==typeof e.kty}function rw(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let rv=(e,...t)=>rw("Key must be ",e,...t);function rx(e,t,...r){return rw(`Key for the ${e} algorithm must be `,t,...r)}async function r_(e){if(rg(e))if("secret"!==e.type)return e.export({format:"jwk"});else e=e.export();if(e instanceof Uint8Array)return{kty:"oct",k:rr(e)};if(!rh(e))throw TypeError(rv(e,"CryptoKey","KeyObject","Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:i,...a}=await crypto.subtle.exportKey("jwk",e);return a}async function rE(e){return r_(e)}let rS=(e,t)=>{if("string"!=typeof e||!e)throw new rd(`${t} missing or invalid`)};async function rk(e,t){let r,n;if(ry(e))r=e;else if(rb(e))r=await rE(e);else throw TypeError(rv(e,"CryptoKey","KeyObject","JSON Web Key"));if("sha256"!==(t??="sha256")&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(r.kty){case"EC":rS(r.crv,'"crv" (Curve) Parameter'),rS(r.x,'"x" (X Coordinate) Parameter'),rS(r.y,'"y" (Y Coordinate) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x,y:r.y};break;case"OKP":rS(r.crv,'"crv" (Subtype of Key Pair) Parameter'),rS(r.x,'"x" (Public Key) Parameter'),n={crv:r.crv,kty:r.kty,x:r.x};break;case"RSA":rS(r.e,'"e" (Exponent) Parameter'),rS(r.n,'"n" (Modulus) Parameter'),n={e:r.e,kty:r.kty,n:r.n};break;case"oct":rS(r.k,'"k" (Key Value) Parameter'),n={k:r.k,kty:r.kty};break;default:throw new rs('"kty" (Key Type) Parameter missing or unsupported')}let i=t3.encode(JSON.stringify(n));return rr(await t6(t,i))}let rA=Symbol();function rR(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new rs(`Unsupported JWE Algorithm: ${e}`)}}let rT=e=>crypto.getRandomValues(new Uint8Array(rR(e)>>3)),rP=(e,t)=>{if(t.length<<3!==rR(e))throw new rl("Invalid Initialization Vector length")},rC=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new rl(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)};function rO(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function rN(e,t){return e.name===t}function rI(e,t,r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!rN(e.algorithm,"AES-GCM"))throw rO("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw rO(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!rN(e.algorithm,"AES-KW"))throw rO("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw rO(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":break;default:throw rO("ECDH or X25519")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!rN(e.algorithm,"PBKDF2"))throw rO("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!rN(e.algorithm,"RSA-OAEP"))throw rO("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(parseInt(e.algorithm.hash.name.slice(4),10)!==r)throw rO(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}var n=e,i=r;if(i&&!n.usages.includes(i))throw TypeError(`CryptoKey does not support this operation, its usages must include ${i}.`)}async function rU(e,t,r,n,i){if(!(r instanceof Uint8Array))throw TypeError(rv(r,"Uint8Array"));let a=parseInt(e.slice(1,4),10),o=await crypto.subtle.importKey("raw",r.subarray(a>>3),"AES-CBC",!1,["encrypt"]),s=await crypto.subtle.importKey("raw",r.subarray(0,a>>3),{hash:`SHA-${a<<1}`,name:"HMAC"},!1,["sign"]),c=new Uint8Array(await crypto.subtle.encrypt({iv:n,name:"AES-CBC"},o,t)),l=t4(i,n,c,t7(i.length<<3));return{ciphertext:c,tag:new Uint8Array((await crypto.subtle.sign("HMAC",s,l)).slice(0,a>>3)),iv:n}}async function rj(e,t,r,n,i){let a;r instanceof Uint8Array?a=await crypto.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(rI(r,e,"encrypt"),a=r);let o=new Uint8Array(await crypto.subtle.encrypt({additionalData:i,iv:n,name:"AES-GCM",tagLength:128},a,t)),s=o.slice(-16);return{ciphertext:o.slice(0,-16),tag:s,iv:n}}let rD=async(e,t,r,n,i)=>{if(!rh(r)&&!(r instanceof Uint8Array))throw TypeError(rv(r,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));switch(n?rP(e,n):n=rT(e),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&rC(r,parseInt(e.slice(-3),10)),rU(e,t,r,n,i);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&rC(r,parseInt(e.slice(1,4),10)),rj(e,t,r,n,i);default:throw new rs("Unsupported JWE Content Encryption Algorithm")}};function rM(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function r$(e,t,r){return e instanceof Uint8Array?crypto.subtle.importKey("raw",e,"AES-KW",!0,[r]):(rI(e,t,r),e)}async function rL(e,t,r){let n=await r$(t,e,"wrapKey");rM(n,e);let i=await crypto.subtle.importKey("raw",r,{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.wrapKey("raw",i,n,"AES-KW"))}async function rH(e,t,r){let n=await r$(t,e,"unwrapKey");rM(n,e);let i=await crypto.subtle.unwrapKey("raw",r,n,"AES-KW",{hash:"SHA-256",name:"HMAC"},!0,["sign"]);return new Uint8Array(await crypto.subtle.exportKey("raw",i))}function rW(e){return t4(re(e.length),e)}async function rK(e,t,r){let n=Math.ceil((t>>3)/32),i=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(re(t+1)),n.set(e,4),n.set(r,4+e.length),i.set(await t6("sha256",n),32*t)}return i.slice(0,t>>3)}async function rq(e,t,r,n,i=new Uint8Array(0),a=new Uint8Array(0)){let o;rI(e,"ECDH"),rI(t,"ECDH","deriveBits");let s=t4(rW(t3.encode(r)),rW(i),rW(a),re(n));return o="X25519"===e.algorithm.name?256:Math.ceil(parseInt(e.algorithm.namedCurve.slice(-3),10)/8)<<3,rK(new Uint8Array(await crypto.subtle.deriveBits({name:e.algorithm.name,public:e},t,o)),n,s)}function rB(e){switch(e.algorithm.namedCurve){case"P-256":case"P-384":case"P-521":return!0;default:return"X25519"===e.algorithm.name}}let rJ=(e,t)=>t4(t3.encode(e),new Uint8Array([0]),t);async function rV(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new rl("PBES2 Salt Input must be 8 or more octets");let i=rJ(t,e),a=parseInt(t.slice(13,16),10),o={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:i},s=await (n instanceof Uint8Array?crypto.subtle.importKey("raw",n,"PBKDF2",!1,["deriveBits"]):(rI(n,t,"deriveBits"),n));return new Uint8Array(await crypto.subtle.deriveBits(o,s,a))}async function rz(e,t,r,n=2048,i=crypto.getRandomValues(new Uint8Array(16))){let a=await rV(i,e,n,t);return{encryptedKey:await rL(e.slice(-6),a,r),p2c:n,p2s:rr(i)}}async function rF(e,t,r,n,i){let a=await rV(i,e,n,t);return rH(e.slice(-6),a,r)}let rG=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},rX=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new rs(`alg ${e} is not supported either by JOSE or your javascript runtime`)}};async function rZ(e,t,r){return rI(t,e,"encrypt"),rG(e,t),new Uint8Array(await crypto.subtle.encrypt(rX(e),t,r))}async function rY(e,t,r){return rI(t,e,"decrypt"),rG(e,t),new Uint8Array(await crypto.subtle.decrypt(rX(e),t,r))}let rQ=async e=>{if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:t,keyUsages:r}=function(e){let t,r;switch(e.kty){case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new rs('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new rs('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"Ed25519":case"EdDSA":t={name:"Ed25519"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new rs('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new rs('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),n={...e};return delete n.alg,delete n.use,crypto.subtle.importKey("jwk",n,t,e.ext??!e.d,e.key_ops??r)},r0=async(e,t,r,n=!1)=>{let a=(i||=new WeakMap).get(e);if(a?.[r])return a[r];let o=await rQ({...t,alg:r});return n&&Object.freeze(e),a?a[r]=o:i.set(e,{[r]:o}),o},r1=(e,t)=>{let r,n=(i||=new WeakMap).get(e);if(n?.[t])return n[t];let a="public"===e.type,o=!!a;if("x25519"===e.asymmetricKeyType){switch(t){case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}r=e.toCryptoKey(e.asymmetricKeyType,o,a?[]:["deriveBits"])}if("ed25519"===e.asymmetricKeyType){if("EdDSA"!==t&&"Ed25519"!==t)throw TypeError("given KeyObject instance cannot be used for this algorithm");r=e.toCryptoKey(e.asymmetricKeyType,o,[a?"verify":"sign"])}if("rsa"===e.asymmetricKeyType){let n;switch(t){case"RSA-OAEP":n="SHA-1";break;case"RS256":case"PS256":case"RSA-OAEP-256":n="SHA-256";break;case"RS384":case"PS384":case"RSA-OAEP-384":n="SHA-384";break;case"RS512":case"PS512":case"RSA-OAEP-512":n="SHA-512";break;default:throw TypeError("given KeyObject instance cannot be used for this algorithm")}if(t.startsWith("RSA-OAEP"))return e.toCryptoKey({name:"RSA-OAEP",hash:n},o,a?["encrypt"]:["decrypt"]);r=e.toCryptoKey({name:t.startsWith("PS")?"RSA-PSS":"RSASSA-PKCS1-v1_5",hash:n},o,[a?"verify":"sign"])}if("ec"===e.asymmetricKeyType){let n=new Map([["prime256v1","P-256"],["secp384r1","P-384"],["secp521r1","P-521"]]).get(e.asymmetricKeyDetails?.namedCurve);if(!n)throw TypeError("given KeyObject instance cannot be used for this algorithm");"ES256"===t&&"P-256"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),"ES384"===t&&"P-384"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),"ES512"===t&&"P-521"===n&&(r=e.toCryptoKey({name:"ECDSA",namedCurve:n},o,[a?"verify":"sign"])),t.startsWith("ECDH-ES")&&(r=e.toCryptoKey({name:"ECDH",namedCurve:n},o,a?[]:["deriveBits"]))}if(!r)throw TypeError("given KeyObject instance cannot be used for this algorithm");return n?n[t]=r:i.set(e,{[t]:r}),r},r2=async(e,t)=>{if(e instanceof Uint8Array||rh(e))return e;if(rg(e)){if("secret"===e.type)return e.export();if("toCryptoKey"in e&&"function"==typeof e.toCryptoKey)try{return r1(e,t)}catch(e){if(e instanceof TypeError)throw e}let r=e.export({format:"jwk"});return r0(e,r,t)}if(ry(e))return e.k?rt(e.k):r0(e,e,t,!0);throw Error("unreachable")};function r5(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new rs(`Unsupported JWE Algorithm: ${e}`)}}let r6=e=>crypto.getRandomValues(new Uint8Array(r5(e)>>3));async function r3(e,t){if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");let r={name:"HMAC",hash:"SHA-256"},n=await crypto.subtle.generateKey(r,!1,["sign"]),i=new Uint8Array(await crypto.subtle.sign(r,n,e)),a=new Uint8Array(await crypto.subtle.sign(r,n,t)),o=0,s=-1;for(;++s<32;)o|=i[s]^a[s];return 0===o}async function r8(e,t,r,n,i,a){let o,s;if(!(t instanceof Uint8Array))throw TypeError(rv(t,"Uint8Array"));let c=parseInt(e.slice(1,4),10),l=await crypto.subtle.importKey("raw",t.subarray(c>>3),"AES-CBC",!1,["decrypt"]),u=await crypto.subtle.importKey("raw",t.subarray(0,c>>3),{hash:`SHA-${c<<1}`,name:"HMAC"},!1,["sign"]),d=t4(a,n,r,t7(a.length<<3)),p=new Uint8Array((await crypto.subtle.sign("HMAC",u,d)).slice(0,c>>3));try{o=await r3(i,p)}catch{}if(!o)throw new rc;try{s=new Uint8Array(await crypto.subtle.decrypt({iv:n,name:"AES-CBC"},l,r))}catch{}if(!s)throw new rc;return s}async function r4(e,t,r,n,i,a){let o;t instanceof Uint8Array?o=await crypto.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(rI(t,e,"decrypt"),o=t);try{return new Uint8Array(await crypto.subtle.decrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},o,t4(r,i)))}catch{throw new rc}}let r9=async(e,t,r,n,i,a)=>{if(!rh(t)&&!(t instanceof Uint8Array))throw TypeError(rv(t,"CryptoKey","KeyObject","Uint8Array","JSON Web Key"));if(!n)throw new rl("JWE Initialization Vector missing");if(!i)throw new rl("JWE Authentication Tag missing");switch(rP(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&rC(t,parseInt(e.slice(-3),10)),r8(e,t,r,n,i,a);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&rC(t,parseInt(e.slice(1,4),10)),r4(e,t,r,n,i,a);default:throw new rs("Unsupported JWE Content Encryption Algorithm")}};async function r7(e,t,r,n){let i=e.slice(0,7),a=await rD(i,r,t,n,new Uint8Array(0));return{encryptedKey:a.ciphertext,iv:rr(a.iv),tag:rr(a.tag)}}async function ne(e,t,r,n,i){return r9(e.slice(0,7),t,r,n,i,new Uint8Array(0))}let nt=async(e,t,r,n,i={})=>{let a,o,s;switch(e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let c;if(rf(r),!rB(r))throw new rs("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:l,apv:u}=i;c=i.epk?await r2(i.epk,e):(await crypto.subtle.generateKey(r.algorithm,!0,["deriveBits"])).privateKey;let{x:d,y:p,crv:f,kty:h}=await rE(c),g=await rq(r,c,"ECDH-ES"===e?t:e,"ECDH-ES"===e?r5(t):parseInt(e.slice(-5,-2),10),l,u);if(o={epk:{x:d,crv:f,kty:h}},"EC"===h&&(o.epk.y=p),l&&(o.apu=rr(l)),u&&(o.apv=rr(u)),"ECDH-ES"===e){s=g;break}s=n||r6(t);let b=e.slice(-6);a=await rL(b,g,s);break}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||r6(t),rf(r),a=await rZ(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||r6(t);let{p2c:c,p2s:l}=i;({encryptedKey:a,...o}=await rz(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||r6(t),a=await rL(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||r6(t);let{iv:c}=i;({encryptedKey:a,...o}=await r7(e,r,s,c));break}default:throw new rs('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:a,parameters:o}},nr=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},nn=(e,t,r,n,i)=>{let a;if(void 0!==i.crit&&n?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(a=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!a.has(o))throw new rs(`Extension Header Parameter "${o}" is not recognized`);if(void 0===i[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(a.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},ni=e=>e?.[Symbol.toStringTag],na=(e,t,r)=>{if(void 0!==t.use){let e;switch(r){case"sign":case"verify":e="sig";break;case"encrypt":case"decrypt":e="enc"}if(t.use!==e)throw TypeError(`Invalid key for this operation, its "use" must be "${e}" when present`)}if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, its "alg" must be "${e}" when present`);if(Array.isArray(t.key_ops)){let n;switch(!0){case"sign"===r||"verify"===r:case"dir"===e:case e.includes("CBC-HS"):n=r;break;case e.startsWith("PBES2"):n="deriveBits";break;case/^A\d{3}(?:GCM)?(?:KW)?$/.test(e):n=!e.includes("GCM")&&e.endsWith("KW")?"encrypt"===r?"wrapKey":"unwrapKey":r;break;case"encrypt"===r&&e.startsWith("RSA"):n="wrapKey";break;case"decrypt"===r:n=e.startsWith("RSA")?"unwrapKey":"deriveBits"}if(n&&t.key_ops?.includes?.(n)===!1)throw TypeError(`Invalid key for this operation, its "key_ops" must include "${n}" when present`)}return!0},no=(e,t,r)=>{if(!(t instanceof Uint8Array)){if(ry(t)){if(function(e){return"oct"===e.kty&&"string"==typeof e.k}(t)&&na(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!rb(t))throw TypeError(rx(e,t,"CryptoKey","KeyObject","JSON Web Key","Uint8Array"));if("secret"!==t.type)throw TypeError(`${ni(t)} instances for symmetric algorithms must be of type "secret"`)}},ns=(e,t,r)=>{if(ry(t))switch(r){case"decrypt":case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&na(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"encrypt":case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&na(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!rb(t))throw TypeError(rx(e,t,"CryptoKey","KeyObject","JSON Web Key"));if("secret"===t.type)throw TypeError(`${ni(t)} instances for asymmetric algorithms must not be of type "secret"`);if("public"===t.type)switch(r){case"sign":throw TypeError(`${ni(t)} instances for asymmetric algorithm signing must be of type "private"`);case"decrypt":throw TypeError(`${ni(t)} instances for asymmetric algorithm decryption must be of type "private"`)}if("private"===t.type)switch(r){case"verify":throw TypeError(`${ni(t)} instances for asymmetric algorithm verifying must be of type "public"`);case"encrypt":throw TypeError(`${ni(t)} instances for asymmetric algorithm encryption must be of type "public"`)}},nc=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(e)||/^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(e)?no(e,t,r):ns(e,t,r)};class nl{#e;#t;#r;#n;#i;#a;#o;#s;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this.#e=e}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setSharedUnprotectedHeader(e){if(this.#r)throw TypeError("setSharedUnprotectedHeader can only be called once");return this.#r=e,this}setUnprotectedHeader(e){if(this.#n)throw TypeError("setUnprotectedHeader can only be called once");return this.#n=e,this}setAdditionalAuthenticatedData(e){return this.#i=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}async encrypt(e,t){let r,n,i,a,o;if(!this.#t&&!this.#n&&!this.#r)throw new rl("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!nr(this.#t,this.#n,this.#r))throw new rl("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let s={...this.#t,...this.#n,...this.#r};if(nn(rl,new Map,t?.crit,this.#t,s),void 0!==s.zip)throw new rs('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:c,enc:l}=s;if("string"!=typeof c||!c)throw new rl('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof l||!l)throw new rl('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(this.#a&&("dir"===c||"ECDH-ES"===c))throw TypeError(`setContentEncryptionKey cannot be called with JWE "alg" (Algorithm) Header ${c}`);nc("dir"===c?l:c,e,"encrypt");{let i,a=await r2(e,c);({cek:n,encryptedKey:r,parameters:i}=await nt(c,l,a,this.#a,this.#s)),i&&(t&&rA in t?this.#n?this.#n={...this.#n,...i}:this.setUnprotectedHeader(i):this.#t?this.#t={...this.#t,...i}:this.setProtectedHeader(i))}a=this.#t?t3.encode(rr(JSON.stringify(this.#t))):t3.encode(""),this.#i?(o=rr(this.#i),i=t4(a,t3.encode("."),t3.encode(o))):i=a;let{ciphertext:u,tag:d,iv:p}=await rD(l,this.#e,n,this.#o,i),f={ciphertext:rr(u)};return p&&(f.iv=rr(p)),d&&(f.tag=rr(d)),r&&(f.encrypted_key=rr(r)),o&&(f.aad=o),this.#t&&(f.protected=t8.decode(a)),this.#r&&(f.unprotected=this.#r),this.#n&&(f.header=this.#n),f}}class nu{#c;constructor(e){this.#c=new nl(e)}setContentEncryptionKey(e){return this.#c.setContentEncryptionKey(e),this}setInitializationVector(e){return this.#c.setInitializationVector(e),this}setProtectedHeader(e){return this.#c.setProtectedHeader(e),this}setKeyManagementParameters(e){return this.#c.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this.#c.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let nd=e=>Math.floor(e.getTime()/1e3),np=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,nf=e=>{let t,r=np.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let n=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(n);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*n);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*n);break;case"day":case"days":case"d":t=Math.round(86400*n);break;case"week":case"weeks":case"w":t=Math.round(604800*n);break;default:t=Math.round(0x1e187e0*n)}return"-"===r[1]||"ago"===r[4]?-t:t};function nh(e,t){if(!Number.isFinite(t))throw TypeError(`Invalid ${e} input`);return t}let ng=e=>e.includes("/")?e.toLowerCase():`application/${e.toLowerCase()}`,nb=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e)));class nm{#l;constructor(e){if(!rm(e))throw TypeError("JWT Claims Set MUST be an object");this.#l=structuredClone(e)}data(){return t3.encode(JSON.stringify(this.#l))}get iss(){return this.#l.iss}set iss(e){this.#l.iss=e}get sub(){return this.#l.sub}set sub(e){this.#l.sub=e}get aud(){return this.#l.aud}set aud(e){this.#l.aud=e}set jti(e){this.#l.jti=e}set nbf(e){"number"==typeof e?this.#l.nbf=nh("setNotBefore",e):e instanceof Date?this.#l.nbf=nh("setNotBefore",nd(e)):this.#l.nbf=nd(new Date)+nf(e)}set exp(e){"number"==typeof e?this.#l.exp=nh("setExpirationTime",e):e instanceof Date?this.#l.exp=nh("setExpirationTime",nd(e)):this.#l.exp=nd(new Date)+nf(e)}set iat(e){void 0===e?this.#l.iat=nd(new Date):e instanceof Date?this.#l.iat=nh("setIssuedAt",nd(e)):"string"==typeof e?this.#l.iat=nh("setIssuedAt",nd(new Date)+nf(e)):this.#l.iat=nh("setIssuedAt",e)}}class ny{#a;#o;#s;#t;#u;#d;#p;#f;constructor(e={}){this.#f=new nm(e)}setIssuer(e){return this.#f.iss=e,this}setSubject(e){return this.#f.sub=e,this}setAudience(e){return this.#f.aud=e,this}setJti(e){return this.#f.jti=e,this}setNotBefore(e){return this.#f.nbf=e,this}setExpirationTime(e){return this.#f.exp=e,this}setIssuedAt(e){return this.#f.iat=e,this}setProtectedHeader(e){if(this.#t)throw TypeError("setProtectedHeader can only be called once");return this.#t=e,this}setKeyManagementParameters(e){if(this.#s)throw TypeError("setKeyManagementParameters can only be called once");return this.#s=e,this}setContentEncryptionKey(e){if(this.#a)throw TypeError("setContentEncryptionKey can only be called once");return this.#a=e,this}setInitializationVector(e){if(this.#o)throw TypeError("setInitializationVector can only be called once");return this.#o=e,this}replicateIssuerAsHeader(){return this.#u=!0,this}replicateSubjectAsHeader(){return this.#d=!0,this}replicateAudienceAsHeader(){return this.#p=!0,this}async encrypt(e,t){let r=new nu(this.#f.data());return this.#t&&(this.#u||this.#d||this.#p)&&(this.#t={...this.#t,iss:this.#u?this.#f.iss:void 0,sub:this.#d?this.#f.sub:void 0,aud:this.#p?this.#f.aud:void 0}),r.setProtectedHeader(this.#t),this.#o&&r.setInitializationVector(this.#o),this.#a&&r.setContentEncryptionKey(this.#a),this.#s&&r.setKeyManagementParameters(this.#s),r.encrypt(e,t)}}async function nw(e,t,r){let n;if(!rm(e))throw TypeError("JWK must be an object");switch(t??=e.alg,n??=r?.extractable??e.ext,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return rt(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new rs('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return rQ({...e,alg:t,ext:n});default:throw new rs('Unsupported "kty" (Key Type) Parameter value')}}let nv=async(e,t,r,n,i)=>{switch(e){case"dir":if(void 0!==r)throw new rl("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new rl("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,a;if(!rm(n.epk))throw new rl('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(rf(t),!rB(t))throw new rs("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await nw(n.epk,e);if(rf(o),void 0!==n.apu){if("string"!=typeof n.apu)throw new rl('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=rt(n.apu)}catch{throw new rl("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new rl('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{a=rt(n.apv)}catch{throw new rl("Failed to base64url decode the apv")}}let s=await rq(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?r5(n.enc):parseInt(e.slice(-5,-2),10),i,a);if("ECDH-ES"===e)return s;if(void 0===r)throw new rl("JWE Encrypted Key missing");return rH(e.slice(-6),s,r)}case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new rl("JWE Encrypted Key missing");return rf(t),rY(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let a;if(void 0===r)throw new rl("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new rl('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=i?.maxPBES2Count||1e4;if(n.p2c>o)throw new rl('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new rl('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{a=rt(n.p2s)}catch{throw new rl("Failed to base64url decode the p2s")}return rF(e,t,r,n.p2c,a)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new rl("JWE Encrypted Key missing");return rH(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,a;if(void 0===r)throw new rl("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new rl('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new rl('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=rt(n.iv)}catch{throw new rl("Failed to base64url decode the iv")}try{a=rt(n.tag)}catch{throw new rl("Failed to base64url decode the tag")}return ne(e,t,r,i,a)}default:throw new rs('Invalid or unsupported "alg" (JWE Algorithm) header value')}},nx=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function n_(e,t,r){let n,i,a,o,s,c,l;if(!rm(e))throw new rl("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new rl("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new rl("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new rl("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new rl("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new rl("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new rl("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new rl("JWE AAD incorrect type");if(void 0!==e.header&&!rm(e.header))throw new rl("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!rm(e.unprotected))throw new rl("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=rt(e.protected);n=JSON.parse(t8.decode(t))}catch{throw new rl("JWE Protected Header is invalid")}if(!nr(n,e.header,e.unprotected))throw new rl("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let u={...n,...e.header,...e.unprotected};if(nn(rl,new Map,r?.crit,n,u),void 0!==u.zip)throw new rs('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:d,enc:p}=u;if("string"!=typeof d||!d)throw new rl("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof p||!p)throw new rl("missing JWE Encryption Algorithm (enc) in JWE Header");let f=r&&nx("keyManagementAlgorithms",r.keyManagementAlgorithms),h=r&&nx("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(f&&!f.has(d)||!f&&d.startsWith("PBES2"))throw new ro('"alg" (Algorithm) Header Parameter value not allowed');if(h&&!h.has(p))throw new ro('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=rt(e.encrypted_key)}catch{throw new rl("Failed to base64url decode the encrypted_key")}let g=!1;"function"==typeof t&&(t=await t(n,e),g=!0),nc("dir"===d?p:d,t,"decrypt");let b=await r2(t,d);try{a=await nv(d,b,i,u,r)}catch(e){if(e instanceof TypeError||e instanceof rl||e instanceof rs)throw e;a=r6(p)}if(void 0!==e.iv)try{o=rt(e.iv)}catch{throw new rl("Failed to base64url decode the iv")}if(void 0!==e.tag)try{s=rt(e.tag)}catch{throw new rl("Failed to base64url decode the tag")}let m=t3.encode(e.protected??"");c=void 0!==e.aad?t4(m,t3.encode("."),t3.encode(e.aad)):m;try{l=rt(e.ciphertext)}catch{throw new rl("Failed to base64url decode the ciphertext")}let y={plaintext:await r9(p,a,l,o,s,c)};if(void 0!==e.protected&&(y.protectedHeader=n),void 0!==e.aad)try{y.additionalAuthenticatedData=rt(e.aad)}catch{throw new rl("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(y.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(y.unprotectedHeader=e.header),g)?{...y,key:b}:y}async function nE(e,t,r){if(e instanceof Uint8Array&&(e=t8.decode(e)),"string"!=typeof e)throw new rl("Compact JWE must be a string or Uint8Array");let{0:n,1:i,2:a,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new rl("Invalid Compact JWE");let l=await n_({ciphertext:o,iv:a||void 0,protected:n,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:l.plaintext,protectedHeader:l.protectedHeader};return"function"==typeof t?{...u,key:l.key}:u}async function nS(e,t,r){let n=await nE(e,t,r),i=function(e,t,r={}){let n,i;try{n=JSON.parse(t8.decode(t))}catch{}if(!rm(n))throw new ru("JWT Claims Set must be a top-level JSON object");let{typ:a}=r;if(a&&("string"!=typeof e.typ||ng(e.typ)!==ng(a)))throw new ri('unexpected "typ" JWT header value',n,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:c,audience:l,maxTokenAge:u}=r,d=[...o];for(let e of(void 0!==u&&d.push("iat"),void 0!==l&&d.push("aud"),void 0!==c&&d.push("sub"),void 0!==s&&d.push("iss"),new Set(d.reverse())))if(!(e in n))throw new ri(`missing required "${e}" claim`,n,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new ri('unexpected "iss" claim value',n,"iss","check_failed");if(c&&n.sub!==c)throw new ri('unexpected "sub" claim value',n,"sub","check_failed");if(l&&!nb(n.aud,"string"==typeof l?[l]:l))throw new ri('unexpected "aud" claim value',n,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=nf(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,f=nd(p||new Date);if((void 0!==n.iat||u)&&"number"!=typeof n.iat)throw new ri('"iat" claim must be a number',n,"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new ri('"nbf" claim must be a number',n,"nbf","invalid");if(n.nbf>f+i)throw new ri('"nbf" claim timestamp check failed',n,"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new ri('"exp" claim must be a number',n,"exp","invalid");if(n.exp<=f-i)throw new ra('"exp" claim timestamp check failed',n,"exp","check_failed")}if(u){let e=f-n.iat;if(e-i>("number"==typeof u?u:nf(u)))throw new ra('"iat" claim timestamp check failed (too far in the past)',n,"iat","check_failed");if(e<0-i)throw new ri('"iat" claim timestamp check failed (it should be in the past)',n,"iat","check_failed")}return n}(n.protectedHeader,n.plaintext,r),{protectedHeader:a}=n;if(void 0!==a.iss&&a.iss!==i.iss)throw new ri('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==a.sub&&a.sub!==i.sub)throw new ri('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==a.aud&&JSON.stringify(a.aud)!==JSON.stringify(i.aud))throw new ri('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let o={payload:i,protectedHeader:a};return"function"==typeof t?{...o,key:n.key}:o}let nk=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,nA=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,nR=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,nT=/^[\u0020-\u003A\u003D-\u007E]*$/,nP=Object.prototype.toString,nC=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function nO(e,t){let r=new nC,n=e.length;if(n<2)return r;let i=t?.decode||nj,a=0;do{let t=e.indexOf("=",a);if(-1===t)break;let o=e.indexOf(";",a),s=-1===o?n:o;if(t>s){a=e.lastIndexOf(";",t-1)+1;continue}let c=nN(e,a,t),l=nI(e,t,c),u=e.slice(c,l);if(void 0===r[u]){let n=nN(e,t+1,s),a=nI(e,s,n),o=i(e.slice(n,a));r[u]=o}a=s+1}while(a<n);return r}function nN(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function nI(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function nU(e,t,r){let n=r?.encode||encodeURIComponent;if(!nk.test(e))throw TypeError(`argument name is invalid: ${e}`);let i=n(t);if(!nA.test(i))throw TypeError(`argument val is invalid: ${t}`);let a=e+"="+i;if(!r)return a;if(void 0!==r.maxAge){if(!Number.isInteger(r.maxAge))throw TypeError(`option maxAge is invalid: ${r.maxAge}`);a+="; Max-Age="+r.maxAge}if(r.domain){if(!nR.test(r.domain))throw TypeError(`option domain is invalid: ${r.domain}`);a+="; Domain="+r.domain}if(r.path){if(!nT.test(r.path))throw TypeError(`option path is invalid: ${r.path}`);a+="; Path="+r.path}if(r.expires){var o;if(o=r.expires,"[object Date]"!==nP.call(o)||!Number.isFinite(r.expires.valueOf()))throw TypeError(`option expires is invalid: ${r.expires}`);a+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.partitioned&&(a+="; Partitioned"),r.priority)switch("string"==typeof r.priority?r.priority.toLowerCase():void 0){case"low":a+="; Priority=Low";break;case"medium":a+="; Priority=Medium";break;case"high":a+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch("string"==typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"none":a+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${r.sameSite}`)}return a}function nj(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}let{q:nD}=c,nM=()=>Date.now()/1e3|0,n$="A256CBC-HS512";async function nL(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:i}=e,a=Array.isArray(r)?r:[r],o=await nW(n$,a[0],i),s=await rk({kty:"oct",k:rr(o)},`sha${o.byteLength<<3}`);return await new ny(t).setProtectedHeader({alg:"dir",enc:n$,kid:s}).setIssuedAt().setExpirationTime(nM()+n).setJti(crypto.randomUUID()).encrypt(o)}async function nH(e){let{token:t,secret:r,salt:n}=e,i=Array.isArray(r)?r:[r];if(!t)return null;let{payload:a}=await nS(t,async({kid:e,enc:t})=>{for(let r of i){let i=await nW(t,r,n);if(void 0===e||e===await rk({kty:"oct",k:rr(i)},`sha${i.byteLength<<3}`))return i}throw Error("no matching decryption secret")},{clockTolerance:15,keyManagementAlgorithms:["dir"],contentEncryptionAlgorithms:[n$,"A256GCM"]});return a}async function nW(e,t,r){let n;switch(e){case"A256CBC-HS512":n=64;break;case"A256GCM":n=32;break;default:throw Error("Unsupported JWT Content Encryption Algorithm")}return await t5("sha256",t,r,`Auth.js Generated Encryption Key (${r})`,n)}async function nK({options:e,paramValue:t,cookieValue:r}){let{url:n,callbacks:i}=e,a=n.origin;return t?a=await i.redirect({url:t,baseUrl:n.origin}):r&&(a=await i.redirect({url:r,baseUrl:n.origin})),{callbackUrl:a,callbackUrlCookie:a!==r?a:void 0}}let nq="\x1b[31m",nB="\x1b[0m",nJ={error(e){let t=e instanceof td?e.type:e.name;if(console.error(`${nq}[auth][error]${nB} ${t}: ${e.message}`),e.cause&&"object"==typeof e.cause&&"err"in e.cause&&e.cause.err instanceof Error){let{err:t,...r}=e.cause;console.error(`${nq}[auth][cause]${nB}:`,t.stack),r&&console.error(`${nq}[auth][details]${nB}:`,JSON.stringify(r,null,2))}else e.stack&&console.error(e.stack.replace(/.*/,"").substring(1))},warn(e){console.warn(`\x1b[33m[auth][warn][${e}]${nB}`,"Read more: https://warnings.authjs.dev")},debug(e,t){console.log(`\x1b[90m[auth][debug]:${nB} ${e}`,JSON.stringify(t,null,2))}};function nV(e){let t={...nJ};return e.debug||(t.debug=()=>{}),e.logger?.error&&(t.error=e.logger.error),e.logger?.warn&&(t.warn=e.logger.warn),e.logger?.debug&&(t.debug=e.logger.debug),e.logger??(e.logger=t),t}let nz=["providers","session","csrf","signin","signout","callback","verify-request","error","webauthn-options"],{q:nF,l:nG}=c;async function nX(e){if(!("body"in e)||!e.body||"POST"!==e.method)return;let t=e.headers.get("content-type");return t?.includes("application/json")?await e.json():t?.includes("application/x-www-form-urlencoded")?Object.fromEntries(new URLSearchParams(await e.text())):void 0}async function nZ(e,t){try{if("GET"!==e.method&&"POST"!==e.method)throw new tU("Only GET and POST requests are supported");t.basePath??(t.basePath="/auth");let r=new URL(e.url),{action:n,providerId:i}=function(e,t){let r=e.match(RegExp(`^${t}(.+)`));if(null===r)throw new tU(`Cannot parse action at ${e}`);let n=r.at(-1).replace(/^\//,"").split("/").filter(Boolean);if(1!==n.length&&2!==n.length)throw new tU(`Cannot parse action at ${e}`);let[i,a]=n;if(!nz.includes(i)||a&&!["signin","callback","webauthn-options"].includes(i))throw new tU(`Cannot parse action at ${e}`);return{action:i,providerId:a}}(r.pathname,t.basePath);return{url:r,action:n,providerId:i,method:e.method,headers:Object.fromEntries(e.headers),body:e.body?await nX(e):void 0,cookies:nF(e.headers.get("cookie")??"")??{},error:r.searchParams.get("error")??void 0,query:Object.fromEntries(r.searchParams)}}catch(n){let r=nV(t);r.error(n),r.debug("request",e)}}function nY(e){let t=new Headers(e.headers);e.cookies?.forEach(e=>{let{name:r,value:n,options:i}=e,a=nG(r,n,i);t.has("Set-Cookie")?t.append("Set-Cookie",a):t.set("Set-Cookie",a)});let r=e.body;"application/json"===t.get("content-type")?r=JSON.stringify(e.body):"application/x-www-form-urlencoded"===t.get("content-type")&&(r=new URLSearchParams(e.body).toString());let n=new Response(r,{headers:t,status:e.redirect?302:e.status??200});return e.redirect&&n.headers.set("Location",e.redirect),n}async function nQ(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").toString()}function n0(e){let t=e=>("0"+e.toString(16)).slice(-2);return Array.from(crypto.getRandomValues(new Uint8Array(e))).reduce((e,r)=>e+t(r),"")}async function n1({options:e,cookieValue:t,isPost:r,bodyValue:n}){if(t){let[i,a]=t.split("|");if(a===await nQ(`${i}${e.secret}`))return{csrfTokenVerified:r&&i===n,csrfToken:i}}let i=n0(32),a=await nQ(`${i}${e.secret}`);return{cookie:`${i}|${a}`,csrfToken:i}}function n2(e,t){if(!t)throw new tL(`CSRF token was missing during an action ${e}`)}function n5(e){return null!==e&&"object"==typeof e}function n6(e,...t){if(!t.length)return e;let r=t.shift();if(n5(e)&&n5(r))for(let t in r)n5(r[t])?(n5(e[t])||(e[t]=Array.isArray(r[t])?[]:{}),n6(e[t],r[t])):void 0!==r[t]&&(e[t]=r[t]);return n6(e,...t)}let n3=Symbol("skip-csrf-check"),n8=Symbol("return-type-raw"),n4=Symbol("custom-fetch"),n9=Symbol("conform-internal"),n7=e=>it({id:e.sub??e.id??crypto.randomUUID(),name:e.name??e.nickname??e.preferred_username,email:e.email,image:e.picture}),ie=e=>it({access_token:e.access_token,id_token:e.id_token,refresh_token:e.refresh_token,expires_at:e.expires_at,scope:e.scope,token_type:e.token_type,session_state:e.session_state});function it(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}function ir(e,t){if(!e&&t)return;if("string"==typeof e)return{url:new URL(e)};let r=new URL(e?.url??"https://authjs.dev");if(e?.params!=null)for(let[t,n]of Object.entries(e.params))"claims"===t&&(n=JSON.stringify(n)),r.searchParams.set(t,String(n));return{url:r,request:e?.request,conform:e?.conform,...e?.clientPrivateKey?{clientPrivateKey:e?.clientPrivateKey}:null}}let ii={signIn:()=>!0,redirect:({url:e,baseUrl:t})=>e.startsWith("/")?`${t}${e}`:new URL(e).origin===t?e:t,session:({session:e})=>({user:{name:e.user?.name,email:e.user?.email,image:e.user?.image},expires:e.expires?.toISOString?.()??e.expires}),jwt:({token:e})=>e};async function ia({authOptions:e,providerId:t,action:r,url:n,cookies:i,callbackUrl:a,csrfToken:o,csrfDisabled:s,isPost:c}){var l,u;let d=nV(e),{providers:p,provider:f}=function(e){let{providerId:t,config:r}=e,n=new URL(r.basePath??"/auth",e.url.origin),i=r.providers.map(e=>{let t="function"==typeof e?e():e,{options:i,...a}=t,o=i?.id??a.id,s=n6(a,i,{signinUrl:`${n}/signin/${o}`,callbackUrl:`${n}/callback/${o}`});if("oauth"===t.type||"oidc"===t.type){s.redirectProxyUrl??(s.redirectProxyUrl=i?.redirectProxyUrl??r.redirectProxyUrl);let e=function(e){e.issuer&&(e.wellKnown??(e.wellKnown=`${e.issuer}/.well-known/openid-configuration`));let t=ir(e.authorization,e.issuer);t&&!t.url?.searchParams.has("scope")&&t.url.searchParams.set("scope","openid profile email");let r=ir(e.token,e.issuer),n=ir(e.userinfo,e.issuer),i=e.checks??["pkce"];return e.redirectProxyUrl&&(i.includes("state")||i.push("state"),e.redirectProxyUrl=`${e.redirectProxyUrl}/callback/${e.id}`),{...e,authorization:t,token:r,checks:i,userinfo:n,profile:e.profile??n7,account:e.account??ie}}(s);return e.authorization?.url.searchParams.get("response_mode")==="form_post"&&delete e.redirectProxyUrl,e[n4]??(e[n4]=i?.[n4]),e}return s}),a=i.find(({id:e})=>e===t);if(t&&!a){let e=i.map(e=>e.id).join(", ");throw Error(`Provider with id "${t}" not found. Available providers: [${e}].`)}return{providers:i,provider:a}}({url:n,providerId:t,config:e}),h=!1;if((f?.type==="oauth"||f?.type==="oidc")&&f.redirectProxyUrl)try{h=new URL(f.redirectProxyUrl).origin===n.origin}catch{throw TypeError(`redirectProxyUrl must be a valid URL. Received: ${f.redirectProxyUrl}`)}let g={debug:!1,pages:{},theme:{colorScheme:"auto",logo:"",brandColor:"",buttonText:""},...e,url:n,action:r,provider:f,cookies:n6(tl(e.useSecureCookies??"https:"===n.protocol),e.cookies),providers:p,session:{strategy:e.adapter?"database":"jwt",maxAge:2592e3,updateAge:86400,generateSessionToken:()=>crypto.randomUUID(),...e.session},jwt:{secret:e.secret,maxAge:e.session?.maxAge??2592e3,encode:nL,decode:nH,...e.jwt},events:(l=e.events??{},u=d,Object.keys(l).reduce((e,t)=>(e[t]=async(...e)=>{try{let r=l[t];return await r(...e)}catch(e){u.error(new tm(e))}},e),{})),adapter:function(e,t){if(e)return Object.keys(e).reduce((r,n)=>(r[n]=async(...r)=>{try{t.debug(`adapter_${n}`,{args:r});let i=e[n];return await i(...r)}catch(r){let e=new tf(r);throw t.error(e),e}},r),{})}(e.adapter,d),callbacks:{...ii,...e.callbacks},logger:d,callbackUrl:n.origin,isOnRedirectProxy:h,experimental:{...e.experimental}},b=[];if(s)g.csrfTokenVerified=!0;else{let{csrfToken:e,cookie:t,csrfTokenVerified:r}=await n1({options:g,cookieValue:i?.[g.cookies.csrfToken.name],isPost:c,bodyValue:o});g.csrfToken=e,g.csrfTokenVerified=r,t&&b.push({name:g.cookies.csrfToken.name,value:t,options:g.cookies.csrfToken.options})}let{callbackUrl:m,callbackUrlCookie:y}=await nK({options:g,cookieValue:i?.[g.cookies.callbackUrl.name],paramValue:a});return g.callbackUrl=m,y&&b.push({name:g.cookies.callbackUrl.name,value:y,options:g.cookies.callbackUrl.options}),{options:g,cookies:b}}var io,is,ic,il,iu,id,ip,ih,ig,ib,im,iy,iw,iv,ix,i_,iE={},iS=[],ik=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,iA=Array.isArray;function iR(e,t){for(var r in t)e[r]=t[r];return e}function iT(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function iP(e,t,r){var n,i,a,o={};for(a in t)"key"==a?n=t[a]:"ref"==a?i=t[a]:o[a]=t[a];if(arguments.length>2&&(o.children=arguments.length>3?ip.call(arguments,2):r),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===o[a]&&(o[a]=e.defaultProps[a]);return iC(e,o,n,i,null)}function iC(e,t,r,n,i){var a={type:e,props:t,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==i?++ig:i,__i:-1,__u:0};return null==i&&null!=ih.vnode&&ih.vnode(a),a}function iO(e){return e.children}function iN(e,t){this.props=e,this.context=t}function iI(e,t){if(null==t)return e.__?iI(e.__,e.__i+1):null;for(var r;t<e.__k.length;t++)if(null!=(r=e.__k[t])&&null!=r.__e)return r.__e;return"function"==typeof e.type?iI(e):null}function iU(e){(!e.__d&&(e.__d=!0)&&ib.push(e)&&!ij.__r++||im!==ih.debounceRendering)&&((im=ih.debounceRendering)||iy)(ij)}function ij(){var e,t,r,n,i,a,o,s;for(ib.sort(iw);e=ib.shift();)e.__d&&(t=ib.length,n=void 0,a=(i=(r=e).__v).__e,o=[],s=[],r.__P&&((n=iR({},i)).__v=i.__v+1,ih.vnode&&ih.vnode(n),iH(r.__P,n,i,r.__n,r.__P.namespaceURI,32&i.__u?[a]:null,o,null==a?iI(i):a,!!(32&i.__u),s),n.__v=i.__v,n.__.__k[n.__i]=n,iW(o,n,s),n.__e!=a&&function e(t){var r,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,r=0;r<t.__k.length;r++)if(null!=(n=t.__k[r])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return e(t)}}(n)),ib.length>t&&ib.sort(iw));ij.__r=0}function iD(e,t,r,n,i,a,o,s,c,l,u){var d,p,f,h,g,b=n&&n.__k||iS,m=t.length;for(r.__d=c,function(e,t,r){var n,i,a,o,s,c=t.length,l=r.length,u=l,d=0;for(e.__k=[],n=0;n<c;n++)null!=(i=t[n])&&"boolean"!=typeof i&&"function"!=typeof i?(o=n+d,(i=e.__k[n]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?iC(null,i,null,null,null):iA(i)?iC(iO,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?iC(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=e,i.__b=e.__b+1,a=null,-1!==(s=i.__i=function(e,t,r,n){var i=e.key,a=e.type,o=r-1,s=r+1,c=t[r];if(null===c||c&&i==c.key&&a===c.type&&0==(131072&c.__u))return r;if(n>+(null!=c&&0==(131072&c.__u)))for(;o>=0||s<t.length;){if(o>=0){if((c=t[o])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return o;o--}if(s<t.length){if((c=t[s])&&0==(131072&c.__u)&&i==c.key&&a===c.type)return s;s++}}return -1}(i,r,o,u))&&(u--,(a=r[s])&&(a.__u|=131072)),null==a||null===a.__v?(-1==s&&d--,"function"!=typeof i.type&&(i.__u|=65536)):s!==o&&(s==o-1?d--:s==o+1?d++:(s>o?d--:d++,i.__u|=65536))):i=e.__k[n]=null;if(u)for(n=0;n<l;n++)null!=(a=r[n])&&0==(131072&a.__u)&&(a.__e==e.__d&&(e.__d=iI(a)),function e(t,r,n){var i,a;if(ih.unmount&&ih.unmount(t),(i=t.ref)&&(i.current&&i.current!==t.__e||iK(i,null,r)),null!=(i=t.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(e){ih.__e(e,r)}i.base=i.__P=null}if(i=t.__k)for(a=0;a<i.length;a++)i[a]&&e(i[a],r,n||"function"!=typeof t.type);n||iT(t.__e),t.__c=t.__=t.__e=t.__d=void 0}(a,a))}(r,t,b),c=r.__d,d=0;d<m;d++)null!=(f=r.__k[d])&&(p=-1===f.__i?iE:b[f.__i]||iE,f.__i=d,iH(e,f,p,i,a,o,s,c,l,u),h=f.__e,f.ref&&p.ref!=f.ref&&(p.ref&&iK(p.ref,null,f),u.push(f.ref,f.__c||h,f)),null==g&&null!=h&&(g=h),65536&f.__u||p.__k===f.__k?c=function e(t,r,n){var i,a;if("function"==typeof t.type){for(i=t.__k,a=0;i&&a<i.length;a++)i[a]&&(i[a].__=t,r=e(i[a],r,n));return r}t.__e!=r&&(r&&t.type&&!n.contains(r)&&(r=iI(t)),n.insertBefore(t.__e,r||null),r=t.__e);do r=r&&r.nextSibling;while(null!=r&&8===r.nodeType);return r}(f,c,e):"function"==typeof f.type&&void 0!==f.__d?c=f.__d:h&&(c=h.nextSibling),f.__d=void 0,f.__u&=-196609);r.__d=c,r.__e=g}function iM(e,t,r){"-"===t[0]?e.setProperty(t,null==r?"":r):e[t]=null==r?"":"number"!=typeof r||ik.test(t)?r:r+"px"}function i$(e,t,r,n,i){var a;t:if("style"===t)if("string"==typeof r)e.style.cssText=r;else{if("string"==typeof n&&(e.style.cssText=n=""),n)for(t in n)r&&t in r||iM(e.style,t,"");if(r)for(t in r)n&&r[t]===n[t]||iM(e.style,t,r[t])}else if("o"===t[0]&&"n"===t[1])a=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+a]=r,r?n?r.u=n.u:(r.u=iv,e.addEventListener(t,a?i_:ix,a)):e.removeEventListener(t,a?i_:ix,a);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==r?"":r;break t}catch(e){}"function"==typeof r||(null==r||!1===r&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==r?"":r))}}function iL(e){return function(t){if(this.l){var r=this.l[t.type+e];if(null==t.t)t.t=iv++;else if(t.t<r.u)return;return r(ih.event?ih.event(t):t)}}}function iH(e,t,r,n,i,a,o,s,c,l){var u,d,p,f,h,g,b,m,y,w,v,x,_,E,S,k,A=t.type;if(void 0!==t.constructor)return null;128&r.__u&&(c=!!(32&r.__u),a=[s=t.__e=r.__e]),(u=ih.__b)&&u(t);t:if("function"==typeof A)try{if(m=t.props,y="prototype"in A&&A.prototype.render,w=(u=A.contextType)&&n[u.__c],v=u?w?w.props.value:u.__:n,r.__c?b=(d=t.__c=r.__c).__=d.__E:(y?t.__c=d=new A(m,v):(t.__c=d=new iN(m,v),d.constructor=A,d.render=iq),w&&w.sub(d),d.props=m,d.state||(d.state={}),d.context=v,d.__n=n,p=d.__d=!0,d.__h=[],d._sb=[]),y&&null==d.__s&&(d.__s=d.state),y&&null!=A.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=iR({},d.__s)),iR(d.__s,A.getDerivedStateFromProps(m,d.__s))),f=d.props,h=d.state,d.__v=t,p)y&&null==A.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),y&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(y&&null==A.getDerivedStateFromProps&&m!==f&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(m,v),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(m,d.__s,v)||t.__v===r.__v)){for(t.__v!==r.__v&&(d.props=m,d.state=d.__s,d.__d=!1),t.__e=r.__e,t.__k=r.__k,t.__k.some(function(e){e&&(e.__=t)}),x=0;x<d._sb.length;x++)d.__h.push(d._sb[x]);d._sb=[],d.__h.length&&o.push(d);break t}null!=d.componentWillUpdate&&d.componentWillUpdate(m,d.__s,v),y&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(f,h,g)})}if(d.context=v,d.props=m,d.__P=e,d.__e=!1,_=ih.__r,E=0,y){for(d.state=d.__s,d.__d=!1,_&&_(t),u=d.render(d.props,d.state,d.context),S=0;S<d._sb.length;S++)d.__h.push(d._sb[S]);d._sb=[]}else do d.__d=!1,_&&_(t),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++E<25);d.state=d.__s,null!=d.getChildContext&&(n=iR(iR({},n),d.getChildContext())),y&&!p&&null!=d.getSnapshotBeforeUpdate&&(g=d.getSnapshotBeforeUpdate(f,h)),iD(e,iA(k=null!=u&&u.type===iO&&null==u.key?u.props.children:u)?k:[k],t,r,n,i,a,o,s,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),b&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=a){for(t.__u|=c?160:128;s&&8===s.nodeType&&s.nextSibling;)s=s.nextSibling;a[a.indexOf(s)]=null,t.__e=s}else t.__e=r.__e,t.__k=r.__k;ih.__e(e,t,r)}else null==a&&t.__v===r.__v?(t.__k=r.__k,t.__e=r.__e):t.__e=function(e,t,r,n,i,a,o,s,c){var l,u,d,p,f,h,g,b=r.props,m=t.props,y=t.type;if("svg"===y?i="http://www.w3.org/2000/svg":"math"===y?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=a){for(l=0;l<a.length;l++)if((f=a[l])&&"setAttribute"in f==!!y&&(y?f.localName===y:3===f.nodeType)){e=f,a[l]=null;break}}if(null==e){if(null===y)return document.createTextNode(m);e=document.createElementNS(i,y,m.is&&m),s&&(ih.__m&&ih.__m(t,a),s=!1),a=null}if(null===y)b===m||s&&e.data===m||(e.data=m);else{if(a=a&&ip.call(e.childNodes),b=r.props||iE,!s&&null!=a)for(b={},l=0;l<e.attributes.length;l++)b[(f=e.attributes[l]).name]=f.value;for(l in b)if(f=b[l],"children"==l);else if("dangerouslySetInnerHTML"==l)d=f;else if(!(l in m)){if("value"==l&&"defaultValue"in m||"checked"==l&&"defaultChecked"in m)continue;i$(e,l,null,f,i)}for(l in m)f=m[l],"children"==l?p=f:"dangerouslySetInnerHTML"==l?u=f:"value"==l?h=f:"checked"==l?g=f:s&&"function"!=typeof f||b[l]===f||i$(e,l,f,b[l],i);if(u)s||d&&(u.__html===d.__html||u.__html===e.innerHTML)||(e.innerHTML=u.__html),t.__k=[];else if(d&&(e.innerHTML=""),iD(e,iA(p)?p:[p],t,r,n,"foreignObject"===y?"http://www.w3.org/1999/xhtml":i,a,o,a?a[0]:r.__k&&iI(r,0),s,c),null!=a)for(l=a.length;l--;)iT(a[l]);s||(l="value","progress"===y&&null==h?e.removeAttribute("value"):void 0===h||h===e[l]&&("progress"!==y||h)&&("option"!==y||h===b[l])||i$(e,l,h,b[l],i),l="checked",void 0!==g&&g!==e[l]&&i$(e,l,g,b[l],i))}return e}(r.__e,t,r,n,i,a,o,c,l);(u=ih.diffed)&&u(t)}function iW(e,t,r){t.__d=void 0;for(var n=0;n<r.length;n++)iK(r[n],r[++n],r[++n]);ih.__c&&ih.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){ih.__e(e,t.__v)}})}function iK(e,t,r){try{if("function"==typeof e){var n="function"==typeof e.__u;n&&e.__u(),n&&null==t||(e.__u=e(t))}else e.current=t}catch(e){ih.__e(e,r)}}function iq(e,t,r){return this.constructor(e,r)}function iB(e,t){var r,n,i,a,o;r=e,ih.__&&ih.__(r,t),i=(n="function"==typeof iB)?null:iB&&iB.__k||t.__k,a=[],o=[],iH(t,r=(!n&&iB||t).__k=iP(iO,null,[r]),i||iE,iE,t.namespaceURI,!n&&iB?[iB]:i?null:t.firstChild?ip.call(t.childNodes):null,a,!n&&iB?iB:i?i.__e:t.firstChild,n,o),iW(a,r,o)}ip=iS.slice,ih={__e:function(e,t,r,n){for(var i,a,o;t=t.__;)if((i=t.__c)&&!i.__)try{if((a=i.constructor)&&null!=a.getDerivedStateFromError&&(i.setState(a.getDerivedStateFromError(e)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,n||{}),o=i.__d),o)return i.__E=i}catch(t){e=t}throw e}},ig=0,iN.prototype.setState=function(e,t){var r;r=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=iR({},this.state),"function"==typeof e&&(e=e(iR({},r),this.props)),e&&iR(r,e),null!=e&&this.__v&&(t&&this._sb.push(t),iU(this))},iN.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),iU(this))},iN.prototype.render=iO,ib=[],iy="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,iw=function(e,t){return e.__v.__b-t.__v.__b},ij.__r=0,iv=0,ix=iL(!1),i_=iL(!0);var iJ=/[\s\n\\/='"\0<>]/,iV=/^(xlink|xmlns|xml)([A-Z])/,iz=/^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/,iF=/^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/,iG=new Set(["draggable","spellcheck"]),iX=/["&<]/;function iZ(e){if(0===e.length||!1===iX.test(e))return e;for(var t=0,r=0,n="",i="";r<e.length;r++){switch(e.charCodeAt(r)){case 34:i="&quot;";break;case 38:i="&amp;";break;case 60:i="&lt;";break;default:continue}r!==t&&(n+=e.slice(t,r)),n+=i,t=r+1}return r!==t&&(n+=e.slice(t,r)),n}var iY={},iQ=new Set(["animation-iteration-count","border-image-outset","border-image-slice","border-image-width","box-flex","box-flex-group","box-ordinal-group","column-count","fill-opacity","flex","flex-grow","flex-negative","flex-order","flex-positive","flex-shrink","flood-opacity","font-weight","grid-column","grid-row","line-clamp","line-height","opacity","order","orphans","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","widows","z-index","zoom"]),i0=/[A-Z]/g;function i1(){this.__d=!0}var i2=null,i5,i6,i3,i8,i4={},i9=[],i7=Array.isArray,ae=Object.assign;function at(e,t){var r,n=e.type,i=!0;return e.__c?(i=!1,(r=e.__c).state=r.__s):r=new n(e.props,t),e.__c=r,r.__v=e,r.props=e.props,r.context=t,r.__d=!0,null==r.state&&(r.state=i4),null==r.__s&&(r.__s=r.state),n.getDerivedStateFromProps?r.state=ae({},r.state,n.getDerivedStateFromProps(r.props,r.state)):i&&r.componentWillMount?(r.componentWillMount(),r.state=r.__s!==r.state?r.__s:r.state):!i&&r.componentWillUpdate&&r.componentWillUpdate(),i3&&i3(e),r.render(r.props,r.state,t)}var ar=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),an=/["&<]/,ai=0;function aa(e,t,r,n,i,a){t||(t={});var o,s,c=t;"ref"in t&&(o=t.ref,delete t.ref);var l={type:e,props:c,key:r,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:--ai,__i:-1,__u:0,__source:i,__self:a};if("function"==typeof e&&(o=e.defaultProps))for(s in o)void 0===c[s]&&(c[s]=o[s]);return ih.vnode&&ih.vnode(l),l}async function ao(e,t){let r=window.SimpleWebAuthnBrowser;async function n(r){let n=new URL(`${e}/webauthn-options/${t}`);r&&n.searchParams.append("action",r),a().forEach(e=>{n.searchParams.append(e.name,e.value)});let i=await fetch(n);return i.ok?i.json():void console.error("Failed to fetch options",i)}function i(){let e=`#${t}-form`,r=document.querySelector(e);if(!r)throw Error(`Form '${e}' not found`);return r}function a(){return Array.from(i().querySelectorAll("input[data-form-field]"))}async function o(e,t){let r=i();if(e){let t=document.createElement("input");t.type="hidden",t.name="action",t.value=e,r.appendChild(t)}if(t){let e=document.createElement("input");e.type="hidden",e.name="data",e.value=JSON.stringify(t),r.appendChild(e)}return r.submit()}async function s(e,t){let n=await r.startAuthentication(e,t);return await o("authenticate",n)}async function c(e){a().forEach(e=>{if(e.required&&!e.value)throw Error(`Missing required field: ${e.name}`)});let t=await r.startRegistration(e);return await o("register",t)}async function l(){if(!r.browserSupportsWebAuthnAutofill())return;let e=await n("authenticate");if(!e)return void console.error("Failed to fetch option for autofill authentication");try{await s(e.options,!0)}catch(e){console.error(e)}}(async function(){let e=i();if(!r.browserSupportsWebAuthn()){e.style.display="none";return}e&&e.addEventListener("submit",async e=>{e.preventDefault();let t=await n(void 0);if(!t)return void console.error("Failed to fetch options for form submission");if("authenticate"===t.action)try{await s(t.options,!1)}catch(e){console.error(e)}else if("register"===t.action)try{await c(t.options)}catch(e){console.error(e)}})})(),l()}let as={default:"Unable to sign in.",Signin:"Try signing in with a different account.",OAuthSignin:"Try signing in with a different account.",OAuthCallbackError:"Try signing in with a different account.",OAuthCreateAccount:"Try signing in with a different account.",EmailCreateAccount:"Try signing in with a different account.",Callback:"Try signing in with a different account.",OAuthAccountNotLinked:"To confirm your identity, sign in with the same account you used originally.",EmailSignin:"The e-mail could not be sent.",CredentialsSignin:"Sign in failed. Check the details you provided are correct.",SessionRequired:"Please sign in to access this page."},ac=`:root {
  --border-width: 1px;
  --border-radius: 0.5rem;
  --color-error: #c94b4b;
  --color-info: #157efb;
  --color-info-hover: #0f6ddb;
  --color-info-text: #fff;
}

.__next-auth-theme-auto,
.__next-auth-theme-light {
  --color-background: #ececec;
  --color-background-hover: rgba(236, 236, 236, 0.8);
  --color-background-card: #fff;
  --color-text: #000;
  --color-primary: #444;
  --color-control-border: #bbb;
  --color-button-active-background: #f9f9f9;
  --color-button-active-border: #aaa;
  --color-separator: #ccc;
  --provider-bg: #fff;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #fff
  );
}

.__next-auth-theme-dark {
  --color-background: #161b22;
  --color-background-hover: rgba(22, 27, 34, 0.8);
  --color-background-card: #0d1117;
  --color-text: #fff;
  --color-primary: #ccc;
  --color-control-border: #555;
  --color-button-active-background: #060606;
  --color-button-active-border: #666;
  --color-separator: #444;
  --provider-bg: #161b22;
  --provider-bg-hover: color-mix(
    in srgb,
    var(--provider-brand-color) 30%,
    #000
  );
}

.__next-auth-theme-dark img[src$="42-school.svg"],
  .__next-auth-theme-dark img[src$="apple.svg"],
  .__next-auth-theme-dark img[src$="boxyhq-saml.svg"],
  .__next-auth-theme-dark img[src$="eveonline.svg"],
  .__next-auth-theme-dark img[src$="github.svg"],
  .__next-auth-theme-dark img[src$="mailchimp.svg"],
  .__next-auth-theme-dark img[src$="medium.svg"],
  .__next-auth-theme-dark img[src$="okta.svg"],
  .__next-auth-theme-dark img[src$="patreon.svg"],
  .__next-auth-theme-dark img[src$="ping-id.svg"],
  .__next-auth-theme-dark img[src$="roblox.svg"],
  .__next-auth-theme-dark img[src$="threads.svg"],
  .__next-auth-theme-dark img[src$="wikimedia.svg"] {
    filter: invert(1);
  }

.__next-auth-theme-dark #submitButton {
    background-color: var(--provider-bg, var(--color-info));
  }

@media (prefers-color-scheme: dark) {
  .__next-auth-theme-auto {
    --color-background: #161b22;
    --color-background-hover: rgba(22, 27, 34, 0.8);
    --color-background-card: #0d1117;
    --color-text: #fff;
    --color-primary: #ccc;
    --color-control-border: #555;
    --color-button-active-background: #060606;
    --color-button-active-border: #666;
    --color-separator: #444;
    --provider-bg: #161b22;
    --provider-bg-hover: color-mix(
      in srgb,
      var(--provider-brand-color) 30%,
      #000
    );
  }
    .__next-auth-theme-auto img[src$="42-school.svg"],
    .__next-auth-theme-auto img[src$="apple.svg"],
    .__next-auth-theme-auto img[src$="boxyhq-saml.svg"],
    .__next-auth-theme-auto img[src$="eveonline.svg"],
    .__next-auth-theme-auto img[src$="github.svg"],
    .__next-auth-theme-auto img[src$="mailchimp.svg"],
    .__next-auth-theme-auto img[src$="medium.svg"],
    .__next-auth-theme-auto img[src$="okta.svg"],
    .__next-auth-theme-auto img[src$="patreon.svg"],
    .__next-auth-theme-auto img[src$="ping-id.svg"],
    .__next-auth-theme-auto img[src$="roblox.svg"],
    .__next-auth-theme-auto img[src$="threads.svg"],
    .__next-auth-theme-auto img[src$="wikimedia.svg"] {
      filter: invert(1);
    }
    .__next-auth-theme-auto #submitButton {
      background-color: var(--provider-bg, var(--color-info));
    }
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  font-family:
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    "Noto Sans",
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    "Segoe UI Symbol",
    "Noto Color Emoji";
}

h1 {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  font-weight: 400;
  color: var(--color-text);
}

p {
  margin-bottom: 1.5rem;
  padding: 0 1rem;
  color: var(--color-text);
}

form {
  margin: 0;
  padding: 0;
}

label {
  font-weight: 500;
  text-align: left;
  margin-bottom: 0.25rem;
  display: block;
  color: var(--color-text);
}

input[type] {
  box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  border: var(--border-width) solid var(--color-control-border);
  background: var(--color-background-card);
  font-size: 1rem;
  border-radius: var(--border-radius);
  color: var(--color-text);
}

p {
  font-size: 1.1rem;
  line-height: 2rem;
}

a.button {
  text-decoration: none;
  line-height: 1rem;
}

a.button:link,
  a.button:visited {
    background-color: var(--color-background);
    color: var(--color-primary);
  }

button,
a.button {
  padding: 0.75rem 1rem;
  color: var(--provider-color, var(--color-primary));
  background-color: var(--provider-bg, var(--color-background));
  border: 1px solid #00000031;
  font-size: 0.9rem;
  height: 50px;
  border-radius: var(--border-radius);
  transition: background-color 250ms ease-in-out;
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

:is(button,a.button):hover {
    background-color: var(--provider-bg-hover, var(--color-background-hover));
    cursor: pointer;
  }

:is(button,a.button):active {
    cursor: pointer;
  }

:is(button,a.button) span {
    color: var(--provider-bg);
  }

#submitButton {
  color: var(--button-text-color, var(--color-info-text));
  background-color: var(--brand-color, var(--color-info));
  width: 100%;
}

#submitButton:hover {
    background-color: var(
      --button-hover-bg,
      var(--color-info-hover)
    ) !important;
  }

a.site {
  color: var(--color-primary);
  text-decoration: none;
  font-size: 1rem;
  line-height: 2rem;
}

a.site:hover {
    text-decoration: underline;
  }

.page {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page > div {
    text-align: center;
  }

.error a.button {
    padding-left: 2rem;
    padding-right: 2rem;
    margin-top: 0.5rem;
  }

.error .message {
    margin-bottom: 1.5rem;
  }

.signin input[type="text"] {
    margin-left: auto;
    margin-right: auto;
    display: block;
  }

.signin hr {
    display: block;
    border: 0;
    border-top: 1px solid var(--color-separator);
    margin: 2rem auto 1rem auto;
    overflow: visible;
  }

.signin hr::before {
      content: "or";
      background: var(--color-background-card);
      color: #888;
      padding: 0 0.4rem;
      position: relative;
      top: -0.7rem;
    }

.signin .error {
    background: #f5f5f5;
    font-weight: 500;
    border-radius: 0.3rem;
    background: var(--color-error);
  }

.signin .error p {
      text-align: left;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      line-height: 1.2rem;
      color: var(--color-info-text);
    }

.signin > div,
  .signin form {
    display: block;
  }

.signin > div input[type], .signin form input[type] {
      margin-bottom: 0.5rem;
    }

.signin > div button, .signin form button {
      width: 100%;
    }

.signin .provider + .provider {
    margin-top: 1rem;
  }

.logo {
  display: inline-block;
  max-width: 150px;
  margin: 1.25rem 0;
  max-height: 70px;
}

.card {
  background-color: var(--color-background-card);
  border-radius: 1rem;
  padding: 1.25rem 2rem;
}

.card .header {
    color: var(--color-primary);
  }

.card input[type]::-moz-placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type]::placeholder {
    color: color-mix(
      in srgb,
      var(--color-text) 20%,
      var(--color-button-active-background)
    );
  }

.card input[type] {
    background: color-mix(in srgb, var(--color-background-card) 95%, black);
  }

.section-header {
  color: var(--color-text);
}

@media screen and (min-width: 450px) {
  .card {
    margin: 2rem 0;
    width: 368px;
  }
}

@media screen and (max-width: 450px) {
  .card {
    margin: 1rem 0;
    width: 343px;
  }
}
`;function al({html:e,title:t,status:r,cookies:n,theme:i,headTags:a}){return{cookies:n,status:r,headers:{"Content-Type":"text/html"},body:`<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width, initial-scale=1.0"><style>${ac}</style><title>${t}</title>${a??""}</head><body class="__next-auth-theme-${i?.colorScheme??"auto"}"><div class="page">${function(e,t,r){var n=ih.__s;ih.__s=!0,i5=ih.__b,i6=ih.diffed,i3=ih.__r,i8=ih.unmount;var i=iP(iO,null);i.__k=[e];try{var a=function e(t,r,n,i,a,o,s){if(null==t||!0===t||!1===t||""===t)return"";var c=typeof t;if("object"!=c)return"function"==c?"":"string"==c?iZ(t):t+"";if(i7(t)){var l,u="";a.__k=t;for(var d=0;d<t.length;d++){var p=t[d];if(null!=p&&"boolean"!=typeof p){var f,h=e(p,r,n,i,a,o,s);"string"==typeof h?u+=h:(l||(l=[]),u&&l.push(u),u="",i7(h)?(f=l).push.apply(f,h):l.push(h))}}return l?(u&&l.push(u),l):u}if(void 0!==t.constructor)return"";t.__=a,i5&&i5(t);var g=t.type,b=t.props;if("function"==typeof g){var m,y,w,v=r;if(g===iO){if("tpl"in b){for(var x="",_=0;_<b.tpl.length;_++)if(x+=b.tpl[_],b.exprs&&_<b.exprs.length){var E=b.exprs[_];if(null==E)continue;"object"==typeof E&&(void 0===E.constructor||i7(E))?x+=e(E,r,n,i,t,o,s):x+=E}return x}if("UNSTABLE_comment"in b)return"\x3c!--"+iZ(b.UNSTABLE_comment)+"--\x3e";y=b.children}else{if(null!=(m=g.contextType)){var S=r[m.__c];v=S?S.props.value:m.__}var k=g.prototype&&"function"==typeof g.prototype.render;if(k)y=at(t,v),w=t.__c;else{t.__c=w={__v:t,context:v,props:t.props,setState:i1,forceUpdate:i1,__d:!0,__h:[]};for(var A=0;w.__d&&A++<25;)w.__d=!1,i3&&i3(t),y=g.call(w,b,v);w.__d=!0}if(null!=w.getChildContext&&(r=ae({},r,w.getChildContext())),k&&ih.errorBoundaries&&(g.getDerivedStateFromError||w.componentDidCatch)){y=null!=y&&y.type===iO&&null==y.key&&null==y.props.tpl?y.props.children:y;try{return e(y,r,n,i,t,o,s)}catch(a){return g.getDerivedStateFromError&&(w.__s=g.getDerivedStateFromError(a)),w.componentDidCatch&&w.componentDidCatch(a,i4),w.__d?(y=at(t,r),null!=(w=t.__c).getChildContext&&(r=ae({},r,w.getChildContext())),e(y=null!=y&&y.type===iO&&null==y.key&&null==y.props.tpl?y.props.children:y,r,n,i,t,o,s)):""}finally{i6&&i6(t),t.__=null,i8&&i8(t)}}}y=null!=y&&y.type===iO&&null==y.key&&null==y.props.tpl?y.props.children:y;try{var R=e(y,r,n,i,t,o,s);return i6&&i6(t),t.__=null,ih.unmount&&ih.unmount(t),R}catch(a){if(!o&&s&&s.onError){var T=s.onError(a,t,function(a){return e(a,r,n,i,t,o,s)});if(void 0!==T)return T;var P=ih.__e;return P&&P(a,t),""}if(!o||!a||"function"!=typeof a.then)throw a;return a.then(function a(){try{return e(y,r,n,i,t,o,s)}catch(c){if(!c||"function"!=typeof c.then)throw c;return c.then(function(){return e(y,r,n,i,t,o,s)},a)}})}}var C,O="<"+g,N="";for(var I in b){var U=b[I];if("function"!=typeof U||"class"===I||"className"===I){switch(I){case"children":C=U;continue;case"key":case"ref":case"__self":case"__source":continue;case"htmlFor":if("for"in b)continue;I="for";break;case"className":if("class"in b)continue;I="class";break;case"defaultChecked":I="checked";break;case"defaultSelected":I="selected";break;case"defaultValue":case"value":switch(I="value",g){case"textarea":C=U;continue;case"select":i=U;continue;case"option":i!=U||"selected"in b||(O+=" selected")}break;case"dangerouslySetInnerHTML":N=U&&U.__html;continue;case"style":"object"==typeof U&&(U=function(e){var t="";for(var r in e){var n=e[r];if(null!=n&&""!==n){var i="-"==r[0]?r:iY[r]||(iY[r]=r.replace(i0,"-$&").toLowerCase()),a=";";"number"!=typeof n||i.startsWith("--")||iQ.has(i)||(a="px;"),t=t+i+":"+n+a}}return t||void 0}(U));break;case"acceptCharset":I="accept-charset";break;case"httpEquiv":I="http-equiv";break;default:if(iV.test(I))I=I.replace(iV,"$1:$2").toLowerCase();else{if(iJ.test(I))continue;("-"===I[4]||iG.has(I))&&null!=U?U+="":n?iF.test(I)&&(I="panose1"===I?"panose-1":I.replace(/([A-Z])/g,"-$1").toLowerCase()):iz.test(I)&&(I=I.toLowerCase())}}null!=U&&!1!==U&&(O=!0===U||""===U?O+" "+I:O+" "+I+'="'+("string"==typeof U?iZ(U):U+"")+'"')}}if(iJ.test(g))throw Error(g+" is not a valid HTML tag name in "+O+">");if(N||("string"==typeof C?N=iZ(C):null!=C&&!1!==C&&!0!==C&&(N=e(C,r,"svg"===g||"foreignObject"!==g&&n,i,t,o,s))),i6&&i6(t),t.__=null,i8&&i8(t),!N&&ar.has(g))return O+"/>";var j="</"+g+">",D=O+">";return i7(N)?[D].concat(N,[j]):"string"!=typeof N?[D,N,j]:D+N+j}(e,i4,!1,void 0,i,!1,void 0);return i7(a)?a.join(""):a}catch(e){if(e.then)throw Error('Use "renderToStringAsync" for suspenseful rendering.');throw e}finally{ih.__c&&ih.__c(e,i9),ih.__s=n,i9.length=0}}(e)}</div></body></html>`}}function au(e){let{url:t,theme:r,query:n,cookies:i,pages:a,providers:o}=e;return{csrf:(e,t,r)=>e?(t.logger.warn("csrf-disabled"),r.push({name:t.cookies.csrfToken.name,value:"",options:{...t.cookies.csrfToken.options,maxAge:0}}),{status:404,cookies:r}):{headers:{"Content-Type":"application/json","Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"},body:{csrfToken:t.csrfToken},cookies:r},providers:e=>({headers:{"Content-Type":"application/json"},body:e.reduce((e,{id:t,name:r,type:n,signinUrl:i,callbackUrl:a})=>(e[t]={id:t,name:r,type:n,signinUrl:i,callbackUrl:a},e),{})}),signin(t,s){if(t)throw new tU("Unsupported action");if(a?.signIn){let t=`${a.signIn}${a.signIn.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:e.callbackUrl??"/"})}`;return s&&(t=`${t}&${new URLSearchParams({error:s})}`),{redirect:t,cookies:i}}let c=o?.find(e=>"webauthn"===e.type&&e.enableConditionalUI&&!!e.simpleWebAuthnBrowserVersion),l="";if(c){let{simpleWebAuthnBrowserVersion:e}=c;l=`<script src="https://unpkg.com/@simplewebauthn/browser@${e}/dist/bundle/index.umd.min.js" crossorigin="anonymous"></script>`}return al({cookies:i,theme:r,html:function(e){let{csrfToken:t,providers:r=[],callbackUrl:n,theme:i,email:a,error:o}=e;"undefined"!=typeof document&&i?.brandColor&&document.documentElement.style.setProperty("--brand-color",i.brandColor),"undefined"!=typeof document&&i?.buttonText&&document.documentElement.style.setProperty("--button-text-color",i.buttonText);let s=o&&(as[o]??as.default),c=r.find(e=>"webauthn"===e.type&&e.enableConditionalUI)?.id;return aa("div",{className:"signin",children:[i?.brandColor&&aa("style",{dangerouslySetInnerHTML:{__html:`:root {--brand-color: ${i.brandColor}}`}}),i?.buttonText&&aa("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${i.buttonText}
        }
      `}}),aa("div",{className:"card",children:[s&&aa("div",{className:"error",children:aa("p",{children:s})}),i?.logo&&aa("img",{src:i.logo,alt:"Logo",className:"logo"}),r.map((e,i)=>{let o,s,c;("oauth"===e.type||"oidc"===e.type)&&({bg:o="#fff",brandColor:s,logo:c=`https://authjs.dev/img/providers/${e.id}.svg`}=e.style??{});let l=s??o??"#fff";return aa("div",{className:"provider",children:["oauth"===e.type||"oidc"===e.type?aa("form",{action:e.signinUrl,method:"POST",children:[aa("input",{type:"hidden",name:"csrfToken",value:t}),n&&aa("input",{type:"hidden",name:"callbackUrl",value:n}),aa("button",{type:"submit",className:"button",style:{"--provider-brand-color":l},tabIndex:0,children:[aa("span",{style:{filter:"invert(1) grayscale(1) brightness(1.3) contrast(9000)","mix-blend-mode":"luminosity",opacity:.95},children:["Sign in with ",e.name]}),c&&aa("img",{loading:"lazy",height:24,src:c})]})]}):null,("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i>0&&"email"!==r[i-1].type&&"credentials"!==r[i-1].type&&"webauthn"!==r[i-1].type&&aa("hr",{}),"email"===e.type&&aa("form",{action:e.signinUrl,method:"POST",children:[aa("input",{type:"hidden",name:"csrfToken",value:t}),aa("label",{className:"section-header",htmlFor:`input-email-for-${e.id}-provider`,children:"Email"}),aa("input",{id:`input-email-for-${e.id}-provider`,autoFocus:!0,type:"email",name:"email",value:a,placeholder:"<EMAIL>",required:!0}),aa("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"credentials"===e.type&&aa("form",{action:e.callbackUrl,method:"POST",children:[aa("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.credentials).map(t=>aa("div",{children:[aa("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.credentials[t].label??t}),aa("input",{name:t,id:`input-${t}-for-${e.id}-provider`,type:e.credentials[t].type??"text",placeholder:e.credentials[t].placeholder??"",...e.credentials[t]})]},`input-group-${e.id}`)),aa("button",{id:"submitButton",type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),"webauthn"===e.type&&aa("form",{action:e.callbackUrl,method:"POST",id:`${e.id}-form`,children:[aa("input",{type:"hidden",name:"csrfToken",value:t}),Object.keys(e.formFields).map(t=>aa("div",{children:[aa("label",{className:"section-header",htmlFor:`input-${t}-for-${e.id}-provider`,children:e.formFields[t].label??t}),aa("input",{name:t,"data-form-field":!0,id:`input-${t}-for-${e.id}-provider`,type:e.formFields[t].type??"text",placeholder:e.formFields[t].placeholder??"",...e.formFields[t]})]},`input-group-${e.id}`)),aa("button",{id:`submitButton-${e.id}`,type:"submit",tabIndex:0,children:["Sign in with ",e.name]})]}),("email"===e.type||"credentials"===e.type||"webauthn"===e.type)&&i+1<r.length&&aa("hr",{})]},e.id)})]}),c&&aa(iO,{children:aa("script",{dangerouslySetInnerHTML:{__html:`
const currentURL = window.location.href;
const authURL = currentURL.substring(0, currentURL.lastIndexOf('/'));
(${ao})(authURL, "${c}");
`}})})]})}({csrfToken:e.csrfToken,providers:e.providers?.filter(e=>["email","oauth","oidc"].includes(e.type)||"credentials"===e.type&&e.credentials||"webauthn"===e.type&&e.formFields||!1),callbackUrl:e.callbackUrl,theme:e.theme,error:s,...n}),title:"Sign In",headTags:l})},signout:()=>a?.signOut?{redirect:a.signOut,cookies:i}:al({cookies:i,theme:r,html:function(e){let{url:t,csrfToken:r,theme:n}=e;return aa("div",{className:"signout",children:[n?.brandColor&&aa("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n.brandColor}
        }
      `}}),n?.buttonText&&aa("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --button-text-color: ${n.buttonText}
        }
      `}}),aa("div",{className:"card",children:[n?.logo&&aa("img",{src:n.logo,alt:"Logo",className:"logo"}),aa("h1",{children:"Signout"}),aa("p",{children:"Are you sure you want to sign out?"}),aa("form",{action:t?.toString(),method:"POST",children:[aa("input",{type:"hidden",name:"csrfToken",value:r}),aa("button",{id:"submitButton",type:"submit",children:"Sign out"})]})]})]})}({csrfToken:e.csrfToken,url:t,theme:r}),title:"Sign Out"}),verifyRequest:e=>a?.verifyRequest?{redirect:`${a.verifyRequest}${t?.search??""}`,cookies:i}:al({cookies:i,theme:r,html:function(e){let{url:t,theme:r}=e;return aa("div",{className:"verify-request",children:[r.brandColor&&aa("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${r.brandColor}
        }
      `}}),aa("div",{className:"card",children:[r.logo&&aa("img",{src:r.logo,alt:"Logo",className:"logo"}),aa("h1",{children:"Check your email"}),aa("p",{children:"A sign in link has been sent to your email address."}),aa("p",{children:aa("a",{className:"site",href:t.origin,children:t.host})})]})]})}({url:t,theme:r,...e}),title:"Verify Request"}),error:e=>a?.error?{redirect:`${a.error}${a.error.includes("?")?"&":"?"}error=${e}`,cookies:i}:al({cookies:i,theme:r,...function(e){let{url:t,error:r="default",theme:n}=e,i=`${t}/signin`,a={default:{status:200,heading:"Error",message:aa("p",{children:aa("a",{className:"site",href:t?.origin,children:t?.host})})},Configuration:{status:500,heading:"Server error",message:aa("div",{children:[aa("p",{children:"There is a problem with the server configuration."}),aa("p",{children:"Check the server logs for more information."})]})},AccessDenied:{status:403,heading:"Access Denied",message:aa("div",{children:[aa("p",{children:"You do not have permission to sign in."}),aa("p",{children:aa("a",{className:"button",href:i,children:"Sign in"})})]})},Verification:{status:403,heading:"Unable to sign in",message:aa("div",{children:[aa("p",{children:"The sign in link is no longer valid."}),aa("p",{children:"It may have been used already or it may have expired."})]}),signin:aa("a",{className:"button",href:i,children:"Sign in"})}},{status:o,heading:s,message:c,signin:l}=a[r]??a.default;return{status:o,html:aa("div",{className:"error",children:[n?.brandColor&&aa("style",{dangerouslySetInnerHTML:{__html:`
        :root {
          --brand-color: ${n?.brandColor}
        }
      `}}),aa("div",{className:"card",children:[n?.logo&&aa("img",{src:n?.logo,alt:"Logo",className:"logo"}),aa("h1",{children:s}),aa("div",{className:"message",children:c}),l]})]})}}({url:t,theme:r,error:e}),title:"Error"})}}function ad(e,t=Date.now()){return new Date(t+1e3*e)}async function ap(e,t,r,n){if(!r?.providerAccountId||!r.type)throw Error("Missing or invalid provider account");if(!["email","oauth","oidc","webauthn"].includes(r.type))throw Error("Provider not supported");let{adapter:i,jwt:a,events:o,session:{strategy:s,generateSessionToken:c}}=n;if(!i)return{user:t,account:r};let l=r,{createUser:u,updateUser:d,getUser:p,getUserByAccount:f,getUserByEmail:h,linkAccount:g,createSession:b,getSessionAndUser:m,deleteSession:y}=i,w=null,v=null,x=!1,_="jwt"===s;if(e)if(_)try{let t=n.cookies.sessionToken.name;(w=await a.decode({...a,token:e,salt:t}))&&"sub"in w&&w.sub&&(v=await p(w.sub))}catch{}else{let t=await m(e);t&&(w=t.session,v=t.user)}if("email"===l.type){let r=await h(t.email);return r?(v?.id!==r.id&&!_&&e&&await y(e),v=await d({id:r.id,emailVerified:new Date}),await o.updateUser?.({user:v})):(v=await u({...t,emailVerified:new Date}),await o.createUser?.({user:v}),x=!0),{session:w=_?{}:await b({sessionToken:c(),userId:v.id,expires:ad(n.session.maxAge)}),user:v,isNewUser:x}}if("webauthn"===l.type){let e=await f({providerAccountId:l.providerAccountId,provider:l.provider});if(e){if(v){if(e.id===v.id){let e={...l,userId:v.id};return{session:w,user:v,isNewUser:x,account:e}}throw new tB("The account is already associated with another user",{provider:l.provider})}w=_?{}:await b({sessionToken:c(),userId:e.id,expires:ad(n.session.maxAge)});let t={...l,userId:e.id};return{session:w,user:e,isNewUser:x,account:t}}{if(v){await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t});let e={...l,userId:v.id};return{session:w,user:v,isNewUser:x,account:e}}if(t.email?await h(t.email):null)throw new tB("Another account already exists with the same e-mail address",{provider:l.provider});v=await u({...t}),await o.createUser?.({user:v}),await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),w=_?{}:await b({sessionToken:c(),userId:v.id,expires:ad(n.session.maxAge)});let e={...l,userId:v.id};return{session:w,user:v,isNewUser:!0,account:e}}}let E=await f({providerAccountId:l.providerAccountId,provider:l.provider});if(E){if(v){if(E.id===v.id)return{session:w,user:v,isNewUser:x};throw new tR("The account is already associated with another user",{provider:l.provider})}return{session:w=_?{}:await b({sessionToken:c(),userId:E.id,expires:ad(n.session.maxAge)}),user:E,isNewUser:x}}{let{provider:e}=n,{type:r,provider:i,providerAccountId:a,userId:s,...d}=l;if(l=Object.assign(e.account(d)??{},{providerAccountId:a,provider:i,type:r,userId:s}),v)return await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),{session:w,user:v,isNewUser:x};let p=t.email?await h(t.email):null;if(p){let e=n.provider;if(e?.allowDangerousEmailAccountLinking)v=p,x=!1;else throw new tR("Another account already exists with the same e-mail address",{provider:l.provider})}else v=await u({...t,emailVerified:null}),x=!0;return await o.createUser?.({user:v}),await g({...l,userId:v.id}),await o.linkAccount?.({user:v,account:l,profile:t}),{session:w=_?{}:await b({sessionToken:c(),userId:v.id,expires:ad(n.session.maxAge)}),user:v,isNewUser:x}}}function af(e,t){if(null==e)return!1;try{return e instanceof t||Object.getPrototypeOf(e)[Symbol.toStringTag]===t.prototype[Symbol.toStringTag]}catch{return!1}}"undefined"!=typeof navigator&&navigator.userAgent?.startsWith?.("Mozilla/5.0 ")||(a="oauth4webapi/v3.5.1");let ah="ERR_INVALID_ARG_VALUE",ag="ERR_INVALID_ARG_TYPE";function ab(e,t,r){let n=TypeError(e,{cause:r});return Object.assign(n,{code:t}),n}let am=Symbol(),ay=Symbol(),aw=Symbol(),av=Symbol(),ax=Symbol(),a_=Symbol(),aE=Symbol(),aS=new TextEncoder,ak=new TextDecoder;function aA(e){return"string"==typeof e?aS.encode(e):ak.decode(e)}function aR(e){if("string"==typeof e)try{let t=atob(e.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"")),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r}catch(e){throw ab("The input to be decoded is not correctly encoded.",ah,e)}var t=e;t instanceof ArrayBuffer&&(t=new Uint8Array(t));let r=[];for(let e=0;e<t.byteLength;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join("")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}class aT extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oM,Error.captureStackTrace?.(this,this.constructor)}}class aP extends Error{code;constructor(e,t){super(e,t),this.name=this.constructor.name,t?.code&&(this.code=t?.code),Error.captureStackTrace?.(this,this.constructor)}}function aC(e,t,r){return new aP(e,{code:t,cause:r})}function aO(e,t){if(!(e instanceof CryptoKey))throw ab(`${t} must be a CryptoKey`,ag)}function aN(e,t){if(aO(e,t),"private"!==e.type)throw ab(`${t} must be a private CryptoKey`,ah)}function aI(e){return!(null===e||"object"!=typeof e||Array.isArray(e))}function aU(e){af(e,Headers)&&(e=Object.fromEntries(e.entries()));let t=new Headers(e);if(a&&!t.has("user-agent")&&t.set("user-agent",a),t.has("authorization"))throw ab('"options.headers" must not include the "authorization" header name',ah);return t}function aj(e){if("function"==typeof e&&(e=e()),!(e instanceof AbortSignal))throw ab('"options.signal" must return or be an instance of AbortSignal',ag);return e}function aD(e){return e.includes("//")?e.replace("//","/"):e}async function aM(e,t,r,n){if(!(e instanceof URL))throw ab(`"${t}" must be an instance of URL`,ag);a5(e,n?.[am]!==!0);let i=r(new URL(e.href)),a=aU(n?.headers);return a.set("accept","application/json"),(n?.[av]||fetch)(i.href,{body:void 0,headers:Object.fromEntries(a.entries()),method:"GET",redirect:"manual",signal:n?.signal?aj(n.signal):void 0})}async function a$(e,t){return aM(e,"issuerIdentifier",e=>{switch(t?.algorithm){case void 0:case"oidc":e.pathname=aD(`${e.pathname}/.well-known/openid-configuration`);break;case"oauth2":var r,n;n=".well-known/oauth-authorization-server","/"===(r=e).pathname?r.pathname=n:r.pathname=aD(`${n}/${r.pathname}`);break;default:throw ab('"options.algorithm" must be "oidc" (default), or "oauth2"',ah)}return e},t)}function aL(e,t,r,n,i){try{if("number"!=typeof e||!Number.isFinite(e))throw ab(`${r} must be a number`,ag,i);if(e>0)return;if(t){if(0!==e)throw ab(`${r} must be a non-negative number`,ah,i);return}throw ab(`${r} must be a positive number`,ah,i)}catch(e){if(n)throw aC(e.message,n,i);throw e}}function aH(e,t,r,n){try{if("string"!=typeof e)throw ab(`${t} must be a string`,ag,n);if(0===e.length)throw ab(`${t} must not be empty`,ah,n)}catch(e){if(r)throw aC(e.message,r,n);throw e}}async function aW(e,t){if(!(e instanceof URL)&&e!==sd)throw ab('"expectedIssuerIdentifier" must be an instance of URL',ag);if(!af(t,Response))throw ab('"response" must be an instance of Response',ag);if(200!==t.status)throw aC('"response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)',oB,t);o0(t);let r=await sl(t);if(aH(r.issuer,'"response" body "issuer" property',oW,{body:r}),e!==sd&&new URL(r.issuer).href!==e.href)throw aC('"response" body "issuer" property does not match the expected value',oG,{expected:e.href,body:r,attribute:"issuer"});return r}function aK(e){var t=e,r="application/json";if(of(t)!==r)throw aq(t,r)}function aq(e,...t){let r='"response" content-type must be ';if(t.length>2){let e=t.pop();r+=`${t.join(", ")}, or ${e}`}else 2===t.length?r+=`${t[0]} or ${t[1]}`:r+=t[0];return aC(r,oq,e)}function aB(){return aR(crypto.getRandomValues(new Uint8Array(32)))}async function aJ(e){return aH(e,"codeVerifier"),aR(await crypto.subtle.digest("SHA-256",aA(e)))}function aV(e){switch(e.algorithm.name){case"RSA-PSS":switch(e.algorithm.hash.name){case"SHA-256":return"PS256";case"SHA-384":return"PS384";case"SHA-512":return"PS512";default:throw new aT("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":switch(e.algorithm.hash.name){case"SHA-256":return"RS256";case"SHA-384":return"RS384";case"SHA-512":return"RS512";default:throw new aT("unsupported RsaHashedKeyAlgorithm hash name",{cause:e})}case"ECDSA":switch(e.algorithm.namedCurve){case"P-256":return"ES256";case"P-384":return"ES384";case"P-521":return"ES512";default:throw new aT("unsupported EcKeyAlgorithm namedCurve",{cause:e})}case"Ed25519":case"EdDSA":return"Ed25519";default:throw new aT("unsupported CryptoKey algorithm name",{cause:e})}}function az(e){let t=e?.[ay];return"number"==typeof t&&Number.isFinite(t)?t:0}function aF(e){let t=e?.[aw];return"number"==typeof t&&Number.isFinite(t)&&-1!==Math.sign(t)?t:30}function aG(){return Math.floor(Date.now()/1e3)}function aX(e){if("object"!=typeof e||null===e)throw ab('"as" must be an object',ag);aH(e.issuer,'"as.issuer"')}function aZ(e){if("object"!=typeof e||null===e)throw ab('"client" must be an object',ag);aH(e.client_id,'"client.client_id"')}function aY(e,t){let r=aG()+az(t);return{jti:aB(),aud:e.issuer,exp:r+60,iat:r,nbf:r,iss:t.client_id,sub:t.client_id}}async function aQ(e,t,r){if(!r.usages.includes("sign"))throw ab('CryptoKey instances used for signing assertions must include "sign" in their "usages"',ah);let n=`${aR(aA(JSON.stringify(e)))}.${aR(aA(JSON.stringify(t)))}`,i=aR(await crypto.subtle.sign(o3(r),r,aA(n)));return`${n}.${i}`}async function a0(e){let{kty:t,e:r,n,x:i,y:a,crv:s}=await crypto.subtle.exportKey("jwk",e),c={kty:t,e:r,n,x:i,y:a,crv:s};return o.set(e,c),c}async function a1(e){return(o||=new WeakMap).get(e)||a0(e)}let a2=URL.parse?(e,t)=>URL.parse(e,t):(e,t)=>{try{return new URL(e,t)}catch{return null}};function a5(e,t){if(t&&"https:"!==e.protocol)throw aC("only requests to HTTPS are allowed",oJ,e);if("https:"!==e.protocol&&"http:"!==e.protocol)throw aC("only HTTP and HTTPS requests are allowed",oV,e)}function a6(e,t,r,n){let i;if("string"!=typeof e||!(i=a2(e)))throw aC(`authorization server metadata does not contain a valid ${r?`"as.mtls_endpoint_aliases.${t}"`:`"as.${t}"`}`,void 0===e?oZ:oY,{attribute:r?`mtls_endpoint_aliases.${t}`:t});return a5(i,n),i}function a3(e,t,r,n){return r&&e.mtls_endpoint_aliases&&t in e.mtls_endpoint_aliases?a6(e.mtls_endpoint_aliases[t],t,r,n):a6(e[t],t,r,n)}class a8 extends Error{cause;code;error;status;error_description;response;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oD,this.cause=t.cause,this.error=t.cause.error,this.status=t.response.status,this.error_description=t.cause.error_description,Object.defineProperty(this,"response",{enumerable:!1,value:t.response}),Error.captureStackTrace?.(this,this.constructor)}}class a4 extends Error{cause;code;error;error_description;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=o$,this.cause=t.cause,this.error=t.cause.get("error"),this.error_description=t.cause.get("error_description")??void 0,Error.captureStackTrace?.(this,this.constructor)}}class a9 extends Error{cause;code;response;status;constructor(e,t){super(e,t),this.name=this.constructor.name,this.code=oj,this.cause=t.cause,this.status=t.response.status,this.response=t.response,Object.defineProperty(this,"response",{enumerable:!1}),Error.captureStackTrace?.(this,this.constructor)}}let a7="[a-zA-Z0-9!#$%&\\'\\*\\+\\-\\.\\^_`\\|~]+",oe=RegExp("^[,\\s]*("+a7+")\\s(.*)"),ot=RegExp("^[,\\s]*("+a7+')\\s*=\\s*"((?:[^"\\\\]|\\\\.)*)"[,\\s]*(.*)'),or=RegExp("^[,\\s]*"+("("+a7+")\\s*=\\s*(")+a7+")[,\\s]*(.*)"),on=RegExp("^([a-zA-Z0-9\\-\\._\\~\\+\\/]+[=]{0,2})(?:$|[,\\s])(.*)");async function oi(e){if(e.status>399&&e.status<500){o0(e),aK(e);try{let t=await e.clone().json();if(aI(t)&&"string"==typeof t.error&&t.error.length)return t}catch{}}}async function oa(e,t,r){if(e.status!==t){let t;if(t=await oi(e))throw await e.body?.cancel(),new a8("server responded with an error in the response body",{cause:t,response:e});throw aC(`"response" is not a conform ${r} response (unexpected HTTP status code)`,oB,e)}}function oo(e){if(!oA.has(e))throw ab('"options.DPoP" is not a valid DPoPHandle',ah)}async function os(e,t,r,n,i,a){if(aH(e,'"accessToken"'),!(r instanceof URL))throw ab('"url" must be an instance of URL',ag);a5(r,a?.[am]!==!0),n=aU(n),a?.DPoP&&(oo(a.DPoP),await a.DPoP.addProof(r,n,t.toUpperCase(),e)),n.set("authorization",`${n.has("dpop")?"DPoP":"Bearer"} ${e}`);let o=await (a?.[av]||fetch)(r.href,{body:i,headers:Object.fromEntries(n.entries()),method:t,redirect:"manual",signal:a?.signal?aj(a.signal):void 0});return a?.DPoP?.cacheNonce(o),o}async function oc(e,t,r,n){aX(e),aZ(t);let i=a3(e,"userinfo_endpoint",t.use_mtls_endpoint_aliases,n?.[am]!==!0),a=aU(n?.headers);return t.userinfo_signed_response_alg?a.set("accept","application/jwt"):(a.set("accept","application/json"),a.append("accept","application/jwt")),os(r,"GET",i,a,null,{...n,[ay]:az(t)})}function ol(e,t,r,n){(s||=new WeakMap).set(e,{jwks:t,uat:r,get age(){return aG()-this.uat}}),n&&Object.assign(n,{jwks:structuredClone(t),uat:r})}function ou(e,t){s?.delete(e),delete t?.jwks,delete t?.uat}async function od(e,t,r){var n;let i,a,o,{alg:c,kid:l}=r;if(function(e){if(!o5(e.alg))throw new aT('unsupported JWS "alg" identifier',{cause:{alg:e.alg}})}(r),!s?.has(e)&&!("object"!=typeof(n=t?.[aE])||null===n||!("uat"in n)||"number"!=typeof n.uat||aG()-n.uat>=300)&&"jwks"in n&&aI(n.jwks)&&Array.isArray(n.jwks.keys)&&Array.prototype.every.call(n.jwks.keys,aI)&&ol(e,t?.[aE].jwks,t?.[aE].uat),s?.has(e)){if({jwks:i,age:a}=s.get(e),a>=300)return ou(e,t?.[aE]),od(e,t,r)}else i=await o1(e,t).then(o2),a=0,ol(e,i,aG(),t?.[aE]);switch(c.slice(0,2)){case"RS":case"PS":o="RSA";break;case"ES":o="EC";break;case"Ed":o="OKP";break;default:throw new aT("unsupported JWS algorithm",{cause:{alg:c}})}let u=i.keys.filter(e=>{if(e.kty!==o||void 0!==l&&l!==e.kid||void 0!==e.alg&&c!==e.alg||void 0!==e.use&&"sig"!==e.use||e.key_ops?.includes("verify")===!1)return!1;switch(!0){case"ES256"===c&&"P-256"!==e.crv:case"ES384"===c&&"P-384"!==e.crv:case"ES512"===c&&"P-521"!==e.crv:case"Ed25519"===c&&"Ed25519"!==e.crv:case"EdDSA"===c&&"Ed25519"!==e.crv:return!1}return!0}),{0:d,length:p}=u;if(!p){if(a>=60)return ou(e,t?.[aE]),od(e,t,r);throw aC("error when selecting a JWT verification key, no applicable keys found",oX,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)})}if(1!==p)throw aC('error when selecting a JWT verification key, multiple applicable keys found, a "kid" JWT Header Parameter is required',oX,{header:r,candidates:u,jwks_uri:new URL(e.jwks_uri)});return ss(c,d)}let op=Symbol();function of(e){return e.headers.get("content-type")?.split(";")[0]}async function oh(e,t,r,n,i){let a;if(aX(e),aZ(t),!af(n,Response))throw ab('"response" must be an instance of Response',ag);if(ox(n),200!==n.status)throw aC('"response" is not a conform UserInfo Endpoint response (unexpected HTTP status code)',oB,n);if(o0(n),"application/jwt"===of(n)){let{claims:r,jwt:o}=await o4(await n.text(),sr.bind(void 0,t.userinfo_signed_response_alg,e.userinfo_signing_alg_values_supported,void 0),az(t),aF(t),i?.[a_]).then(o_.bind(void 0,t.client_id)).then(oS.bind(void 0,e));oy.set(n,o),a=r}else{if(t.userinfo_signed_response_alg)throw aC("JWT UserInfo Response expected",oL,n);a=await sl(n)}if(aH(a.sub,'"response" body "sub" property',oW,{body:a}),r===op);else if(aH(r,'"expectedSubject"'),a.sub!==r)throw aC('unexpected "response" body "sub" property value',oG,{expected:r,body:a,attribute:"sub"});return a}async function og(e,t,r,n,i,a,o){return await r(e,t,i,a),a.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),(o?.[av]||fetch)(n.href,{body:i,headers:Object.fromEntries(a.entries()),method:"POST",redirect:"manual",signal:o?.signal?aj(o.signal):void 0})}async function ob(e,t,r,n,i,a){let o=a3(e,"token_endpoint",t.use_mtls_endpoint_aliases,a?.[am]!==!0);i.set("grant_type",n);let s=aU(a?.headers);s.set("accept","application/json"),a?.DPoP!==void 0&&(oo(a.DPoP),await a.DPoP.addProof(o,s,"POST"));let c=await og(e,t,r,o,i,s,a);return a?.DPoP?.cacheNonce(c),c}let om=new WeakMap,oy=new WeakMap;function ow(e){if(!e.id_token)return;let t=om.get(e);if(!t)throw ab('"ref" was already garbage collected or did not resolve from the proper sources',ah);return t}async function ov(e,t,r,n,i){if(aX(e),aZ(t),!af(r,Response))throw ab('"response" must be an instance of Response',ag);ox(r),await oa(r,200,"Token Endpoint"),o0(r);let a=await sl(r);if(aH(a.access_token,'"response" body "access_token" property',oW,{body:a}),aH(a.token_type,'"response" body "token_type" property',oW,{body:a}),a.token_type=a.token_type.toLowerCase(),"dpop"!==a.token_type&&"bearer"!==a.token_type)throw new aT("unsupported `token_type` value",{cause:{body:a}});if(void 0!==a.expires_in){let e="number"!=typeof a.expires_in?parseFloat(a.expires_in):a.expires_in;aL(e,!1,'"response" body "expires_in" property',oW,{body:a}),a.expires_in=e}if(void 0!==a.refresh_token&&aH(a.refresh_token,'"response" body "refresh_token" property',oW,{body:a}),void 0!==a.scope&&"string"!=typeof a.scope)throw aC('"response" body "scope" property must be a string',oW,{body:a});if(void 0!==a.id_token){aH(a.id_token,'"response" body "id_token" property',oW,{body:a});let o=["aud","exp","iat","iss","sub"];!0===t.require_auth_time&&o.push("auth_time"),void 0!==t.default_max_age&&(aL(t.default_max_age,!1,'"client.default_max_age"'),o.push("auth_time")),n?.length&&o.push(...n);let{claims:s,jwt:c}=await o4(a.id_token,sr.bind(void 0,t.id_token_signed_response_alg,e.id_token_signing_alg_values_supported,"RS256"),az(t),aF(t),i?.[a_]).then(oP.bind(void 0,o)).then(ok.bind(void 0,e)).then(oE.bind(void 0,t.client_id));if(Array.isArray(s.aud)&&1!==s.aud.length){if(void 0===s.azp)throw aC('ID Token "aud" (audience) claim includes additional untrusted audiences',oF,{claims:s,claim:"aud"});if(s.azp!==t.client_id)throw aC('unexpected ID Token "azp" (authorized party) claim value',oF,{expected:t.client_id,claims:s,claim:"azp"})}void 0!==s.auth_time&&aL(s.auth_time,!1,'ID Token "auth_time" (authentication time)',oW,{claims:s}),oy.set(r,c),om.set(a,s)}return a}function ox(e){let t;if(t=function(e){if(!af(e,Response))throw ab('"response" must be an instance of Response',ag);let t=e.headers.get("www-authenticate");if(null===t)return;let r=[],n=t;for(;n;){let e,t=n.match(oe),i=t?.["1"].toLowerCase();if(n=t?.["2"],!i)return;let a={};for(;n;){let r,i;if(t=n.match(ot)){if([,r,i,n]=t,i.includes("\\"))try{i=JSON.parse(`"${i}"`)}catch{}a[r.toLowerCase()]=i;continue}if(t=n.match(or)){[,r,i,n]=t,a[r.toLowerCase()]=i;continue}if(t=n.match(on)){if(Object.keys(a).length)break;[,e,n]=t;break}return}let o={scheme:i,parameters:a};e&&(o.token68=e),r.push(o)}if(r.length)return r}(e))throw new a9("server responded with a challenge in the WWW-Authenticate HTTP Header",{cause:t,response:e})}function o_(e,t){return void 0!==t.claims.aud?oE(e,t):t}function oE(e,t){if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e))throw aC('unexpected JWT "aud" (audience) claim value',oF,{expected:e,claims:t.claims,claim:"aud"})}else if(t.claims.aud!==e)throw aC('unexpected JWT "aud" (audience) claim value',oF,{expected:e,claims:t.claims,claim:"aud"});return t}function oS(e,t){return void 0!==t.claims.iss?ok(e,t):t}function ok(e,t){let r=e[sp]?.(t)??e.issuer;if(t.claims.iss!==r)throw aC('unexpected JWT "iss" (issuer) claim value',oF,{expected:r,claims:t.claims,claim:"iss"});return t}let oA=new WeakSet;async function oR(e,t,r,n,i,a,o){if(aX(e),aZ(t),!oA.has(n))throw ab('"callbackParameters" must be an instance of URLSearchParams obtained from "validateAuthResponse()", or "validateJwtAuthResponse()',ah);aH(i,'"redirectUri"');let s=sn(n,"code");if(!s)throw aC('no authorization code in "callbackParameters"',oW);let c=new URLSearchParams(o?.additionalParameters);return c.set("redirect_uri",i),c.set("code",s),a!==su&&(aH(a,'"codeVerifier"'),c.set("code_verifier",a)),ob(e,t,r,"authorization_code",c,o)}let oT={aud:"audience",c_hash:"code hash",client_id:"client id",exp:"expiration time",iat:"issued at",iss:"issuer",jti:"jwt id",nonce:"nonce",s_hash:"state hash",sub:"subject",ath:"access token hash",htm:"http method",htu:"http uri",cnf:"confirmation",auth_time:"authentication time"};function oP(e,t){for(let r of e)if(void 0===t.claims[r])throw aC(`JWT "${r}" (${oT[r]}) claim missing`,oW,{claims:t.claims});return t}let oC=Symbol(),oO=Symbol();async function oN(e,t,r,n){return"string"==typeof n?.expectedNonce||"number"==typeof n?.maxAge||n?.requireIdToken?oI(e,t,r,n.expectedNonce,n.maxAge,{[a_]:n[a_]}):oU(e,t,r,n)}async function oI(e,t,r,n,i,a){let o=[];switch(n){case void 0:n=oC;break;case oC:break;default:aH(n,'"expectedNonce" argument'),o.push("nonce")}switch(i??=t.default_max_age){case void 0:i=oO;break;case oO:break;default:aL(i,!1,'"maxAge" argument'),o.push("auth_time")}let s=await ov(e,t,r,o,a);aH(s.id_token,'"response" body "id_token" property',oW,{body:s});let c=ow(s);if(i!==oO){let e=aG()+az(t),r=aF(t);if(c.auth_time+i<e-r)throw aC("too much time has elapsed since the last End-User authentication",oz,{claims:c,now:e,tolerance:r,claim:"auth_time"})}if(n===oC){if(void 0!==c.nonce)throw aC('unexpected ID Token "nonce" claim value',oF,{expected:void 0,claims:c,claim:"nonce"})}else if(c.nonce!==n)throw aC('unexpected ID Token "nonce" claim value',oF,{expected:n,claims:c,claim:"nonce"});return s}async function oU(e,t,r,n){let i=await ov(e,t,r,void 0,n),a=ow(i);if(a){if(void 0!==t.default_max_age){aL(t.default_max_age,!1,'"client.default_max_age"');let e=aG()+az(t),r=aF(t);if(a.auth_time+t.default_max_age<e-r)throw aC("too much time has elapsed since the last End-User authentication",oz,{claims:a,now:e,tolerance:r,claim:"auth_time"})}if(void 0!==a.nonce)throw aC('unexpected ID Token "nonce" claim value',oF,{expected:void 0,claims:a,claim:"nonce"})}return i}let oj="OAUTH_WWW_AUTHENTICATE_CHALLENGE",oD="OAUTH_RESPONSE_BODY_ERROR",oM="OAUTH_UNSUPPORTED_OPERATION",o$="OAUTH_AUTHORIZATION_RESPONSE_ERROR",oL="OAUTH_JWT_USERINFO_EXPECTED",oH="OAUTH_PARSE_ERROR",oW="OAUTH_INVALID_RESPONSE",oK="OAUTH_INVALID_REQUEST",oq="OAUTH_RESPONSE_IS_NOT_JSON",oB="OAUTH_RESPONSE_IS_NOT_CONFORM",oJ="OAUTH_HTTP_REQUEST_FORBIDDEN",oV="OAUTH_REQUEST_PROTOCOL_FORBIDDEN",oz="OAUTH_JWT_TIMESTAMP_CHECK_FAILED",oF="OAUTH_JWT_CLAIM_COMPARISON_FAILED",oG="OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED",oX="OAUTH_KEY_SELECTION_FAILED",oZ="OAUTH_MISSING_SERVER_METADATA",oY="OAUTH_INVALID_SERVER_METADATA";function oQ(e,t){if("string"!=typeof t.header.typ||t.header.typ.toLowerCase().replace(/^application\//,"")!==e)throw aC('unexpected JWT "typ" header parameter value',oW,{header:t.header});return t}function o0(e){if(e.bodyUsed)throw ab('"response" body has been used already',ah)}async function o1(e,t){aX(e);let r=a3(e,"jwks_uri",!1,t?.[am]!==!0),n=aU(t?.headers);return n.set("accept","application/json"),n.append("accept","application/jwk-set+json"),(t?.[av]||fetch)(r.href,{body:void 0,headers:Object.fromEntries(n.entries()),method:"GET",redirect:"manual",signal:t?.signal?aj(t.signal):void 0})}async function o2(e){if(!af(e,Response))throw ab('"response" must be an instance of Response',ag);if(200!==e.status)throw aC('"response" is not a conform JSON Web Key Set response (unexpected HTTP status code)',oB,e);o0(e);let t=await sl(e,e=>(function(e,...t){if(!t.includes(of(e)))throw aq(e,...t)})(e,"application/json","application/jwk-set+json"));if(!Array.isArray(t.keys))throw aC('"response" body "keys" property must be an array',oW,{body:t});if(!Array.prototype.every.call(t.keys,aI))throw aC('"response" body "keys" property members must be JWK formatted objects',oW,{body:t});return t}function o5(e){switch(e){case"PS256":case"ES256":case"RS256":case"PS384":case"ES384":case"RS384":case"PS512":case"ES512":case"RS512":case"Ed25519":case"EdDSA":return!0;default:return!1}}function o6(e){let{algorithm:t}=e;if("number"!=typeof t.modulusLength||t.modulusLength<2048)throw new aT(`unsupported ${t.name} modulusLength`,{cause:e})}function o3(e){switch(e.algorithm.name){case"ECDSA":return{name:e.algorithm.name,hash:function(e){let{algorithm:t}=e;switch(t.namedCurve){case"P-256":return"SHA-256";case"P-384":return"SHA-384";case"P-521":return"SHA-512";default:throw new aT("unsupported ECDSA namedCurve",{cause:e})}}(e)};case"RSA-PSS":switch(o6(e),e.algorithm.hash.name){case"SHA-256":case"SHA-384":case"SHA-512":return{name:e.algorithm.name,saltLength:parseInt(e.algorithm.hash.name.slice(-3),10)>>3};default:throw new aT("unsupported RSA-PSS hash name",{cause:e})}case"RSASSA-PKCS1-v1_5":return o6(e),e.algorithm.name;case"Ed25519":return e.algorithm.name}throw new aT("unsupported CryptoKey algorithm name",{cause:e})}async function o8(e,t,r,n){let i=aA(`${e}.${t}`),a=o3(r);if(!await crypto.subtle.verify(a,r,n,i))throw aC("JWT signature verification failed",oW,{key:r,data:i,signature:n,algorithm:a})}async function o4(e,t,r,n,i){let a,o,{0:s,1:c,length:l}=e.split(".");if(5===l)if(void 0!==i)e=await i(e),{0:s,1:c,length:l}=e.split(".");else throw new aT("JWE decryption is not configured",{cause:e});if(3!==l)throw aC("Invalid JWT",oW,e);try{a=JSON.parse(aA(aR(s)))}catch(e){throw aC("failed to parse JWT Header body as base64url encoded JSON",oH,e)}if(!aI(a))throw aC("JWT Header must be a top level object",oW,e);if(t(a),void 0!==a.crit)throw new aT('no JWT "crit" header parameter extensions are supported',{cause:{header:a}});try{o=JSON.parse(aA(aR(c)))}catch(e){throw aC("failed to parse JWT Payload body as base64url encoded JSON",oH,e)}if(!aI(o))throw aC("JWT Payload must be a top level object",oW,e);let u=aG()+r;if(void 0!==o.exp){if("number"!=typeof o.exp)throw aC('unexpected JWT "exp" (expiration time) claim type',oW,{claims:o});if(o.exp<=u-n)throw aC('unexpected JWT "exp" (expiration time) claim value, expiration is past current timestamp',oz,{claims:o,now:u,tolerance:n,claim:"exp"})}if(void 0!==o.iat&&"number"!=typeof o.iat)throw aC('unexpected JWT "iat" (issued at) claim type',oW,{claims:o});if(void 0!==o.iss&&"string"!=typeof o.iss)throw aC('unexpected JWT "iss" (issuer) claim type',oW,{claims:o});if(void 0!==o.nbf){if("number"!=typeof o.nbf)throw aC('unexpected JWT "nbf" (not before) claim type',oW,{claims:o});if(o.nbf>u+n)throw aC('unexpected JWT "nbf" (not before) claim value',oz,{claims:o,now:u,tolerance:n,claim:"nbf"})}if(void 0!==o.aud&&"string"!=typeof o.aud&&!Array.isArray(o.aud))throw aC('unexpected JWT "aud" (audience) claim type',oW,{claims:o});return{header:a,claims:o,jwt:e}}async function o9(e,t,r){let n;switch(t.alg){case"RS256":case"PS256":case"ES256":n="SHA-256";break;case"RS384":case"PS384":case"ES384":n="SHA-384";break;case"RS512":case"PS512":case"ES512":case"Ed25519":case"EdDSA":n="SHA-512";break;default:throw new aT(`unsupported JWS algorithm for ${r} calculation`,{cause:{alg:t.alg}})}let i=await crypto.subtle.digest(n,aA(e));return aR(i.slice(0,i.byteLength/2))}async function o7(e,t,r,n){return t===await o9(e,r,n)}async function se(e){if(e.bodyUsed)throw ab("form_post Request instances must contain a readable body",ah,{cause:e});return e.text()}async function st(e){if("POST"!==e.method)throw ab("form_post responses are expected to use the POST method",ah,{cause:e});if("application/x-www-form-urlencoded"!==of(e))throw ab("form_post responses are expected to use the application/x-www-form-urlencoded content-type",ah,{cause:e});return se(e)}function sr(e,t,r,n){if(void 0!==e){if("string"==typeof e?n.alg!==e:!e.includes(n.alg))throw aC('unexpected JWT "alg" header parameter',oW,{header:n,expected:e,reason:"client configuration"});return}if(Array.isArray(t)){if(!t.includes(n.alg))throw aC('unexpected JWT "alg" header parameter',oW,{header:n,expected:t,reason:"authorization server metadata"});return}if(void 0!==r){if("string"==typeof r?n.alg!==r:"function"==typeof r?!r(n.alg):!r.includes(n.alg))throw aC('unexpected JWT "alg" header parameter',oW,{header:n,expected:r,reason:"default value"});return}throw aC('missing client or server configuration to verify used JWT "alg" header parameter',void 0,{client:e,issuer:t,fallback:r})}function sn(e,t){let{0:r,length:n}=e.getAll(t);if(n>1)throw aC(`"${t}" parameter must be provided only once`,oW);return r}let si=Symbol(),sa=Symbol();function so(e,t,r,n){var i;if(aX(e),aZ(t),r instanceof URL&&(r=r.searchParams),!(r instanceof URLSearchParams))throw ab('"parameters" must be an instance of URLSearchParams, or URL',ag);if(sn(r,"response"))throw aC('"parameters" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()',oW,{parameters:r});let a=sn(r,"iss"),o=sn(r,"state");if(!a&&e.authorization_response_iss_parameter_supported)throw aC('response parameter "iss" (issuer) missing',oW,{parameters:r});if(a&&a!==e.issuer)throw aC('unexpected "iss" (issuer) response parameter value',oW,{expected:e.issuer,parameters:r});switch(n){case void 0:case sa:if(void 0!==o)throw aC('unexpected "state" response parameter encountered',oW,{expected:void 0,parameters:r});break;case si:break;default:if(aH(n,'"expectedState" argument'),o!==n)throw aC(void 0===o?'response parameter "state" missing':'unexpected "state" response parameter value',oW,{expected:n,parameters:r})}if(sn(r,"error"))throw new a4("authorization response from the server is an error",{cause:r});let s=sn(r,"id_token"),c=sn(r,"token");if(void 0!==s||void 0!==c)throw new aT("implicit and hybrid flows are not supported");return i=new URLSearchParams(r),oA.add(i),i}async function ss(e,t){let{ext:r,key_ops:n,use:i,...a}=t;return crypto.subtle.importKey("jwk",a,function(e){switch(e){case"PS256":case"PS384":case"PS512":return{name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`};case"RS256":case"RS384":case"RS512":return{name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`};case"ES256":case"ES384":return{name:"ECDSA",namedCurve:`P-${e.slice(-3)}`};case"ES512":return{name:"ECDSA",namedCurve:"P-521"};case"Ed25519":case"EdDSA":return"Ed25519";default:throw new aT("unsupported JWS algorithm",{cause:{alg:e}})}}(e),!0,["verify"])}function sc(e){let t=new URL(e);return t.search="",t.hash="",t.href}async function sl(e,t=aK){let r;try{r=await e.json()}catch(r){throw t(e),aC('failed to parse "response" body as JSON',oH,r)}if(!aI(r))throw aC('"response" body must be a top level object',oW,{body:r});return r}let su=Symbol(),sd=Symbol(),sp=Symbol();async function sf(e,t,r){let{cookies:n,logger:i}=r,a=n[e],o=new Date;o.setTime(o.getTime()+9e5),i.debug(`CREATE_${e.toUpperCase()}`,{name:a.name,payload:t,COOKIE_TTL:900,expires:o});let s=await nL({...r.jwt,maxAge:900,token:{value:t},salt:a.name}),c={...a.options,expires:o};return{name:a.name,value:s,options:c}}async function sh(e,t,r){try{let{logger:n,cookies:i,jwt:a}=r;if(n.debug(`PARSE_${e.toUpperCase()}`,{cookie:t}),!t)throw new tx(`${e} cookie was missing`);let o=await nH({...a,token:t,salt:i[e].name});if(o?.value)return o.value;throw Error("Invalid cookie")}catch(t){throw new tx(`${e} value could not be parsed`,{cause:t})}}function sg(e,t,r){let{logger:n,cookies:i}=t,a=i[e];n.debug(`CLEAR_${e.toUpperCase()}`,{cookie:a}),r.push({name:a.name,value:"",options:{...i[e].options,maxAge:0}})}function sb(e,t){return async function(r,n,i){let{provider:a,logger:o}=i;if(!a?.checks?.includes(e))return;let s=r?.[i.cookies[t].name];o.debug(`USE_${t.toUpperCase()}`,{value:s});let c=await sh(t,s,i);return sg(t,i,n),c}}let sm={async create(e){let t=aB(),r=await aJ(t);return{cookie:await sf("pkceCodeVerifier",t,e),value:r}},use:sb("pkce","pkceCodeVerifier")},sy="encodedState",sw={async create(e,t){let{provider:r}=e;if(!r.checks.includes("state")){if(t)throw new tx("State data was provided but the provider is not configured to use state");return}let n={origin:t,random:aB()},i=await nL({secret:e.jwt.secret,token:n,salt:sy,maxAge:900});return{cookie:await sf("state",i,e),value:i}},use:sb("state","state"),async decode(e,t){try{t.logger.debug("DECODE_STATE",{state:e});let r=await nH({secret:t.jwt.secret,token:e,salt:sy});if(r)return r;throw Error("Invalid state")}catch(e){throw new tx("State could not be decoded",{cause:e})}}},sv={async create(e){if(!e.provider.checks.includes("nonce"))return;let t=aB();return{cookie:await sf("nonce",t,e),value:t}},use:sb("nonce","nonce")},sx="encodedWebauthnChallenge",s_={create:async(e,t,r)=>({cookie:await sf("webauthnChallenge",await nL({secret:e.jwt.secret,token:{challenge:t,registerData:r},salt:sx,maxAge:900}),e)}),async use(e,t,r){let n=t?.[e.cookies.webauthnChallenge.name],i=await sh("webauthnChallenge",n,e),a=await nH({secret:e.jwt.secret,token:i,salt:sx});if(sg("webauthnChallenge",e,r),!a)throw new tx("WebAuthn challenge was missing");return a}};function sE(e){return encodeURIComponent(e).replace(/%20/g,"+")}async function sS(e,t,r){let n,i,a,{logger:o,provider:s}=r,{token:c,userinfo:l}=s;if(c?.url&&"authjs.dev"!==c.url.host||l?.url&&"authjs.dev"!==l.url.host)n={issuer:s.issuer??"https://authjs.dev",token_endpoint:c?.url.toString(),userinfo_endpoint:l?.url.toString()};else{let e=new URL(s.issuer),t=await a$(e,{[am]:!0,[av]:s[n4]});if(!(n=await aW(e,t)).token_endpoint)throw TypeError("TODO: Authorization server did not provide a token endpoint.");if(!n.userinfo_endpoint)throw TypeError("TODO: Authorization server did not provide a userinfo endpoint.")}let u={client_id:s.clientId,...s.client};switch(u.token_endpoint_auth_method){case void 0:case"client_secret_basic":i=(e,t,r,n)=>{n.set("authorization",function(e,t){let r=sE(e),n=sE(t),i=btoa(`${r}:${n}`);return`Basic ${i}`}(s.clientId,s.clientSecret))};break;case"client_secret_post":var d;aH(d=s.clientSecret,'"clientSecret"'),i=(e,t,r,n)=>{r.set("client_id",t.client_id),r.set("client_secret",d)};break;case"client_secret_jwt":i=function(e,t){let r;aH(e,'"clientSecret"');let n=void 0;return async(t,i,a,o)=>{r||=await crypto.subtle.importKey("raw",aA(e),{hash:"SHA-256",name:"HMAC"},!1,["sign"]);let s={alg:"HS256"},c=aY(t,i);n?.(s,c);let l=`${aR(aA(JSON.stringify(s)))}.${aR(aA(JSON.stringify(c)))}`,u=await crypto.subtle.sign(r.algorithm,r,aA(l));a.set("client_id",i.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",`${l}.${aR(new Uint8Array(u))}`)}}(s.clientSecret);break;case"private_key_jwt":i=function(e,t){var r;let{key:n,kid:i}=(r=e)instanceof CryptoKey?{key:r}:r?.key instanceof CryptoKey?(void 0!==r.kid&&aH(r.kid,'"kid"'),{key:r.key,kid:r.kid}):{};return aN(n,'"clientPrivateKey.key"'),async(e,r,a,o)=>{let s={alg:aV(n),kid:i},c=aY(e,r);t?.[ax]?.(s,c),a.set("client_id",r.client_id),a.set("client_assertion_type","urn:ietf:params:oauth:client-assertion-type:jwt-bearer"),a.set("client_assertion",await aQ(s,c,n))}}(s.token.clientPrivateKey,{[ax](e,t){t.aud=[n.issuer,n.token_endpoint]}});break;case"none":i=(e,t,r,n)=>{r.set("client_id",t.client_id)};break;default:throw Error("unsupported client authentication method")}let p=[],f=await sw.use(t,p,r);try{a=so(n,u,new URLSearchParams(e),s.checks.includes("state")?f:si)}catch(e){if(e instanceof a4){let t={providerId:s.id,...Object.fromEntries(e.cause.entries())};throw o.debug("OAuthCallbackError",t),new tT("OAuth Provider returned an error",t)}throw e}let h=await sm.use(t,p,r),g=s.callbackUrl;!r.isOnRedirectProxy&&s.redirectProxyUrl&&(g=s.redirectProxyUrl);let b=await oR(n,u,i,a,g,h??"decoy",{[am]:!0,[av]:(...e)=>(s.checks.includes("pkce")||e[1].body.delete("code_verifier"),(s[n4]??fetch)(...e))});s.token?.conform&&(b=await s.token.conform(b.clone())??b);let m={},y="oidc"===s.type;if(s[n9])switch(s.id){case"microsoft-entra-id":case"azure-ad":{let e=await b.clone().json();if(e.error){let t={providerId:s.id,...e};throw new tT(`OAuth Provider returned an error: ${e.error}`,t)}let{tid:t}=function(e){let t,r;if("string"!=typeof e)throw new ru("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:i}=e.split(".");if(5===i)throw new ru("Only JWTs using Compact JWS serialization can be decoded");if(3!==i)throw new ru("Invalid JWT");if(!n)throw new ru("JWTs must contain a payload");try{t=rt(n)}catch{throw new ru("Failed to base64url decode the payload")}try{r=JSON.parse(t8.decode(t))}catch{throw new ru("Failed to parse the decoded payload as JSON")}if(!rm(r))throw new ru("Invalid JWT Claims Set");return r}(e.id_token);if("string"==typeof t){let e=n.issuer?.match(/microsoftonline\.com\/(\w+)\/v2\.0/)?.[1]??"common",r=new URL(n.issuer.replace(e,t)),i=await a$(r,{[av]:s[n4]});n=await aW(r,i)}}}let w=await oN(n,u,b,{expectedNonce:await sv.use(t,p,r),requireIdToken:y});if(y){let t=ow(w);if(m=t,s[n9]&&"apple"===s.id)try{m.user=JSON.parse(e?.user)}catch{}if(!1===s.idToken){let e=await oc(n,u,w.access_token,{[av]:s[n4],[am]:!0});m=await oh(n,u,t.sub,e)}}else if(l?.request){let e=await l.request({tokens:w,provider:s});e instanceof Object&&(m=e)}else if(l?.url){let e=await oc(n,u,w.access_token,{[av]:s[n4],[am]:!0});m=await e.json()}else throw TypeError("No userinfo endpoint configured");return w.expires_in&&(w.expires_at=Math.floor(Date.now()/1e3)+Number(w.expires_in)),{...await sk(m,s,w,o),profile:m,cookies:p}}async function sk(e,t,r,n){try{let n=await t.profile(e,r);return{user:{...n,id:crypto.randomUUID(),email:n.email?.toLowerCase()},account:{...r,provider:t.id,type:t.type,providerAccountId:n.id??crypto.randomUUID()}}}catch(r){n.debug("getProfile error details",e),n.error(new tP(r,{provider:t.id}))}}var sA=r(356).Buffer;async function sR(e,t,r,n){let i=await sN(e,t,r),{cookie:a}=await s_.create(e,i.challenge,r);return{status:200,cookies:[...n??[],a],body:{action:"register",options:i},headers:{"Content-Type":"application/json"}}}async function sT(e,t,r,n){let i=await sO(e,t,r),{cookie:a}=await s_.create(e,i.challenge);return{status:200,cookies:[...n??[],a],body:{action:"authenticate",options:i},headers:{"Content-Type":"application/json"}}}async function sP(e,t,r){let n,{adapter:i,provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new td("Invalid WebAuthn Authentication response");let s=sj(sU(o.id)),c=await i.getAuthenticator(s);if(!c)throw new td(`WebAuthn authenticator not found in database: ${JSON.stringify({credentialID:s})}`);let{challenge:l}=await s_.use(e,t.cookies,r);try{var u;let r=a.getRelayingParty(e,t);n=await a.simpleWebAuthn.verifyAuthenticationResponse({...a.verifyAuthenticationOptions,expectedChallenge:l,response:o,authenticator:{...u=c,credentialDeviceType:u.credentialDeviceType,transports:sD(u.transports),credentialID:sU(u.credentialID),credentialPublicKey:sU(u.credentialPublicKey)},expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new tq(e)}let{verified:d,authenticationInfo:p}=n;if(!d)throw new tq("WebAuthn authentication response could not be verified");try{let{newCounter:e}=p;await i.updateAuthenticatorCounter(c.credentialID,e)}catch(e){throw new tf(`Failed to update authenticator counter. This may cause future authentication attempts to fail. ${JSON.stringify({credentialID:s,oldCounter:c.counter,newCounter:p.newCounter})}`,e)}let f=await i.getAccount(c.providerAccountId,a.id);if(!f)throw new td(`WebAuthn account not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId})}`);let h=await i.getUser(f.userId);if(!h)throw new td(`WebAuthn user not found in database: ${JSON.stringify({credentialID:s,providerAccountId:c.providerAccountId,userID:f.userId})}`);return{account:f,user:h}}async function sC(e,t,r){var n;let i,{provider:a}=e,o=t.body&&"string"==typeof t.body.data?JSON.parse(t.body.data):void 0;if(!o||"object"!=typeof o||!("id"in o)||"string"!=typeof o.id)throw new td("Invalid WebAuthn Registration response");let{challenge:s,registerData:c}=await s_.use(e,t.cookies,r);if(!c)throw new td("Missing user registration data in WebAuthn challenge cookie");try{let r=a.getRelayingParty(e,t);i=await a.simpleWebAuthn.verifyRegistrationResponse({...a.verifyRegistrationOptions,expectedChallenge:s,response:o,expectedOrigin:r.origin,expectedRPID:r.id})}catch(e){throw new tq(e)}if(!i.verified||!i.registrationInfo)throw new tq("WebAuthn registration response could not be verified");let l={providerAccountId:sj(i.registrationInfo.credentialID),provider:e.provider.id,type:a.type},u={providerAccountId:l.providerAccountId,counter:i.registrationInfo.counter,credentialID:sj(i.registrationInfo.credentialID),credentialPublicKey:sj(i.registrationInfo.credentialPublicKey),credentialBackedUp:i.registrationInfo.credentialBackedUp,credentialDeviceType:i.registrationInfo.credentialDeviceType,transports:(n=o.response.transports,n?.join(","))};return{user:c,account:l,authenticator:u}}async function sO(e,t,r){let{provider:n,adapter:i}=e,a=r&&r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateAuthenticationOptions({...n.authenticationOptions,rpID:o.id,allowCredentials:a?.map(e=>({id:sU(e.credentialID),type:"public-key",transports:sD(e.transports)}))})}async function sN(e,t,r){let{provider:n,adapter:i}=e,a=r.id?await i.listAuthenticatorsByUserId(r.id):null,o=n0(32),s=n.getRelayingParty(e,t);return await n.simpleWebAuthn.generateRegistrationOptions({...n.registrationOptions,userID:o,userName:r.email,userDisplayName:r.name??void 0,rpID:s.id,rpName:s.name,excludeCredentials:a?.map(e=>({id:sU(e.credentialID),type:"public-key",transports:sD(e.transports)}))})}function sI(e){let{provider:t,adapter:r}=e;if(!r)throw new tE("An adapter is required for the WebAuthn provider");if(!t||"webauthn"!==t.type)throw new tD("Provider must be WebAuthn");return{...e,provider:t,adapter:r}}function sU(e){return new Uint8Array(sA.from(e,"base64"))}function sj(e){return sA.from(e).toString("base64")}function sD(e){return e?e.split(","):void 0}async function sM(e,t,r,n){if(!t.provider)throw new tD("Callback route called without provider");let{query:i,body:a,method:o,headers:s}=e,{provider:c,adapter:l,url:u,callbackUrl:d,pages:p,jwt:f,events:h,callbacks:g,session:{strategy:b,maxAge:m},logger:y}=t,w="jwt"===b;try{if("oauth"===c.type||"oidc"===c.type){let o,s=c.authorization?.url.searchParams.get("response_mode")==="form_post"?a:i;if(t.isOnRedirectProxy&&s?.state){let e=await sw.decode(s.state,t);if(e?.origin&&new URL(e.origin).origin!==t.url.origin){let t=`${e.origin}?${new URLSearchParams(s)}`;return y.debug("Proxy redirecting to",t),{redirect:t,cookies:n}}}let b=await sS(s,e.cookies,t);b.cookies.length&&n.push(...b.cookies),y.debug("authorization result",b);let{user:v,account:x,profile:_}=b;if(!v||!x||!_)return{redirect:`${u}/signin`,cookies:n};if(l){let{getUserByAccount:e}=l;o=await e({providerAccountId:x.providerAccountId,provider:c.id})}let E=await s$({user:o??v,account:x,profile:_},t);if(E)return{redirect:E,cookies:n};let{user:S,session:k,isNewUser:A}=await ap(r.value,v,x,t);if(w){let e={name:S.name,email:S.email,picture:S.image,sub:S.id?.toString()},i=await g.jwt({token:e,user:S,account:x,profile:_,isNewUser:A,trigger:A?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:k.sessionToken,options:{...t.cookies.sessionToken.options,expires:k.expires}});if(await h.signIn?.({user:S,account:x,profile:_,isNewUser:A}),A&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("email"===c.type){let e=i?.token,a=i?.email;if(!e){let t=TypeError("Missing token. The sign-in URL was manually opened without token or the link was not sent correctly in the email.",{cause:{hasToken:!!e}});throw t.name="Configuration",t}let o=c.secret??t.secret,s=await l.useVerificationToken({identifier:a,token:await nQ(`${e}${o}`)}),u=!!s,b=u&&s.expires.valueOf()<Date.now();if(!u||b||a&&s.identifier!==a)throw new t$({hasInvite:u,expired:b});let{identifier:y}=s,v=await l.getUserByEmail(y)??{id:crypto.randomUUID(),email:y,emailVerified:null},x={providerAccountId:v.email,userId:v.id,type:"email",provider:c.id},_=await s$({user:v,account:x},t);if(_)return{redirect:_,cookies:n};let{user:E,session:S,isNewUser:k}=await ap(r.value,v,x,t);if(w){let e={name:E.name,email:E.email,picture:E.image,sub:E.id?.toString()},i=await g.jwt({token:e,user:E,account:x,isNewUser:k,trigger:k?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:S.sessionToken,options:{...t.cookies.sessionToken.options,expires:S.expires}});if(await h.signIn?.({user:E,account:x,isNewUser:k}),k&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}if("credentials"===c.type&&"POST"===o){let e=a??{};Object.entries(i??{}).forEach(([e,t])=>u.searchParams.set(e,t));let l=await c.authorize(e,new Request(u,{headers:s,method:o,body:JSON.stringify(a)}));if(l)l.id=l.id?.toString()??crypto.randomUUID();else throw new tw;let p={providerAccountId:l.id,type:"credentials",provider:c.id},b=await s$({user:l,account:p,credentials:e},t);if(b)return{redirect:b,cookies:n};let y={name:l.name,email:l.email,picture:l.image,sub:l.id},w=await g.jwt({token:y,user:l,account:p,isNewUser:!1,trigger:"signIn"});if(null===w)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,i=await f.encode({...f,token:w,salt:e}),a=new Date;a.setTime(a.getTime()+1e3*m);let o=r.chunk(i,{expires:a});n.push(...o)}return await h.signIn?.({user:l,account:p}),{redirect:d,cookies:n}}else if("webauthn"===c.type&&"POST"===o){let i,a,o,s=e.body?.action;if("string"!=typeof s||"authenticate"!==s&&"register"!==s)throw new td("Invalid action parameter");let c=sI(t);switch(s){case"authenticate":{let t=await sP(c,e,n);i=t.user,a=t.account;break}case"register":{let r=await sC(t,e,n);i=r.user,a=r.account,o=r.authenticator}}await s$({user:i,account:a},t);let{user:l,isNewUser:u,session:b,account:y}=await ap(r.value,i,a,t);if(!y)throw new td("Error creating or finding account");if(o&&l.id&&await c.adapter.createAuthenticator({...o,userId:l.id}),w){let e={name:l.name,email:l.email,picture:l.image,sub:l.id?.toString()},i=await g.jwt({token:e,user:l,account:y,isNewUser:u,trigger:u?"signUp":"signIn"});if(null===i)n.push(...r.clean());else{let e=t.cookies.sessionToken.name,a=await f.encode({...f,token:i,salt:e}),o=new Date;o.setTime(o.getTime()+1e3*m);let s=r.chunk(a,{expires:o});n.push(...s)}}else n.push({name:t.cookies.sessionToken.name,value:b.sessionToken,options:{...t.cookies.sessionToken.options,expires:b.expires}});if(await h.signIn?.({user:l,account:y,isNewUser:u}),u&&p.newUser)return{redirect:`${p.newUser}${p.newUser.includes("?")?"&":"?"}${new URLSearchParams({callbackUrl:d})}`,cookies:n};return{redirect:d,cookies:n}}throw new tD(`Callback for provider type (${c.type}) is not supported`)}catch(t){if(t instanceof td)throw t;let e=new tg(t,{provider:c.id});throw y.debug("callback route error details",{method:o,query:i,body:a}),e}}async function s$(e,t){let r,{signIn:n,redirect:i}=t.callbacks;try{r=await n(e)}catch(e){if(e instanceof td)throw e;throw new th(e)}if(!r)throw new th("AccessDenied");if("string"==typeof r)return await i({url:r,baseUrl:t.url.origin})}async function sL(e,t,r,n,i){let{adapter:a,jwt:o,events:s,callbacks:c,logger:l,session:{strategy:u,maxAge:d}}=e,p={body:null,headers:{"Content-Type":"application/json",...!n&&{"Cache-Control":"private, no-cache, no-store",Expires:"0",Pragma:"no-cache"}},cookies:r},f=t.value;if(!f)return p;if("jwt"===u){try{let r=e.cookies.sessionToken.name,a=await o.decode({...o,token:f,salt:r});if(!a)throw Error("Invalid JWT");let l=await c.jwt({token:a,...n&&{trigger:"update"},session:i}),u=ad(d);if(null!==l){let e={user:{name:l.name,email:l.email,image:l.picture},expires:u.toISOString()},n=await c.session({session:e,token:l});p.body=n;let i=await o.encode({...o,token:l,salt:r}),a=t.chunk(i,{expires:u});p.cookies?.push(...a),await s.session?.({session:n,token:l})}else p.cookies?.push(...t.clean())}catch(e){l.error(new t_(e)),p.cookies?.push(...t.clean())}return p}try{let{getSessionAndUser:r,deleteSession:o,updateSession:l}=a,u=await r(f);if(u&&u.session.expires.valueOf()<Date.now()&&(await o(f),u=null),u){let{user:t,session:r}=u,a=e.session.updateAge,o=r.expires.valueOf()-1e3*d+1e3*a,h=ad(d);o<=Date.now()&&await l({sessionToken:f,expires:h});let g=await c.session({session:{...r,user:t},user:t,newSession:i,...n?{trigger:"update"}:{}});p.body=g,p.cookies?.push({name:e.cookies.sessionToken.name,value:f,options:{...e.cookies.sessionToken.options,expires:h}}),await s.session?.({session:g})}else f&&p.cookies?.push(...t.clean())}catch(e){l.error(new tC(e))}return p}async function sH(e,t){let r,n,{logger:i,provider:a}=t,o=a.authorization?.url;if(!o||"authjs.dev"===o.host){let e=new URL(a.issuer),t=await a$(e,{[av]:a[n4],[am]:!0}),r=await aW(e,t).catch(t=>{if(!(t instanceof TypeError)||"Invalid URL"!==t.message)throw t;throw TypeError(`Discovery request responded with an invalid issuer. expected: ${e}`)});if(!r.authorization_endpoint)throw TypeError("Authorization server did not provide an authorization endpoint.");o=new URL(r.authorization_endpoint)}let s=o.searchParams,c=a.callbackUrl;!t.isOnRedirectProxy&&a.redirectProxyUrl&&(c=a.redirectProxyUrl,n=a.callbackUrl,i.debug("using redirect proxy",{redirect_uri:c,data:n}));let l=Object.assign({response_type:"code",client_id:a.clientId,redirect_uri:c,...a.authorization?.params},Object.fromEntries(a.authorization?.url.searchParams??[]),e);for(let e in l)s.set(e,l[e]);let u=[];a.authorization?.url.searchParams.get("response_mode")==="form_post"&&(t.cookies.state.options.sameSite="none",t.cookies.state.options.secure=!0,t.cookies.nonce.options.sameSite="none",t.cookies.nonce.options.secure=!0);let d=await sw.create(t,n);if(d&&(s.set("state",d.value),u.push(d.cookie)),a.checks?.includes("pkce"))if(r&&!r.code_challenge_methods_supported?.includes("S256"))"oidc"===a.type&&(a.checks=["nonce"]);else{let{value:e,cookie:r}=await sm.create(t);s.set("code_challenge",e),s.set("code_challenge_method","S256"),u.push(r)}let p=await sv.create(t);return p&&(s.set("nonce",p.value),u.push(p.cookie)),"oidc"!==a.type||o.searchParams.has("scope")||o.searchParams.set("scope","openid profile email"),i.debug("authorization url is ready",{url:o,cookies:u,provider:a}),{redirect:o.toString(),cookies:u}}async function sW(e,t){let r,{body:n}=e,{provider:i,callbacks:a,adapter:o}=t,s=(i.normalizeIdentifier??function(e){if(!e)throw Error("Missing email from request body.");let[t,r]=e.toLowerCase().trim().split("@");return r=r.split(",")[0],`${t}@${r}`})(n?.email),c={id:crypto.randomUUID(),email:s,emailVerified:null},l=await o.getUserByEmail(s)??c,u={providerAccountId:s,userId:l.id,type:"email",provider:i.id};try{r=await a.signIn({user:l,account:u,email:{verificationRequest:!0}})}catch(e){throw new th(e)}if(!r)throw new th("AccessDenied");if("string"==typeof r)return{redirect:await a.redirect({url:r,baseUrl:t.url.origin})};let{callbackUrl:d,theme:p}=t,f=await i.generateVerificationToken?.()??n0(32),h=new Date(Date.now()+(i.maxAge??86400)*1e3),g=i.secret??t.secret,b=new URL(t.basePath,t.url.origin),m=i.sendVerificationRequest({identifier:s,token:f,expires:h,url:`${b}/callback/${i.id}?${new URLSearchParams({callbackUrl:d,token:f,email:s})}`,provider:i,theme:p,request:new Request(e.url,{headers:e.headers,method:e.method,body:"POST"===e.method?JSON.stringify(e.body??{}):void 0})}),y=o.createVerificationToken?.({identifier:s,token:await nQ(`${f}${g}`),expires:h});return await Promise.all([m,y]),{redirect:`${b}/verify-request?${new URLSearchParams({provider:i.id,type:i.type})}`}}async function sK(e,t,r){let n=`${r.url.origin}${r.basePath}/signin`;if(!r.provider)return{redirect:n,cookies:t};switch(r.provider.type){case"oauth":case"oidc":{let{redirect:n,cookies:i}=await sH(e.query,r);return i&&t.push(...i),{redirect:n,cookies:t}}case"email":return{...await sW(e,r),cookies:t};default:return{redirect:n,cookies:t}}}async function sq(e,t,r){let{jwt:n,events:i,callbackUrl:a,logger:o,session:s}=r,c=t.value;if(!c)return{redirect:a,cookies:e};try{if("jwt"===s.strategy){let e=r.cookies.sessionToken.name,t=await n.decode({...n,token:c,salt:e});await i.signOut?.({token:t})}else{let e=await r.adapter?.deleteSession(c);await i.signOut?.({session:e})}}catch(e){o.error(new tI(e))}return e.push(...t.clean()),{redirect:a,cookies:e}}async function sB(e,t){let{adapter:r,jwt:n,session:{strategy:i}}=e,a=t.value;if(!a)return null;if("jwt"===i){let t=e.cookies.sessionToken.name,r=await n.decode({...n,token:a,salt:t});if(r&&r.sub)return{id:r.sub,name:r.name,email:r.email,image:r.picture}}else{let e=await r?.getSessionAndUser(a);if(e)return e.user}return null}async function sJ(e,t,r,n){let i=sI(t),{provider:a}=i,{action:o}=e.query??{};if("register"!==o&&"authenticate"!==o&&void 0!==o)return{status:400,body:{error:"Invalid action"},cookies:n,headers:{"Content-Type":"application/json"}};let s=await sB(t,r),c=s?{user:s,exists:!0}:await a.getUserInfo(t,e),l=c?.user;switch(function(e,t,r){let{user:n,exists:i=!1}=r??{};switch(e){case"authenticate":return"authenticate";case"register":if(n&&t===i)return"register";break;case void 0:if(!t)if(!n)return"authenticate";else if(i)return"authenticate";else return"register"}return null}(o,!!s,c)){case"authenticate":return sT(i,e,l,n);case"register":if("string"==typeof l?.email)return sR(i,e,l,n);break;default:return{status:400,body:{error:"Invalid request"},cookies:n,headers:{"Content-Type":"application/json"}}}}async function sV(e,t){let{action:r,providerId:n,error:i,method:a}=e,o=t.skipCSRFCheck===n3,{options:s,cookies:c}=await ia({authOptions:t,action:r,providerId:n,url:e.url,callbackUrl:e.body?.callbackUrl??e.query?.callbackUrl,csrfToken:e.body?.csrfToken,cookies:e.cookies,isPost:"POST"===a,csrfDisabled:o}),l=new tu(s.cookies.sessionToken,e.cookies,s.logger);if("GET"===a){let t=au({...s,query:e.query,cookies:c});switch(r){case"callback":return await sM(e,s,l,c);case"csrf":return t.csrf(o,s,c);case"error":return t.error(i);case"providers":return t.providers(s.providers);case"session":return await sL(s,l,c);case"signin":return t.signin(n,i);case"signout":return t.signout();case"verify-request":return t.verifyRequest();case"webauthn-options":return await sJ(e,s,l,c)}}else{let{csrfTokenVerified:t}=s;switch(r){case"callback":return"credentials"===s.provider.type&&n2(r,t),await sM(e,s,l,c);case"session":return n2(r,t),await sL(s,l,c,!0,e.body?.data);case"signin":return n2(r,t),await sK(e,c,s);case"signout":return n2(r,t),await sq(c,l,s)}}throw new tU(`Cannot handle action: ${r}`)}function sz(e,t,r,n,i){let a,o=i?.basePath,s=n.AUTH_URL??n.NEXTAUTH_URL;if(s)a=new URL(s),o&&"/"!==o&&"/"!==a.pathname&&(a.pathname!==o&&nV(i).warn("env-url-basepath-mismatch"),a.pathname="/");else{let e=r.get("x-forwarded-host")??r.get("host"),n=r.get("x-forwarded-proto")??t??"https",i=n.endsWith(":")?n:n+":";a=new URL(`${i}//${e}`)}let c=a.toString().replace(/\/$/,"");if(o){let t=o?.replace(/(^\/|\/$)/g,"")??"";return new URL(`${c}/${t}/${e}`)}return new URL(`${c}/${e}`)}async function sF(e,t){let r=nV(t),n=await nZ(e,t);if(!n)return Response.json("Bad request.",{status:400});let i=function(e,t){let{url:r}=e,n=[];if(!tV&&t.debug&&n.push("debug-enabled"),!t.trustHost)return new tM(`Host must be trusted. URL was: ${e.url}`);if(!t.secret?.length)return new tA("Please define a `secret`");let i=e.query?.callbackUrl;if(i&&!tz(i,r.origin))return new ty(`Invalid callback URL. Received: ${i}`);let{callbackUrl:a}=tl(t.useSecureCookies??"https:"===r.protocol),o=e.cookies?.[t.cookies?.callbackUrl?.name??a.name];if(o&&!tz(o,r.origin))return new ty(`Invalid callback URL. Received: ${o}`);let s=!1;for(let e of t.providers){let t="function"==typeof e?e():e;if(("oauth"===t.type||"oidc"===t.type)&&!(t.issuer??t.options?.issuer)){let e,{authorization:r,token:n,userinfo:i}=t;if("string"==typeof r||r?.url?"string"==typeof n||n?.url?"string"==typeof i||i?.url||(e="userinfo"):e="token":e="authorization",e)return new tv(`Provider "${t.id}" is missing both \`issuer\` and \`${e}\` endpoint config. At least one of them is required`)}if("credentials"===t.type)tF=!0;else if("email"===t.type)tG=!0;else if("webauthn"===t.type){var c;if(tX=!0,t.simpleWebAuthnBrowserVersion&&(c=t.simpleWebAuthnBrowserVersion,!/^v\d+(?:\.\d+){0,2}$/.test(c)))return new td(`Invalid provider config for "${t.id}": simpleWebAuthnBrowserVersion "${t.simpleWebAuthnBrowserVersion}" must be a valid semver string.`);if(t.enableConditionalUI){if(s)return new tW("Multiple webauthn providers have 'enableConditionalUI' set to True. Only one provider can have this option enabled at a time");if(s=!0,!Object.values(t.formFields).some(e=>e.autocomplete&&e.autocomplete.toString().indexOf("webauthn")>-1))return new tK(`Provider "${t.id}" has 'enableConditionalUI' set to True, but none of its formFields have 'webauthn' in their autocomplete param`)}}}if(tF){let e=t.session?.strategy==="database",r=!t.providers.some(e=>"credentials"!==("function"==typeof e?e():e).type);if(e&&r)return new tj("Signing in with credentials only supported if JWT strategy is enabled");if(t.providers.some(e=>{let t="function"==typeof e?e():e;return"credentials"===t.type&&!t.authorize}))return new tk("Must define an authorize() handler to use credentials authentication provider")}let{adapter:l,session:u}=t,d=[];if(tG||u?.strategy==="database"||!u?.strategy&&l)if(tG){if(!l)return new tE("Email login requires an adapter");d.push(...tZ)}else{if(!l)return new tE("Database session requires an adapter");d.push(...tY)}if(tX){if(!t.experimental?.enableWebAuthn)return new tJ("WebAuthn is an experimental feature. To enable it, set `experimental.enableWebAuthn` to `true` in your config");if(n.push("experimental-webauthn"),!l)return new tE("WebAuthn requires an adapter");d.push(...tQ)}if(l){let e=d.filter(e=>!(e in l));if(e.length)return new tS(`Required adapter methods were missing: ${e.join(", ")}`)}return tV||(tV=!0),n}(n,t);if(Array.isArray(i))i.forEach(r.warn);else if(i){if(r.error(i),!new Set(["signin","signout","error","verify-request"]).has(n.action)||"GET"!==n.method)return Response.json({message:"There was a problem with the server configuration. Check the server logs for more information."},{status:500});let{pages:e,theme:a}=t,o=e?.error&&n.url.searchParams.get("callbackUrl")?.startsWith(e.error);if(!e?.error||o)return o&&r.error(new tb(`The error page ${e?.error} should not require authentication`)),nY(au({theme:a}).error("Configuration"));let s=`${n.url.origin}${e.error}?error=Configuration`;return Response.redirect(s)}let a=e.headers?.has("X-Auth-Return-Redirect"),o=t.raw===n8;try{let e=await sV(n,t);if(o)return e;let r=nY(e),i=r.headers.get("Location");if(!a||!i)return r;return Response.json({url:i},{headers:r.headers})}catch(d){r.error(d);let i=d instanceof td;if(i&&o&&!a)throw d;if("POST"===e.method&&"session"===n.action)return Response.json(null,{status:400});let s=new URLSearchParams({error:d instanceof td&&tH.has(d.type)?d.type:"Configuration"});d instanceof tw&&s.set("code",d.code);let c=i&&d.kind||"error",l=t.pages?.[c]??`${t.basePath}/${c.toLowerCase()}`,u=`${n.url.origin}${l}?${s}`;if(a)return Response.json({url:u});return Response.redirect(u)}}r(280),"undefined"==typeof URLPattern||URLPattern;var sG=r(557),sX=r(602),sZ=r(801);function sY(){let e=e5.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}function sQ(e){let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return e;let{origin:r}=new URL(t),{href:n,origin:i}=e.nextUrl;return new q(n.replace(i,r),e)}function s0(e){try{e.secret??(e.secret=process.env.AUTH_SECRET??process.env.NEXTAUTH_SECRET);let t=process.env.AUTH_URL??process.env.NEXTAUTH_URL;if(!t)return;let{pathname:r}=new URL(t);if("/"===r)return;e.basePath||(e.basePath=r)}catch{}finally{e.basePath||(e.basePath="/api/auth"),function(e,t,r=!1){try{let n=e.AUTH_URL;n&&(t.basePath?r||nV(t).warn("env-url-basepath-redundant"):t.basePath=new URL(n).pathname)}catch{}finally{t.basePath??(t.basePath="/auth")}if(!t.secret?.length){t.secret=[];let r=e.AUTH_SECRET;for(let n of(r&&t.secret.push(r),[1,2,3])){let r=e[`AUTH_SECRET_${n}`];r&&t.secret.unshift(r)}}t.redirectProxyUrl??(t.redirectProxyUrl=e.AUTH_REDIRECT_PROXY_URL),t.trustHost??(t.trustHost=!!(e.AUTH_URL??e.AUTH_TRUST_HOST??e.VERCEL??e.CF_PAGES??"production"!==e.NODE_ENV)),t.providers=t.providers.map(t=>{let{id:r}="function"==typeof t?t({}):t,n=r.toUpperCase().replace(/-/g,"_"),i=e[`AUTH_${n}_ID`],a=e[`AUTH_${n}_SECRET`],o=e[`AUTH_${n}_ISSUER`],s=e[`AUTH_${n}_KEY`],c="function"==typeof t?t({clientId:i,clientSecret:a,issuer:o,apiKey:s}):t;return"oauth"===c.type||"oidc"===c.type?(c.clientId??(c.clientId=i),c.clientSecret??(c.clientSecret=a),c.issuer??(c.issuer=o)):"email"===c.type&&(c.apiKey??(c.apiKey=s)),c})}(process.env,e,!0)}}new WeakMap;var s1=r(815);let s2={current:null},s5="function"==typeof s1.cache?s1.cache:e=>e,s6=console.warn;function s3(e){return function(...t){s6(e(...t))}}function s8(){let e="cookies",t=et.J.getStore(),r=er.FP.getStore();if(t){if(r&&"after"===r.phase&&!sY())throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(t.forceStatic)return s9(ei.seal(new W.RequestCookies(new Headers({}))));if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new sX.f(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(r)if("prerender"===r.type){var n=t.route,i=r;let e=s4.get(i);if(e)return e;let a=(0,sZ.W)(i.renderSignal,"`cookies()`");return s4.set(i,a),Object.defineProperties(a,{[Symbol.iterator]:{value:function(){let e="`cookies()[Symbol.iterator]()`",t=ct(n,e);(0,sG.t3)(n,e,t,i)}},size:{get(){let e="`cookies().size`",t=ct(n,e);(0,sG.t3)(n,e,t,i)}},get:{value:function(){let e;e=0==arguments.length?"`cookies().get()`":`\`cookies().get(${s7(arguments[0])})\``;let t=ct(n,e);(0,sG.t3)(n,e,t,i)}},getAll:{value:function(){let e;e=0==arguments.length?"`cookies().getAll()`":`\`cookies().getAll(${s7(arguments[0])})\``;let t=ct(n,e);(0,sG.t3)(n,e,t,i)}},has:{value:function(){let e;e=0==arguments.length?"`cookies().has()`":`\`cookies().has(${s7(arguments[0])})\``;let t=ct(n,e);(0,sG.t3)(n,e,t,i)}},set:{value:function(){let e;if(0==arguments.length)e="`cookies().set()`";else{let t=arguments[0];e=t?`\`cookies().set(${s7(t)}, ...)\``:"`cookies().set(...)`"}let t=ct(n,e);(0,sG.t3)(n,e,t,i)}},delete:{value:function(){let e;e=0==arguments.length?"`cookies().delete()`":1==arguments.length?`\`cookies().delete(${s7(arguments[0])})\``:`\`cookies().delete(${s7(arguments[0])}, ...)\``;let t=ct(n,e);(0,sG.t3)(n,e,t,i)}},clear:{value:function(){let e="`cookies().clear()`",t=ct(n,e);(0,sG.t3)(n,e,t,i)}},toString:{value:function(){let e="`cookies().toString()`",t=ct(n,e);(0,sG.t3)(n,e,t,i)}}}),a}else"prerender-ppr"===r.type?(0,sG.Ui)(t.route,e,r.dynamicTracking):"prerender-legacy"===r.type&&(0,sG.xI)(e,t,r);(0,sG.Pk)(t,r)}let a=(0,er.XN)(e);return s9(es(a)?a.userspaceMutableCookies:a.cookies)}s5(e=>{try{s6(s2.current)}finally{s2.current=null}});let s4=new WeakMap;function s9(e){let t=s4.get(e);if(t)return t;let r=Promise.resolve(e);return s4.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):cr.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):cn.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function s7(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let ce=s3(ct);function ct(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function cr(){return this.getAll().map(e=>[e.name,e]).values()}function cn(e){for(let e of this.getAll())this.delete(e.name);return e}function ci(){let e=et.J.getStore(),t=er.FP.getStore();if(e){if(t&&"after"===t.phase&&!sY())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return co(ee.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new sX.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,n=t;let i=ca.get(n);if(i)return i;let a=(0,sZ.W)(n.renderSignal,"`headers()`");return ca.set(n,a),Object.defineProperties(a,{append:{value:function(){let e=`\`headers().append(${cs(arguments[0])}, ...)\``,t=cl(r,e);(0,sG.t3)(r,e,t,n)}},delete:{value:function(){let e=`\`headers().delete(${cs(arguments[0])})\``,t=cl(r,e);(0,sG.t3)(r,e,t,n)}},get:{value:function(){let e=`\`headers().get(${cs(arguments[0])})\``,t=cl(r,e);(0,sG.t3)(r,e,t,n)}},has:{value:function(){let e=`\`headers().has(${cs(arguments[0])})\``,t=cl(r,e);(0,sG.t3)(r,e,t,n)}},set:{value:function(){let e=`\`headers().set(${cs(arguments[0])}, ...)\``,t=cl(r,e);(0,sG.t3)(r,e,t,n)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=cl(r,e);(0,sG.t3)(r,e,t,n)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=cl(r,e);(0,sG.t3)(r,e,t,n)}},keys:{value:function(){let e="`headers().keys()`",t=cl(r,e);(0,sG.t3)(r,e,t,n)}},values:{value:function(){let e="`headers().values()`",t=cl(r,e);(0,sG.t3)(r,e,t,n)}},entries:{value:function(){let e="`headers().entries()`",t=cl(r,e);(0,sG.t3)(r,e,t,n)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=cl(r,e);(0,sG.t3)(r,e,t,n)}}}),a}else"prerender-ppr"===t.type?(0,sG.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,sG.xI)("headers",e,t);(0,sG.Pk)(e,t)}return co((0,er.XN)("headers").headers)}let ca=new WeakMap;function co(e){let t=ca.get(e);if(t)return t;let r=Promise.resolve(e);return ca.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function cs(e){return"string"==typeof e?`'${e}'`:"..."}let cc=s3(cl);function cl(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}function cu(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return cd(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return cd(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return cf(null);default:return t}}function cd(e,t){let r,n=cp.get(cu);return n||(r=cf(e),cp.set(e,r),r)}r(16);let cp=new WeakMap;function cf(e){let t=new ch(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class ch{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){cb("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){cb("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let cg=s3(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function cb(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}async function cm(e,t){return sF(new Request(sz("session",e.get("x-forwarded-proto"),e,process.env,t),{headers:{cookie:e.get("cookie")??""}}),{...t,callbacks:{...t.callbacks,async session(...e){let r=await t.callbacks?.session?.(...e)??{...e[0].session,expires:e[0].session.expires?.toISOString?.()??e[0].session.expires};return{user:e[0].user??e[0].token,...r}}}})}function cy(e){return"function"==typeof e}function cw(e,t){return"function"==typeof e?async(...r)=>{if(!r.length){let r=await ci(),n=await e(void 0);return t?.(n),cm(r,n).then(e=>e.json())}if(r[0]instanceof Request){let n=r[0],i=r[1],a=await e(n);return t?.(a),cv([n,i],a)}if(cy(r[0])){let n=r[0];return async(...r)=>{let i=await e(r[0]);return t?.(i),cv(r,i,n)}}let n="req"in r[0]?r[0].req:r[0],i="res"in r[0]?r[0].res:r[1],a=await e(n);return t?.(a),cm(new Headers(n.headers),a).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in i?i.headers.append("set-cookie",t):i.appendHeader("set-cookie",t);return t})}:(...t)=>{if(!t.length)return Promise.resolve(ci()).then(t=>cm(t,e).then(e=>e.json()));if(t[0]instanceof Request)return cv([t[0],t[1]],e);if(cy(t[0])){let r=t[0];return async(...t)=>cv(t,e,r).then(e=>e)}let r="req"in t[0]?t[0].req:t[0],n="res"in t[0]?t[0].res:t[1];return cm(new Headers(r.headers),e).then(async e=>{let t=await e.json();for(let t of e.headers.getSetCookie())"headers"in n?n.headers.append("set-cookie",t):n.appendHeader("set-cookie",t);return t})}}async function cv(e,t,r){let n=sQ(e[0]),i=await cm(n.headers,t),a=await i.json(),o=!0;t.callbacks?.authorized&&(o=await t.callbacks.authorized({request:n,auth:a}));let s=F.next?.();if(o instanceof Response){s=o;let e=o.headers.get("Location"),{pathname:r}=n.nextUrl;e&&function(e,t,r){let n=t.replace(`${e}/`,""),i=Object.values(r.pages??{});return(cx.has(n)||i.includes(t))&&t===e}(r,new URL(e).pathname,t)&&(o=!0)}else if(r)n.auth=a,s=await r(n,e[1])??F.next();else if(!o){let e=t.pages?.signIn??`${t.basePath}/signin`;if(n.nextUrl.pathname!==e){let t=n.nextUrl.clone();t.pathname=e,t.searchParams.set("callbackUrl",n.nextUrl.href),s=F.redirect(t)}}let c=new Response(s?.body,s);for(let e of i.headers.getSetCookie())c.headers.append("set-cookie",e);return c}let cx=new Set(["providers","session","csrf","signin","signout","callback","verify-request","error"]);var c_=r(821),cE=r(167);let cS=r(830).s;function ck(e,t){var r;throw null!=t||(t=(null==cS||null==(r=cS.getStore())?void 0:r.isAction)?cE.zB.push:cE.zB.replace),function(e,t,r){void 0===r&&(r=c_.Q.TemporaryRedirect);let n=Object.defineProperty(Error(cE.oJ),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n.digest=cE.oJ+";"+t+";"+e+";"+r+";",n}(e,t,c_.Q.TemporaryRedirect)}var cA=r(159);async function cR(e,t={},r,n){let i=new Headers(await ci()),{redirect:a=!0,redirectTo:o,...s}=t instanceof FormData?Object.fromEntries(t):t,c=o?.toString()??i.get("Referer")??"/",l=sz("signin",i.get("x-forwarded-proto"),i,process.env,n);if(!e)return l.searchParams.append("callbackUrl",c),a&&ck(l.toString()),l.toString();let u=`${l}/${e}?${new URLSearchParams(r)}`,d={};for(let t of n.providers){let{options:r,...n}="function"==typeof t?t():t,i=r?.id??n.id;if(i===e){d={id:i,type:r?.type??n.type};break}}if(!d.id){let e=`${l}?${new URLSearchParams({callbackUrl:c})}`;return a&&ck(e),e}"credentials"===d.type&&(u=u.replace("signin","callback")),i.set("Content-Type","application/x-www-form-urlencoded");let p=new Request(u,{method:"POST",headers:i,body:new URLSearchParams({...s,callbackUrl:c})}),f=await sF(p,{...n,raw:n8,skipCSRFCheck:n3}),h=await s8();for(let e of f?.cookies??[])h.set(e.name,e.value,e.options);let g=(f instanceof Response?f.headers.get("Location"):f.redirect)??u;return a?ck(g):g}async function cT(e,t){let r=new Headers(await ci());r.set("Content-Type","application/x-www-form-urlencoded");let n=sz("signout",r.get("x-forwarded-proto"),r,process.env,t),i=new URLSearchParams({callbackUrl:e?.redirectTo??r.get("Referer")??"/"}),a=new Request(n,{method:"POST",headers:r,body:i}),o=await sF(a,{...t,raw:n8,skipCSRFCheck:n3}),s=await s8();for(let e of o?.cookies??[])s.set(e.name,e.value,e.options);return e?.redirect??!0?ck(o.redirect):o}async function cP(e,t){let r=new Headers(await ci());r.set("Content-Type","application/json");let n=new Request(sz("session",r.get("x-forwarded-proto"),r,process.env,t),{method:"POST",headers:r,body:JSON.stringify({data:e})}),i=await sF(n,{...t,raw:n8,skipCSRFCheck:n3}),a=await s8();for(let e of i?.cookies??[])a.set(e.name,e.value,e.options);return i.body}cA.s8,cA.s8,cA.s8,r(792).X;var cC=r(388);function cO(e){let t={};for(let r in e)void 0!==e[r]&&(t[r]=e[r]);return{data:t}}let cN=globalThis.prisma||new cC.PrismaClient;var cI=r(825),cU=null;function cj(e,t){if("number"!=typeof(e=e||cz))throw Error("Illegal arguments: "+typeof e+", "+typeof t);e<4?e=4:e>31&&(e=31);var r=[];return r.push("$2b$"),e<10&&r.push("0"),r.push(e.toString()),r.push("$"),r.push(cB(function(e){try{return crypto.getRandomValues(new Uint8Array(e))}catch{}try{return cI.randomBytes(e)}catch{}if(!cU)throw Error("Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative");return cU(e)}(cV),cV)),r.join("")}function cD(e,t,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof e&&(r=e,e=void 0),void 0===e)e=cz;else if("number"!=typeof e)throw Error("illegal arguments: "+typeof e);function n(t){cH(function(){try{t(null,cj(e))}catch(e){t(e)}})}if(!r)return new Promise(function(e,t){n(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);n(r)}function cM(e,t){if(void 0===t&&(t=cz),"number"==typeof t&&(t=cj(t)),"string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return c1(e,t)}function c$(e,t,r,n){function i(r){"string"==typeof e&&"number"==typeof t?cD(t,function(t,i){c1(e,i,r,n)}):"string"==typeof e&&"string"==typeof t?c1(e,t,r,n):cH(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t)))}if(!r)return new Promise(function(e,t){i(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);i(r)}function cL(e,t){for(var r=e.length^t.length,n=0;n<e.length;++n)r|=e.charCodeAt(n)^t.charCodeAt(n);return 0===r}var cH="undefined"!=typeof process&&process&&"function"==typeof process.nextTick?"function"==typeof setImmediate?setImmediate:process.nextTick:setTimeout;function cW(e){for(var t=0,r=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<128?t+=1:r<2048?t+=2:(64512&r)==55296&&(64512&e.charCodeAt(n+1))==56320?(++n,t+=4):t+=3;return t}var cK="./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),cq=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,54,55,56,57,58,59,60,61,62,63,-1,-1,-1,-1,-1,-1,-1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,-1,-1,-1,-1,-1,-1,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,-1,-1,-1,-1,-1];function cB(e,t){var r,n,i=0,a=[];if(t<=0||t>e.length)throw Error("Illegal len: "+t);for(;i<t;){if(r=255&e[i++],a.push(cK[r>>2&63]),r=(3&r)<<4,i>=t||(r|=(n=255&e[i++])>>4&15,a.push(cK[63&r]),r=(15&n)<<2,i>=t)){a.push(cK[63&r]);break}r|=(n=255&e[i++])>>6&3,a.push(cK[63&r]),a.push(cK[63&n])}return a.join("")}function cJ(e,t){var r,n,i,a,o,s=0,c=e.length,l=0,u=[];if(t<=0)throw Error("Illegal len: "+t);for(;s<c-1&&l<t&&(r=(o=e.charCodeAt(s++))<cq.length?cq[o]:-1,n=(o=e.charCodeAt(s++))<cq.length?cq[o]:-1,-1!=r&&-1!=n)&&(a=r<<2>>>0|(48&n)>>4,u.push(String.fromCharCode(a)),!(++l>=t||s>=c||-1==(i=(o=e.charCodeAt(s++))<cq.length?cq[o]:-1)||(a=(15&n)<<4>>>0|(60&i)>>2,u.push(String.fromCharCode(a)),++l>=t||s>=c)));){;a=(3&i)<<6>>>0|((o=e.charCodeAt(s++))<cq.length?cq[o]:-1),u.push(String.fromCharCode(a)),++l}var d=[];for(s=0;s<l;s++)d.push(u[s].charCodeAt(0));return d}var cV=16,cz=10,cF=[0x243f6a88,0x85a308d3,0x13198a2e,0x3707344,0xa4093822,0x299f31d0,0x82efa98,0xec4e6c89,0x452821e6,0x38d01377,0xbe5466cf,0x34e90c6c,0xc0ac29b7,0xc97c50dd,0x3f84d5b5,0xb5470917,0x9216d5d9,0x8979fb1b],cG=[0xd1310ba6,0x98dfb5ac,0x2ffd72db,0xd01adfb7,0xb8e1afed,0x6a267e96,0xba7c9045,0xf12c7f99,0x24a19947,0xb3916cf7,0x801f2e2,0x858efc16,0x636920d8,0x71574e69,0xa458fea3,0xf4933d7e,0xd95748f,0x728eb658,0x718bcd58,0x82154aee,0x7b54a41d,0xc25a59b5,0x9c30d539,0x2af26013,0xc5d1b023,0x286085f0,0xca417918,0xb8db38ef,0x8e79dcb0,0x603a180e,0x6c9e0e8b,0xb01e8a3e,0xd71577c1,0xbd314b27,0x78af2fda,0x55605c60,0xe65525f3,0xaa55ab94,0x57489862,0x63e81440,0x55ca396a,0x2aab10b6,0xb4cc5c34,0x1141e8ce,0xa15486af,0x7c72e993,0xb3ee1411,0x636fbc2a,0x2ba9c55d,0x741831f6,0xce5c3e16,0x9b87931e,0xafd6ba33,0x6c24cf5c,0x7a325381,0x28958677,0x3b8f4898,0x6b4bb9af,0xc4bfe81b,0x66282193,0x61d809cc,0xfb21a991,0x487cac60,0x5dec8032,0xef845d5d,0xe98575b1,0xdc262302,0xeb651b88,0x23893e81,0xd396acc5,0xf6d6ff3,0x83f44239,0x2e0b4482,0xa4842004,0x69c8f04a,0x9e1f9b5e,0x21c66842,0xf6e96c9a,0x670c9c61,0xabd388f0,0x6a51a0d2,0xd8542f68,0x960fa728,0xab5133a3,0x6eef0b6c,0x137a3be4,0xba3bf050,0x7efb2a98,0xa1f1651d,0x39af0176,0x66ca593e,0x82430e88,0x8cee8619,0x456f9fb4,0x7d84a5c3,0x3b8b5ebe,0xe06f75d8,0x85c12073,0x401a449f,0x56c16aa6,0x4ed3aa62,0x363f7706,0x1bfedf72,0x429b023d,0x37d0d724,0xd00a1248,0xdb0fead3,0x49f1c09b,0x75372c9,0x80991b7b,0x25d479d8,0xf6e8def7,0xe3fe501a,0xb6794c3b,0x976ce0bd,0x4c006ba,0xc1a94fb6,0x409f60c4,0x5e5c9ec2,0x196a2463,0x68fb6faf,0x3e6c53b5,0x1339b2eb,0x3b52ec6f,0x6dfc511f,0x9b30952c,0xcc814544,0xaf5ebd09,0xbee3d004,0xde334afd,0x660f2807,0x192e4bb3,0xc0cba857,0x45c8740f,0xd20b5f39,0xb9d3fbdb,0x5579c0bd,0x1a60320a,0xd6a100c6,0x402c7279,0x679f25fe,0xfb1fa3cc,0x8ea5e9f8,0xdb3222f8,0x3c7516df,0xfd616b15,0x2f501ec8,0xad0552ab,0x323db5fa,0xfd238760,0x53317b48,0x3e00df82,0x9e5c57bb,0xca6f8ca0,0x1a87562e,0xdf1769db,0xd542a8f6,0x287effc3,0xac6732c6,0x8c4f5573,0x695b27b0,0xbbca58c8,0xe1ffa35d,0xb8f011a0,0x10fa3d98,0xfd2183b8,0x4afcb56c,0x2dd1d35b,0x9a53e479,0xb6f84565,0xd28e49bc,0x4bfb9790,0xe1ddf2da,0xa4cb7e33,0x62fb1341,0xcee4c6e8,0xef20cada,0x36774c01,0xd07e9efe,0x2bf11fb4,0x95dbda4d,0xae909198,0xeaad8e71,0x6b93d5a0,0xd08ed1d0,0xafc725e0,0x8e3c5b2f,0x8e7594b7,0x8ff6e2fb,0xf2122b64,0x8888b812,0x900df01c,0x4fad5ea0,0x688fc31c,0xd1cff191,0xb3a8c1ad,0x2f2f2218,0xbe0e1777,0xea752dfe,0x8b021fa1,0xe5a0cc0f,0xb56f74e8,0x18acf3d6,0xce89e299,0xb4a84fe0,0xfd13e0b7,0x7cc43b81,0xd2ada8d9,0x165fa266,0x80957705,0x93cc7314,0x211a1477,0xe6ad2065,0x77b5fa86,0xc75442f5,0xfb9d35cf,0xebcdaf0c,0x7b3e89a0,0xd6411bd3,0xae1e7e49,2428461,0x2071b35e,0x226800bb,0x57b8e0af,0x2464369b,0xf009b91e,0x5563911d,0x59dfa6aa,0x78c14389,0xd95a537f,0x207d5ba2,0x2e5b9c5,0x83260376,0x6295cfa9,0x11c81968,0x4e734a41,0xb3472dca,0x7b14a94a,0x1b510052,0x9a532915,0xd60f573f,0xbc9bc6e4,0x2b60a476,0x81e67400,0x8ba6fb5,0x571be91f,0xf296ec6b,0x2a0dd915,0xb6636521,0xe7b9f9b6,0xff34052e,0xc5855664,0x53b02d5d,0xa99f8fa1,0x8ba4799,0x6e85076a,0x4b7a70e9,0xb5b32944,0xdb75092e,0xc4192623,290971e4,0x49a7df7d,0x9cee60b8,0x8fedb266,0xecaa8c71,0x699a17ff,0x5664526c,0xc2b19ee1,0x193602a5,0x75094c29,0xa0591340,0xe4183a3e,0x3f54989a,0x5b429d65,0x6b8fe4d6,0x99f73fd6,0xa1d29c07,0xefe830f5,0x4d2d38e6,0xf0255dc1,0x4cdd2086,0x8470eb26,0x6382e9c6,0x21ecc5e,0x9686b3f,0x3ebaefc9,0x3c971814,0x6b6a70a1,0x687f3584,0x52a0e286,0xb79c5305,0xaa500737,0x3e07841c,0x7fdeae5c,0x8e7d44ec,0x5716f2b8,0xb03ada37,0xf0500c0d,0xf01c1f04,0x200b3ff,0xae0cf51a,0x3cb574b2,0x25837a58,0xdc0921bd,0xd19113f9,0x7ca92ff6,0x94324773,0x22f54701,0x3ae5e581,0x37c2dadc,0xc8b57634,0x9af3dda7,0xa9446146,0xfd0030e,0xecc8c73e,0xa4751e41,0xe238cd99,0x3bea0e2f,0x3280bba1,0x183eb331,0x4e548b38,0x4f6db908,0x6f420d03,0xf60a04bf,0x2cb81290,0x24977c79,0x5679b072,0xbcaf89af,0xde9a771f,0xd9930810,0xb38bae12,0xdccf3f2e,0x5512721f,0x2e6b7124,0x501adde6,0x9f84cd87,0x7a584718,0x7408da17,0xbc9f9abc,0xe94b7d8c,0xec7aec3a,0xdb851dfa,0x63094366,0xc464c3d2,0xef1c1847,0x3215d908,0xdd433b37,0x24c2ba16,0x12a14d43,0x2a65c451,0x50940002,0x133ae4dd,0x71dff89e,0x10314e55,0x81ac77d6,0x5f11199b,0x43556f1,0xd7a3c76b,0x3c11183b,0x5924a509,0xf28fe6ed,0x97f1fbfa,0x9ebabf2c,0x1e153c6e,0x86e34570,0xeae96fb1,0x860e5e0a,0x5a3e2ab3,0x771fe71c,0x4e3d06fa,0x2965dcb9,0x99e71d0f,0x803e89d6,0x5266c825,0x2e4cc978,0x9c10b36a,0xc6150eba,0x94e2ea78,0xa5fc3c53,0x1e0a2df4,0xf2f74ea7,0x361d2b3d,0x1939260f,0x19c27960,0x5223a708,0xf71312b6,0xebadfe6e,0xeac31f66,0xe3bc4595,0xa67bc883,0xb17f37d1,0x18cff28,0xc332ddef,0xbe6c5aa5,0x65582185,0x68ab9802,0xeecea50f,0xdb2f953b,0x2aef7dad,0x5b6e2f84,0x1521b628,0x29076170,0xecdd4775,0x619f1510,0x13cca830,0xeb61bd96,0x334fe1e,0xaa0363cf,0xb5735c90,0x4c70a239,0xd59e9e0b,0xcbaade14,0xeecc86bc,0x60622ca7,0x9cab5cab,0xb2f3846e,0x648b1eaf,0x19bdf0ca,0xa02369b9,0x655abb50,0x40685a32,0x3c2ab4b3,0x319ee9d5,0xc021b8f7,0x9b540b19,0x875fa099,0x95f7997e,0x623d7da8,0xf837889a,0x97e32d77,0x11ed935f,0x16681281,0xe358829,0xc7e61fd6,0x96dedfa1,0x7858ba99,0x57f584a5,0x1b227263,0x9b83c3ff,0x1ac24696,0xcdb30aeb,0x532e3054,0x8fd948e4,0x6dbc3128,0x58ebf2ef,0x34c6ffea,0xfe28ed61,0xee7c3c73,0x5d4a14d9,0xe864b7e3,0x42105d14,0x203e13e0,0x45eee2b6,0xa3aaabea,0xdb6c4f15,0xfacb4fd0,0xc742f442,0xef6abbb5,0x654f3b1d,0x41cd2105,0xd81e799e,0x86854dc7,0xe44b476a,0x3d816250,0xcf62a1f2,0x5b8d2646,0xfc8883a0,0xc1c7b6a3,0x7f1524c3,0x69cb7492,0x47848a0b,0x5692b285,0x95bbf00,0xad19489d,0x1462b174,0x23820e00,0x58428d2a,0xc55f5ea,0x1dadf43e,0x233f7061,0x3372f092,0x8d937e41,0xd65fecf1,0x6c223bdb,0x7cde3759,0xcbee7460,0x4085f2a7,0xce77326e,0xa6078084,0x19f8509e,0xe8efd855,0x61d99735,0xa969a7aa,0xc50c06c2,0x5a04abfc,0x800bcadc,0x9e447a2e,0xc3453484,0xfdd56705,0xe1e9ec9,0xdb73dbd3,0x105588cd,0x675fda79,0xe3674340,0xc5c43465,0x713e38d8,0x3d28f89e,0xf16dff20,0x153e21e7,0x8fb03d4a,0xe6e39f2b,0xdb83adf7,0xe93d5a68,0x948140f7,0xf64c261c,0x94692934,0x411520f7,0x7602d4f7,0xbcf46b2e,0xd4a20068,0xd4082471,0x3320f46a,0x43b7d4b7,0x500061af,0x1e39f62e,0x97244546,0x14214f74,0xbf8b8840,0x4d95fc1d,0x96b591af,0x70f4ddd3,0x66a02f45,0xbfbc09ec,0x3bd9785,0x7fac6dd0,0x31cb8504,0x96eb27b3,0x55fd3941,0xda2547e6,0xabca0a9a,0x28507825,0x530429f4,0xa2c86da,0xe9b66dfb,0x68dc1462,0xd7486900,0x680ec0a4,0x27a18dee,0x4f3ffea2,0xe887ad8c,0xb58ce006,0x7af4d6b6,0xaace1e7c,0xd3375fec,0xce78a399,0x406b2a42,0x20fe9e35,0xd9f385b9,0xee39d7ab,0x3b124e8b,0x1dc9faf7,0x4b6d1856,0x26a36631,0xeae397b2,0x3a6efa74,0xdd5b4332,0x6841e7f7,0xca7820fb,0xfb0af54e,0xd8feb397,0x454056ac,0xba489527,0x55533a3a,0x20838d87,0xfe6ba9b7,0xd096954b,0x55a867bc,0xa1159a58,0xcca92963,0x99e1db33,0xa62a4a56,0x3f3125f9,0x5ef47e1c,0x9029317c,0xfdf8e802,0x4272f70,0x80bb155c,0x5282ce3,0x95c11548,0xe4c66d22,0x48c1133f,0xc70f86dc,0x7f9c9ee,0x41041f0f,0x404779a4,0x5d886e17,0x325f51eb,0xd59bc0d1,0xf2bcc18f,0x41113564,0x257b7834,0x602a9c60,0xdff8e8a3,0x1f636c1b,0xe12b4c2,0x2e1329e,0xaf664fd1,0xcad18115,0x6b2395e0,0x333e92e1,0x3b240b62,0xeebeb922,0x85b2a20e,0xe6ba0d99,0xde720c8c,0x2da2f728,0xd0127845,0x95b794fd,0x647d0862,0xe7ccf5f0,0x5449a36f,0x877d48fa,0xc39dfd27,0xf33e8d1e,0xa476341,0x992eff74,0x3a6f6eab,0xf4f8fd37,0xa812dc60,0xa1ebddf8,0x991be14c,0xdb6e6b0d,0xc67b5510,0x6d672c37,0x2765d43b,0xdcd0e804,0xf1290dc7,0xcc00ffa3,0xb5390f92,0x690fed0b,0x667b9ffb,0xcedb7d9c,0xa091cf0b,0xd9155ea3,0xbb132f88,0x515bad24,0x7b9479bf,0x763bd6eb,0x37392eb3,0xcc115979,0x8026e297,0xf42e312d,0x6842ada7,0xc66a2b3b,0x12754ccc,0x782ef11c,0x6a124237,0xb79251e7,0x6a1bbe6,0x4bfb6350,0x1a6b1018,0x11caedfa,0x3d25bdd8,0xe2e1c3c9,0x44421659,0xa121386,0xd90cec6e,0xd5abea2a,0x64af674e,0xda86a85f,0xbebfe988,0x64e4c3fe,0x9dbc8057,0xf0f7c086,0x60787bf8,0x6003604d,0xd1fd8346,0xf6381fb0,0x7745ae04,0xd736fccc,0x83426b33,0xf01eab71,0xb0804187,0x3c005e5f,0x77a057be,0xbde8ae24,0x55464299,0xbf582e61,0x4e58f48f,0xf2ddfda2,0xf474ef38,0x8789bdc2,0x5366f9c3,0xc8b38e74,0xb475f255,0x46fcd9b9,0x7aeb2661,0x8b1ddf84,0x846a0e79,0x915f95e2,0x466e598e,0x20b45770,0x8cd55591,0xc902de4c,0xb90bace1,0xbb8205d0,0x11a86248,0x7574a99e,0xb77f19b6,0xe0a9dc09,0x662d09a1,0xc4324633,0xe85a1f02,0x9f0be8c,0x4a99a025,0x1d6efe10,0x1ab93d1d,0xba5a4df,0xa186f20f,0x2868f169,0xdcb7da83,0x573906fe,0xa1e2ce9b,0x4fcd7f52,0x50115e01,0xa70683fa,0xa002b5c4,0xde6d027,0x9af88c27,0x773f8641,0xc3604c06,0x61a806b5,0xf0177a28,0xc0f586e0,6314154,0x30dc7d62,0x11e69ed7,0x2338ea63,0x53c2dd94,0xc2c21634,0xbbcbee56,0x90bcb6de,0xebfc7da1,0xce591d76,0x6f05e409,0x4b7c0188,0x39720a3d,0x7c927c24,0x86e3725f,0x724d9db9,0x1ac15bb4,0xd39eb8fc,0xed545578,0x8fca5b5,0xd83d7cd3,0x4dad0fc4,0x1e50ef5e,0xb161e6f8,0xa28514d9,0x6c51133c,0x6fd5c7e7,0x56e14ec4,0x362abfce,0xddc6c837,0xd79a3234,0x92638212,0x670efa8e,0x406000e0,0x3a39ce37,0xd3faf5cf,0xabc27737,0x5ac52d1b,0x5cb0679e,0x4fa33742,0xd3822740,0x99bc9bbe,0xd5118e9d,0xbf0f7315,0xd62d1c7e,0xc700c47b,0xb78c1b6b,0x21a19045,0xb26eb1be,0x6a366eb4,0x5748ab2f,0xbc946e79,0xc6a376d2,0x6549c2c8,0x530ff8ee,0x468dde7d,0xd5730a1d,0x4cd04dc6,0x2939bbdb,0xa9ba4650,0xac9526e8,0xbe5ee304,0xa1fad5f0,0x6a2d519a,0x63ef8ce2,0x9a86ee22,0xc089c2b8,0x43242ef6,0xa51e03aa,0x9cf2d0a4,0x83c061ba,0x9be96a4d,0x8fe51550,0xba645bd6,0x2826a2f9,0xa73a3ae1,0x4ba99586,0xef5562e9,0xc72fefd3,0xf752f7da,0x3f046f69,0x77fa0a59,0x80e4a915,0x87b08601,0x9b09e6ad,0x3b3ee593,0xe990fd5a,0x9e34d797,0x2cf0b7d9,0x22b8b51,0x96d5ac3a,0x17da67d,0xd1cf3ed6,0x7c7d2d28,0x1f9f25cf,0xadf2b89b,0x5ad6b472,0x5a88f54c,0xe029ac71,0xe019a5e6,0x47b0acfd,0xed93fa9b,0xe8d3c48d,0x283b57cc,0xf8d56629,0x79132e28,0x785f0191,0xed756055,0xf7960e44,0xe3d35e8c,0x15056dd4,0x88f46dba,0x3a16125,0x564f0bd,0xc3eb9e15,0x3c9057a2,0x97271aec,0xa93a072a,0x1b3f6d9b,0x1e6321f5,0xf59c66fb,0x26dcf319,0x7533d928,0xb155fdf5,0x3563482,0x8aba3cbb,0x28517711,0xc20ad9f8,0xabcc5167,0xccad925f,0x4de81751,0x3830dc8e,0x379d5862,0x9320f991,0xea7a90c2,0xfb3e7bce,0x5121ce64,0x774fbe32,0xa8b6e37e,0xc3293d46,0x48de5369,0x6413e680,0xa2ae0810,0xdd6db224,0x69852dfd,0x9072166,0xb39a460a,0x6445c0dd,0x586cdecf,0x1c20c8ae,0x5bbef7dd,0x1b588d40,0xccd2017f,0x6bb4e3bb,0xdda26a7e,0x3a59ff45,0x3e350a44,0xbcb4cdd5,0x72eacea8,0xfa6484bb,0x8d6612ae,0xbf3c6f47,0xd29be463,0x542f5d9e,0xaec2771b,0xf64e6370,0x740e0d8d,0xe75b1357,0xf8721671,0xaf537d5d,0x4040cb08,0x4eb4e2cc,0x34d2466a,0x115af84,3786409e3,0x95983a1d,0x6b89fb4,0xce6ea048,0x6f3f3b82,0x3520ab82,0x11a1d4b,0x277227f8,0x611560b1,0xe7933fdc,0xbb3a792b,0x344525bd,0xa08839e1,0x51ce794b,0x2f32c9b7,0xa01fbac9,0xe01cc87e,0xbcc7d1f6,0xcf0111c3,0xa1e8aac7,0x1a908749,0xd44fbd9a,0xd0dadecb,0xd50ada38,0x339c32a,0xc6913667,0x8df9317c,0xe0b12b4f,0xf79e59b7,0x43f5bb3a,0xf2d519ff,0x27d9459c,0xbf97222c,0x15e6fc2a,0xf91fc71,0x9b941525,0xfae59361,0xceb69ceb,0xc2a86459,0x12baa8d1,0xb6c1075e,0xe3056a0c,0x10d25065,0xcb03a442,0xe0ec6e0e,0x1698db3b,0x4c98a0be,0x3278e964,0x9f1f9532,0xe0d392df,0xd3a0342b,0x8971f21e,0x1b0a7441,0x4ba3348c,0xc5be7120,0xc37632d8,0xdf359f8d,0x9b992f2e,0xe60b6f47,0xfe3f11d,0xe54cda54,0x1edad891,0xce6279cf,0xcd3e7e6f,0x1618b166,0xfd2c1d05,0x848fd2c5,0xf6fb2299,0xf523f357,0xa6327623,0x93a83531,0x56cccd02,0xacf08162,0x5a75ebb5,0x6e163697,0x88d273cc,0xde966292,0x81b949d0,0x4c50901b,0x71c65614,0xe6c6c7bd,0x327a140a,0x45e1d006,0xc3f27b9a,0xc9aa53fd,0x62a80f00,0xbb25bfe2,0x35bdd2f6,0x71126905,0xb2040222,0xb6cbcf7c,0xcd769c2b,0x53113ec0,0x1640e3d3,0x38abbd60,0x2547adf0,0xba38209c,0xf746ce76,0x77afa1c5,0x20756060,0x85cbfe4e,0x8ae88dd8,0x7aaaf9b0,0x4cf9aa7e,0x1948c25c,0x2fb8a8c,0x1c36ae4,0xd6ebe1f9,0x90d4f869,0xa65cdea0,0x3f09252d,0xc208e69f,0xb74e6132,0xce77e25b,0x578fdfe3,0x3ac372e6],cX=[0x4f727068,0x65616e42,0x65686f6c,0x64657253,0x63727944,0x6f756274];function cZ(e,t,r,n){var i,a=e[t],o=e[t+1];return a^=r[0],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[1],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[2],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[3],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[4],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[5],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[6],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[7],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[8],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[9],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[10],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[11],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[12],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[13],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[14],o^=(n[a>>>24]+n[256|a>>16&255]^n[512|a>>8&255])+n[768|255&a]^r[15],a^=(n[o>>>24]+n[256|o>>16&255]^n[512|o>>8&255])+n[768|255&o]^r[16],e[t]=o^r[17],e[t+1]=a,e}function cY(e,t){for(var r=0,n=0;r<4;++r)n=n<<8|255&e[t],t=(t+1)%e.length;return{key:n,offp:t}}function cQ(e,t,r){for(var n,i=0,a=[0,0],o=t.length,s=r.length,c=0;c<o;c++)i=(n=cY(e,i)).offp,t[c]=t[c]^n.key;for(c=0;c<o;c+=2)a=cZ(a,0,t,r),t[c]=a[0],t[c+1]=a[1];for(c=0;c<s;c+=2)a=cZ(a,0,t,r),r[c]=a[0],r[c+1]=a[1]}function c0(e,t,r,n,i){var a,o,s=cX.slice(),c=s.length;if(r<4||r>31){if(o=Error("Illegal number of rounds (4-31): "+r),n)return void cH(n.bind(this,o));throw o}if(t.length!==cV){if(o=Error("Illegal salt length: "+t.length+" != "+cV),n)return void cH(n.bind(this,o));throw o}r=1<<r>>>0;var l,u,d,p=0;function f(){if(i&&i(p/r),p<r)for(var a=Date.now();p<r&&(p+=1,cQ(e,l,u),cQ(t,l,u),!(Date.now()-a>100)););else{for(p=0;p<64;p++)for(d=0;d<c>>1;d++)cZ(s,d<<1,l,u);var o=[];for(p=0;p<c;p++)o.push((s[p]>>24&255)>>>0),o.push((s[p]>>16&255)>>>0),o.push((s[p]>>8&255)>>>0),o.push((255&s[p])>>>0);return n?void n(null,o):o}n&&cH(f)}if("function"==typeof Int32Array?(l=new Int32Array(cF),u=new Int32Array(cG)):(l=cF.slice(),u=cG.slice()),!function(e,t,r,n){for(var i,a=0,o=[0,0],s=r.length,c=n.length,l=0;l<s;l++)a=(i=cY(t,a)).offp,r[l]=r[l]^i.key;for(l=0,a=0;l<s;l+=2)a=(i=cY(e,a)).offp,o[0]^=i.key,a=(i=cY(e,a)).offp,o[1]^=i.key,o=cZ(o,0,r,n),r[l]=o[0],r[l+1]=o[1];for(l=0;l<c;l+=2)a=(i=cY(e,a)).offp,o[0]^=i.key,a=(i=cY(e,a)).offp,o[1]^=i.key,o=cZ(o,0,r,n),n[l]=o[0],n[l+1]=o[1]}(t,e,l,u),void 0!==n)f();else for(;;)if(void 0!==(a=f()))return a||[]}function c1(e,t,r,n){if("string"!=typeof e||"string"!=typeof t){if(i=Error("Invalid string / salt: Not a string"),r)return void cH(r.bind(this,i));throw i}if("$"!==t.charAt(0)||"2"!==t.charAt(1)){if(i=Error("Invalid salt version: "+t.substring(0,2)),r)return void cH(r.bind(this,i));throw i}if("$"===t.charAt(2))a="\0",o=3;else{if("a"!==(a=t.charAt(2))&&"b"!==a&&"y"!==a||"$"!==t.charAt(3)){if(i=Error("Invalid salt revision: "+t.substring(2,4)),r)return void cH(r.bind(this,i));throw i}o=4}if(t.charAt(o+2)>"$"){if(i=Error("Missing salt rounds"),r)return void cH(r.bind(this,i));throw i}var i,a,o,s=10*parseInt(t.substring(o,o+1),10)+parseInt(t.substring(o+1,o+2),10),c=t.substring(o+3,o+25),l=function(e){for(var t,r,n=0,i=Array(cW(e)),a=0,o=e.length;a<o;++a)(t=e.charCodeAt(a))<128?i[n++]=t:(t<2048?i[n++]=t>>6|192:((64512&t)==55296&&(64512&(r=e.charCodeAt(a+1)))==56320?(t=65536+((1023&t)<<10)+(1023&r),++a,i[n++]=t>>18|240,i[n++]=t>>12&63|128):i[n++]=t>>12|224,i[n++]=t>>6&63|128),i[n++]=63&t|128);return i}(e+=a>="a"?"\0":""),u=cJ(c,cV);function d(e){var t=[];return t.push("$2"),a>="a"&&t.push(a),t.push("$"),s<10&&t.push("0"),t.push(s.toString()),t.push("$"),t.push(cB(u,u.length)),t.push(cB(e,4*cX.length-1)),t.join("")}if(void 0===r)return d(c0(l,u,s));c0(l,u,s,function(e,t){e?r(e,null):r(null,d(t))},n)}let c2={setRandomFallback:function(e){cU=e},genSaltSync:cj,genSalt:cD,hashSync:cM,hash:c$,compareSync:function(e,t){if("string"!=typeof e||"string"!=typeof t)throw Error("Illegal arguments: "+typeof e+", "+typeof t);return 60===t.length&&cL(cM(e,t.substring(0,t.length-31)),t)},compare:function(e,t,r,n){function i(r){return"string"!=typeof e||"string"!=typeof t?void cH(r.bind(this,Error("Illegal arguments: "+typeof e+", "+typeof t))):60!==t.length?void cH(r.bind(this,null,!1)):void c$(e,t.substring(0,29),function(e,n){e?r(e):r(null,cL(n,t))},n)}if(!r)return new Promise(function(e,t){i(function(r,n){if(r)return void t(r);e(n)})});if("function"!=typeof r)throw Error("Illegal callback: "+typeof r);i(r)},getRounds:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return parseInt(e.split("$")[2],10)},getSalt:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);if(60!==e.length)throw Error("Illegal hash length: "+e.length+" != 60");return e.substring(0,29)},truncates:function(e){if("string"!=typeof e)throw Error("Illegal arguments: "+typeof e);return cW(e)>72},encodeBase64:function(e,t){return cB(e,t)},decodeBase64:function(e,t){return cJ(e,t)}},{handlers:c5,signIn:c6,signOut:c3,auth:c8}=function(e){if("function"==typeof e){let t=async t=>{let r=await e(t);return s0(r),sF(sQ(t),r)};return{handlers:{GET:t,POST:t},auth:cw(e,e=>s0(e)),signIn:async(t,r,n)=>{let i=await e(void 0);return s0(i),cR(t,r,n,i)},signOut:async t=>{let r=await e(void 0);return s0(r),cT(t,r)},unstable_update:async t=>{let r=await e(void 0);return s0(r),cP(t,r)}}}s0(e);let t=t=>sF(sQ(t),e);return{handlers:{GET:t,POST:t},auth:cw(e),signIn:(t,r,n)=>cR(t,r,n,e),signOut:t=>cT(t,e),unstable_update:t=>cP(t,e)}}({adapter:function(e){return{createUser:({id:t,...r})=>e.user.create(cO(r)),getUser:t=>e.user.findUnique({where:{id:t}}),getUserByEmail:t=>e.user.findUnique({where:{email:t}}),async getUserByAccount(t){let r=await e.account.findUnique({where:{provider_providerAccountId:t},include:{user:!0}});return r?.user??null},updateUser:({id:t,...r})=>e.user.update({where:{id:t},...cO(r)}),deleteUser:t=>e.user.delete({where:{id:t}}),linkAccount:t=>e.account.create({data:t}),unlinkAccount:t=>e.account.delete({where:{provider_providerAccountId:t}}),async getSessionAndUser(t){let r=await e.session.findUnique({where:{sessionToken:t},include:{user:!0}});if(!r)return null;let{user:n,...i}=r;return{user:n,session:i}},createSession:t=>e.session.create(cO(t)),updateSession:t=>e.session.update({where:{sessionToken:t.sessionToken},...cO(t)}),deleteSession:t=>e.session.delete({where:{sessionToken:t}}),async createVerificationToken(t){let r=await e.verificationToken.create(cO(t));return"id"in r&&r.id&&delete r.id,r},async useVerificationToken(t){try{let r=await e.verificationToken.delete({where:{identifier_token:t}});return"id"in r&&r.id&&delete r.id,r}catch(e){if(e instanceof cC.Prisma.PrismaClientKnownRequestError&&"P2025"===e.code)return null;throw e}},getAccount:async(t,r)=>e.account.findFirst({where:{providerAccountId:t,provider:r}}),createAuthenticator:async t=>e.authenticator.create(cO(t)),getAuthenticator:async t=>e.authenticator.findUnique({where:{credentialID:t}}),listAuthenticatorsByUserId:async t=>e.authenticator.findMany({where:{userId:t}}),updateAuthenticatorCounter:async(t,r)=>e.authenticator.update({where:{credentialID:t},data:{counter:r}})}}(cN),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[function(e){return{id:"google",name:"Google",type:"oidc",issuer:"https://accounts.google.com",style:{brandColor:"#1a73e8"},options:e}},{id:"credentials",name:"Credentials",type:"credentials",credentials:{},authorize:()=>null,options:{credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await cN.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!t||!t.password||!await c2.compare(e.password,t.password))return null;return{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}}}],callbacks:{async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,n=t.pathname.startsWith("/dashboard"),i=t.pathname.startsWith("/manuscripts"),a=t.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",t.pathname),!n&&!i&&!a||r},session:async({session:e,token:t})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",t),t?.sub&&(e.user.id=t.sub,t.name&&(e.user.name=t.name),t.email&&(e.user.email=t.email),t.picture&&(e.user.image=t.picture),t.role&&(e.user.role=t.role)),e),jwt:async({token:e,user:t,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",t),console.log("\uD83D\uDD10 JWT callback - account:",r),t&&(e.sub=t.id,e.name=t.name,e.email=t.email,e.picture=t.image,e.role=t.role),e)}}),c4=c8(e=>(console.log(`🔐 Middleware: ${e.nextUrl.pathname}, Auth:`,!!e.auth),F.next())),c9={matcher:["/((?!api|_next/static|_next/image|favicon.ico).*)"]};r(199);let c7={...l},le=c7.middleware||c7.default,lt="/middleware";if("function"!=typeof le)throw Object.defineProperty(Error(`The Middleware "${lt}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function lr(e){return to({...e,page:lt,handler:async(...e)=>{try{return await le(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await f(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},792:(e,t,r)=>{"use strict";r.d(t,{X:()=>function e(t){if((0,a.p)(t)||"object"==typeof t&&null!==t&&"digest"in t&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t.digest||(0,s.h)(t)||(0,o.I3)(t)||"object"==typeof t&&null!==t&&t.$$typeof===i||(0,n.T)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}});var n=r(801);let i=Symbol.for("react.postpone");var a=r(199),o=r(557),s=r(16)},801:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===i}r.d(t,{T:()=>n,W:()=>s});let i="HANGING_PROMISE_REJECTION";class a extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=i}}let o=new WeakMap;function s(e,t){if(e.aborted)return Promise.reject(new a(t));{let r=new Promise((r,n)=>{let i=n.bind(null,new a(t)),s=o.get(e);if(s)s.push(i);else{let t=[i];o.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(c),r}}function c(){}},802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var s=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,i,a,o){var s=r?r+e:e;if(!this._events[s])return!1;var c,l,u=this._events[s],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,a),!0;case 6:return u.fn.call(u.context,t,n,i,a,o),!0}for(l=1,c=Array(d-1);l<d;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var p,f=u.length;for(l=0;l<f;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),d){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,n);break;case 4:u[l].fn.call(u[l].context,t,n,i);break;default:if(!c)for(p=1,c=Array(d-1);p<d;p++)c[p-1]=arguments[p];u[l].fn.apply(u[l].context,c)}}return!0},s.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var s=this._events[a];if(s.fn)s.fn!==t||i&&!s.once||n&&s.context!==n||o(this,a);else{for(var c=0,l=[],u=s.length;c<u;c++)(s[c].fn!==t||i&&!s[c].once||n&&s[c].context!==n)&&l.push(s[c]);l.length?this._events[a]=1===l.length?l[0]:l:o(this,a)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let a=i/2|0,o=n+a;0>=r(e[o],t)?(n=++o,i-=a+1):i=a}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let a=(e,t,r)=>new Promise((a,o)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void a(e);let s=setTimeout(()=>{if("function"==typeof r){try{a(r())}catch(e){o(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,s=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),o(s)},t);n(e.then(a,o),()=>{clearTimeout(s)})});e.exports=a,e.exports.default=a,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,n),o=!1}finally{o&&delete r[e]}return a.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),a=()=>{},o=new t.TimeoutError;class s extends e{constructor(e){var t,n,i,o;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=a,this._resolveIdle=a,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(o=null==(i=e.interval)?void 0:i.toString())?o:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=a,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=a,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let a=async()=>{this._pendingCount++,this._intervalCount++;try{let a=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(o)});n(await a)}catch(e){i(e)}this._next()};this._queue.enqueue(a,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=s})(),e.exports=i})()},815:(e,t,r)=>{"use strict";e.exports=r(35)},821:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});var n=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({})},825:()=>{},830:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(0,r(58).xl)()},890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},a=t.split(n),o=(r||{}).decode||e,s=0;s<a.length;s++){var c=a[s],l=c.indexOf("=");if(!(l<0)){var u=c.substr(0,l).trim(),d=c.substr(++l,c.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return i},t.serialize=function(e,t,n){var a=n||{},o=a.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var c=e+"="+s;if(null!=a.maxAge){var l=a.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(a.domain){if(!i.test(a.domain))throw TypeError("option domain is invalid");c+="; Domain="+a.domain}if(a.path){if(!i.test(a.path))throw TypeError("option path is invalid");c+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(c+="; HttpOnly"),a.secure&&(c+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return a},wrapRequestHandler:function(){return o}});let n=r(201),i=r(552);function a(){return(0,i.interceptFetch)(r.g.fetch)}function o(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},956:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),a=r(930),o="context",s=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(o,e,a.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(o,a.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),a=r(957),o=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,o.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:a.DiagLogLevel.INFO})=>{var n,s,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let l=(0,o.getGlobal)("diag"),u=(0,i.createLogLevelDiagLogger)(null!=(s=r.logLevel)?s:a.DiagLogLevel.INFO,e);if(l&&!r.suppressOverrideMessage){let e=null!=(c=Error().stack)?c:"<failed to generate stacktrace>";l.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),a=r(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(o,e,a.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(o,a.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),a=r(194),o=r(277),s=r(369),c=r(930),l="propagation",u=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(l,e,c.DiagAPI.instance())}inject(e,t,r=a.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=a.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(l,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(l)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),a=r(139),o=r(607),s=r(930),c="trace";class l{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=a.wrapSpanContext,this.isSpanContextValid=a.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,s.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=l},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function a(e){return e.getValue(i)||void 0}t.getBaggage=a,t.getActiveBaggage=function(){return a(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),a=r(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:a.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return a("debug",this._namespace,e)}error(...e){return a("error",this._namespace,e)}info(...e){return a("info",this._namespace,e)}warn(...e){return a("warn",this._namespace,e)}verbose(...e){return a("verbose",this._namespace,e)}}function a(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),a=r(130),o=i.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var a;let o=c[s]=null!=(a=c[s])?a:{version:i.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(o.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return o[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=c[s])?void 0:t.version;if(n&&(0,a.isCompatible)(n))return null==(r=c[s])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=c[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function a(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let a={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=a.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||a.major!==s.major)return o(e);if(0===a.major)return a.minor===s.minor&&a.patch<=s.patch?(t.add(e),!0):o(e);return a.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=a,t.isCompatible=a(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class a extends n{add(e,t){}}t.NoopUpDownCounterMetric=a;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class c extends s{}t.NoopObservableCounterMetric=c;class l extends s{}t.NoopObservableGaugeMetric=l;class u extends s{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new a,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new l,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),a=r(403),o=r(139),s=n.ContextAPI.getInstance();class c{startSpan(e,t,r=s.active()){var n;if(null==t?void 0:t.root)return new a.NonRecordingSpan;let c=r&&(0,i.getSpanContext)(r);return"object"==typeof(n=c)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(c)?new a.NonRecordingSpan(c):new a.NonRecordingSpan}startActiveSpan(e,t,r,n){let a,o,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(a=t,c=r):(a=t,o=r,c=n);let l=null!=o?o:s.active(),u=this.startSpan(e,a,l),d=(0,i.setSpan)(l,u);return s.with(d,c,void 0,u)}}t.NoopTracer=c},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class a{getTracer(e,t,r){var i;return null!=(i=this.getDelegateTracer(e,t,r))?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=a},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),a=r(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function c(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(a.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return c(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let a=r.slice(0,i),o=r.slice(i+1,t.length);(0,n.validateKey)(a)&&(0,n.validateValue)(o)&&e.set(a,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,a=RegExp(`^(?:${n}|${i})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return a.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),a=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return a.test(e)&&e!==n.INVALID_TRACEID}function c(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=c,t.isSpanContextValid=function(e){return s(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var a=n[e]={exports:{}},o=!0;try{t[e].call(a.exports,a,a.exports,i),o=!1}finally{o&&delete n[e]}return a.exports}i.ab="//";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var o=i(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=i(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var c=i(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var l=i(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return l.ProxyTracer}});var u=i(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=i(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var p=i(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return p.SpanKind}});var f=i(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return f.SpanStatusCode}});var h=i(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return h.TraceFlags}});var g=i(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var b=i(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return b.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return b.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return b.isValidSpanId}});var m=i(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return m.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return m.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return m.INVALID_SPAN_CONTEXT}});let y=i(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return y.context}});let w=i(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return w.diag}});let v=i(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return v.metrics}});let x=i(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return x.propagation}});let _=i(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return _.trace}}),a.default={context:y.context,diag:w.diag,metrics:v.metrics,propagation:x.propagation,trace:_.trace}})(),e.exports=a})()}},e=>{var t=e(e.s=752);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map