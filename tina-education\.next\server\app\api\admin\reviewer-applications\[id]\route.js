"use strict";(()=>{var e={};e.id=6397,e.ids=[6397],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44451:(e,r,i)=>{i.r(r),i.d(r,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>v});var t={};i.r(t),i.d(t,{PATCH:()=>u});var a=i(96559),o=i(48088),n=i(37719),s=i(32190),p=i(56814),d=i(5956);let l=new(i(20549)).u(process.env.RESEND_API_KEY);async function u(e,{params:r}){try{let i=await (0,p.j2)();if(!i?.user?.id)return s.NextResponse.json({error:"Unauthorized"},{status:401});let t=await d.z.user.findUnique({where:{id:i.user.id},select:{role:!0,name:!0}});if(t?.role!=="ADMIN")return s.NextResponse.json({error:"Admin access required"},{status:403});let{id:a}=await r,{action:o}=await e.json();if(!["APPROVE","REJECT","UNDER_REVIEW"].includes(o))return s.NextResponse.json({error:"Invalid action"},{status:400});let n=await d.z.reviewerApplication.findUnique({where:{id:a},include:{user:{select:{id:!0,name:!0,email:!0,role:!0}}}});if(!n)return s.NextResponse.json({error:"Application not found"},{status:404});let u="APPROVE"===o?"APPROVED":"REJECT"===o?"REJECTED":"UNDER_REVIEW",c=await d.z.reviewerApplication.update({where:{id:a},data:{status:u,reviewedBy:i.user.id,reviewedAt:new Date,adminNotes:`${o.toLowerCase()} by ${t.name||i.user.email}`}});if("APPROVE"===o&&await d.z.user.update({where:{id:n.userId},data:{role:"REVIEWER"}}),await d.z.notification.create({data:{userId:n.userId,title:`Reviewer Application ${"APPROVE"===o?"Approved":"REJECT"===o?"Rejected":"Under Review"}`,message:"APPROVE"===o?"Congratulations! Your reviewer application has been approved. You now have reviewer access.":"REJECT"===o?"Your reviewer application has been reviewed. Please check your email for details.":"Your reviewer application is currently under detailed review.",type:"REVIEWER_APPLICATION_UPDATE",relatedId:n.id}}),process.env.RESEND_API_KEY)try{let e="APPROVE"===o?"Reviewer Application Approved - Welcome to Tina Education":"REJECT"===o?"Reviewer Application Update - Tina Education":"Reviewer Application Under Review - Tina Education",r="APPROVE"===o?`
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h2 style="color: #1e40af; margin: 0;">🎉 Congratulations! Your Application is Approved</h2>
              </div>
              
              <p>Dear ${n.user.name||"Reviewer"},</p>
              
              <p>We are pleased to inform you that your reviewer application has been <strong>approved</strong>!</p>
              
              <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #374151;">What's Next:</h3>
                <ul style="margin: 0; padding-left: 20px; color: #374151;">
                  <li>You now have reviewer access to the system</li>
                  <li>You will start receiving manuscript review assignments</li>
                  <li>Please review our reviewer guidelines and best practices</li>
                  <li>Maintain confidentiality and provide constructive feedback</li>
                </ul>
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${process.env.NEXTAUTH_URL||"http://localhost:3000"}/dashboard/reviews"
                   style="background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  Access Reviewer Dashboard
                </a>
              </div>
              
              <p>Thank you for joining our community of expert reviewers. Your expertise and insights will help maintain the quality of academic publications.</p>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
              <p style="color: #64748b; font-size: 14px;">
                Best regards,<br>
                Tina Education Team<br>
                <small>This is an automated notification.</small>
              </p>
            </div>
          `:"REJECT"===o?`
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h2 style="color: #dc2626; margin: 0;">📋 Reviewer Application Update</h2>
              </div>
              
              <p>Dear ${n.user.name||"Applicant"},</p>
              
              <p>Thank you for your interest in becoming a reviewer for Tina Education. After careful consideration, we are unable to approve your reviewer application at this time.</p>
              
              <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #374151;">Next Steps:</h3>
                <ul style="margin: 0; padding-left: 20px; color: #374151;">
                  <li>You may reapply in the future as your qualifications develop</li>
                  <li>Consider gaining more experience in your field of expertise</li>
                  <li>Continue contributing to the academic community</li>
                </ul>
              </div>
              
              <p>We appreciate your interest in contributing to the peer review process and encourage you to continue your academic pursuits.</p>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
              <p style="color: #64748b; font-size: 14px;">
                Best regards,<br>
                Tina Education Team<br>
                <small>This is an automated notification.</small>
              </p>
            </div>
          `:`
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h2 style="color: #1e40af; margin: 0;">🔍 Application Under Review</h2>
              </div>
              
              <p>Dear ${n.user.name||"Applicant"},</p>
              
              <p>Your reviewer application is currently under detailed review by our administrative team.</p>
              
              <p>We will notify you of our decision soon. Thank you for your patience.</p>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
              <p style="color: #64748b; font-size: 14px;">
                Best regards,<br>
                Tina Education Team<br>
                <small>This is an automated notification.</small>
              </p>
            </div>
          `;await l.emails.send({from:"Tina Education <<EMAIL>>",to:n.user.email,subject:e,html:r}),console.log(`✅ Email sent to applicant: ${n.user.email}`)}catch(e){console.error("Failed to send email notification:",e)}return s.NextResponse.json({message:`Application ${o.toLowerCase()}d successfully`,application:c})}catch(e){return console.error("Failed to update reviewer application:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/reviewer-applications/[id]/route",pathname:"/api/admin/reviewer-applications/[id]",filename:"route",bundlePath:"app/api/admin/reviewer-applications/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\admin\\reviewer-applications\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:v,serverHooks:x}=c;function f(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:v})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")},96330:e=>{e.exports=require("@prisma/client")},96559:(e,r,i)=>{e.exports=i(44870)}};var r=require("../../../../../webpack-runtime.js");r.C(e);var i=e=>r(r.s=e),t=r.X(0,[4447,2190,5663,9404,9468],()=>i(44451));module.exports=t})();