"use strict";(()=>{var e={};e.id=8686,e.ids=[8686],e.modules={2502:e=>{e.exports=import("prettier/plugins/html")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18163:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var i={};t.r(i),t.d(i,{POST:()=>u});var a=t(96559),o=t(48088),s=t(37719),n=t(32190),p=t(56814),d=t(5956);let l=new(t(20549)).u(process.env.RESEND_API_KEY);async function u(e){try{let r=await (0,p.j2)();if(!r?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{motivation:t,expertise:i,experience:a,qualifications:o,availability:s}=await e.json();if(!t?.trim())return n.NextResponse.json({error:"Motivation is required"},{status:400});if(!i?.trim())return n.NextResponse.json({error:"Areas of expertise are required"},{status:400});if(!o?.trim())return n.NextResponse.json({error:"Qualifications are required"},{status:400});let u=await d.z.user.findUnique({where:{id:r.user.id},select:{role:!0,name:!0,email:!0}});if(!u)return n.NextResponse.json({error:"User not found"},{status:404});if("REVIEWER"===u.role||"ADMIN"===u.role)return n.NextResponse.json({error:"You are already a reviewer or admin"},{status:400});if(await d.z.reviewerApplication.findUnique({where:{userId:r.user.id}}))return n.NextResponse.json({error:"You have already submitted an application"},{status:400});let c=await d.z.reviewerApplication.create({data:{userId:r.user.id,motivation:t.trim(),expertise:i.trim(),experience:a?.trim()||"",qualifications:o.trim(),availability:s?.trim()||"",status:"PENDING"}}),m=await d.z.user.findMany({where:{role:"ADMIN"},select:{id:!0,email:!0,name:!0}}),x=m.map(e=>d.z.notification.create({data:{userId:e.id,title:"New Reviewer Application",message:`${u.name||u.email} has submitted a reviewer application`,type:"REVIEWER_APPLICATION",relatedId:c.id}}));if(await Promise.all(x),process.env.RESEND_API_KEY&&m.length>0){let e=m.map(e=>l.emails.send({from:"Tina Education <<EMAIL>>",to:e.email,subject:"New Reviewer Application Submitted",html:`
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h2 style="color: #1e40af; margin: 0;">📝 New Reviewer Application</h2>
              </div>
              
              <p>Dear ${e.name||"Administrator"},</p>
              
              <p>A new reviewer application has been submitted and requires your review.</p>
              
              <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #374151;">Applicant Information:</h3>
                <p style="margin: 5px 0;"><strong>Name:</strong> ${u.name||"Not provided"}</p>
                <p style="margin: 5px 0;"><strong>Email:</strong> ${u.email}</p>
                <p style="margin: 5px 0;"><strong>Application ID:</strong> ${c.id}</p>
              </div>
              
              <div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h4 style="margin: 0 0 10px 0; color: #92400e;">Areas of Expertise:</h4>
                <p style="margin: 0; color: #92400e;">${i.substring(0,200)}${i.length>200?"...":""}</p>
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${process.env.NEXTAUTH_URL||"http://localhost:3000"}/dashboard/admin"
                   style="background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                  Review Application
                </a>
              </div>
              
              <p>Please log in to the admin dashboard to review the complete application and make a decision.</p>
              
              <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
              <p style="color: #64748b; font-size: 14px;">
                Best regards,<br>
                Tina Education System<br>
                <small>This is an automated notification.</small>
              </p>
            </div>
          `}));try{await Promise.all(e),console.log(`✅ Email notifications sent to ${m.length} admins`)}catch(e){console.error("Failed to send email notifications:",e)}}return n.NextResponse.json({message:"Application submitted successfully",application:{id:c.id,status:c.status,createdAt:c.createdAt}})}catch(e){return console.error("Failed to submit reviewer application:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/reviewer-application/route",pathname:"/api/reviewer-application",filename:"route",bundlePath:"app/api/reviewer-application/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviewer-application\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:g}=c;function v(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83505:e=>{e.exports=import("prettier/standalone")},84297:e=>{e.exports=require("async_hooks")},96330:e=>{e.exports=require("@prisma/client")},96559:(e,r,t)=>{e.exports=t(44870)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4447,2190,5663,9404,9468],()=>t(18163));module.exports=i})();