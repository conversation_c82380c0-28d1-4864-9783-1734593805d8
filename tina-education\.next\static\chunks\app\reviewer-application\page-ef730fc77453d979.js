(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4523],{5695:(e,r,a)=>{"use strict";var s=a(8999);a.o(s,"usePathname")&&a.d(r,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(r,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(r,{useSearchParams:function(){return s.useSearchParams}})},7092:(e,r,a)=>{"use strict";a.d(r,{default:()=>o});var s=a(5155),t=a(2115),i=a(5695);function o(e){let{user:r}=e,a=(0,i.useRouter)(),[o,n]=(0,t.useState)(!1),[l,c]=(0,t.useState)(""),[d,u]=(0,t.useState)({motivation:"",expertise:r.expertise||"",experience:"",qualifications:"",availability:""}),m=async e=>{if(e.preventDefault(),n(!0),c(""),!d.motivation.trim()){c("Please provide your motivation for becoming a reviewer"),n(!1);return}if(!d.expertise.trim()){c("Please describe your areas of expertise"),n(!1);return}if(!d.qualifications.trim()){c("Please provide your qualifications"),n(!1);return}try{let e=await fetch("/api/reviewer-application",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)}),r=await e.json();if(!e.ok)throw Error(r.error||"Failed to submit application");a.push("/dashboard/reviews?message=Application submitted successfully")}catch(e){c(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}},x=e=>{u({...d,[e.target.name]:e.target.value})};return(0,s.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[l&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:l}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Your Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-gray-700",children:"Name:"}),(0,s.jsx)("span",{className:"ml-2 text-gray-600",children:r.name||"Not provided"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-gray-700",children:"Email:"}),(0,s.jsx)("span",{className:"ml-2 text-gray-600",children:r.email})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("span",{className:"font-medium text-gray-700",children:"Current Affiliation:"}),(0,s.jsx)("span",{className:"ml-2 text-gray-600",children:r.affiliation||"Not provided"})]})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["You can update this information in your"," ",(0,s.jsx)("a",{href:"/profile",className:"text-blue-600 hover:text-blue-500",children:"profile page"}),"."]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"motivation",className:"block text-sm font-medium text-gray-700 mb-2",children:"Motivation for Becoming a Reviewer *"}),(0,s.jsx)("textarea",{id:"motivation",name:"motivation",rows:4,required:!0,value:d.motivation,onChange:x,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Explain why you want to become a reviewer and how you can contribute to the peer review process..."}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Describe your motivation and commitment to peer review (minimum 100 characters)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"expertise",className:"block text-sm font-medium text-gray-700 mb-2",children:"Areas of Expertise *"}),(0,s.jsx)("textarea",{id:"expertise",name:"expertise",rows:4,required:!0,value:d.expertise,onChange:x,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"List your specific areas of expertise, research interests, and fields you are qualified to review..."}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Be specific about your areas of specialization and research interests"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"experience",className:"block text-sm font-medium text-gray-700 mb-2",children:"Previous Review Experience"}),(0,s.jsx)("textarea",{id:"experience",name:"experience",rows:4,value:d.experience,onChange:x,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Describe any previous experience with peer review, including journals you've reviewed for, number of reviews completed, etc. If you're new to reviewing, mention any relevant experience..."}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Include any relevant experience, even if you're new to formal peer review"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"qualifications",className:"block text-sm font-medium text-gray-700 mb-2",children:"Academic and Professional Qualifications *"}),(0,s.jsx)("textarea",{id:"qualifications",name:"qualifications",rows:4,required:!0,value:d.qualifications,onChange:x,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"List your degrees, certifications, publications, professional positions, and other relevant qualifications..."}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Include degrees, publications, professional experience, and other relevant credentials"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"availability",className:"block text-sm font-medium text-gray-700 mb-2",children:"Availability and Commitment"}),(0,s.jsx)("textarea",{id:"availability",name:"availability",rows:3,value:d.availability,onChange:x,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Describe your availability for reviewing manuscripts, typical turnaround time you can commit to, and any scheduling constraints..."}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Help us understand your capacity and preferred review schedule"})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Reviewer Commitments"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-gray-700",children:[(0,s.jsxs)("label",{className:"flex items-start",children:[(0,s.jsx)("input",{type:"checkbox",required:!0,className:"mt-1 mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{children:"I commit to providing timely, thorough, and constructive reviews"})]}),(0,s.jsxs)("label",{className:"flex items-start",children:[(0,s.jsx)("input",{type:"checkbox",required:!0,className:"mt-1 mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{children:"I will maintain confidentiality of all manuscripts under review"})]}),(0,s.jsxs)("label",{className:"flex items-start",children:[(0,s.jsx)("input",{type:"checkbox",required:!0,className:"mt-1 mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{children:"I will declare any conflicts of interest and recuse myself when appropriate"})]}),(0,s.jsxs)("label",{className:"flex items-start",children:[(0,s.jsx)("input",{type:"checkbox",required:!0,className:"mt-1 mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("span",{children:"I understand that reviewer assignments are at the discretion of the editorial team"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,s.jsx)("a",{href:"/dashboard",className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:o,className:"px-6 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Submitting Application...":"Submit Application"})]})]})}},8116:(e,r,a)=>{Promise.resolve().then(a.bind(a,7092))}},e=>{var r=r=>e(e.s=r);e.O(0,[8441,1684,7358],()=>r(8116)),_N_E=e.O()}]);