(()=>{var e={};e.id=3651,e.ids=[3651],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma||new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29459:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>w,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{PATCH:()=>c});var a=r(96559),i=r(48088),n=r(37719),o=r(56814),u=r(5956),l=r(32190);async function c(e,{params:t}){let r=await (0,o.j2)();if(!r?.user?.id)return l.NextResponse.json({error:"Unauthorized"},{status:401});try{let s=await e.json(),{id:a}=await t,i=await u.z.review.findUnique({where:{id:a},select:{reviewer_id:!0,status:!0}});if(!i)return l.NextResponse.json({error:"Review not found"},{status:404});if(i.reviewer_id!==r.user.id)return l.NextResponse.json({error:"Forbidden"},{status:403});if(!["ACCEPTED","IN_REVIEW"].includes(i.status))return l.NextResponse.json({error:"Review cannot be updated in its current state"},{status:400});let n=await u.z.review.update({where:{id:a},data:{contentEvaluation:s.contentEvaluation,styleEvaluation:s.styleEvaluation,strengths:s.strengths,weaknesses:s.weaknesses,recommendation:s.recommendation,confidentialComments:s.confidentialComments,publicComments:s.publicComments,overallRating:s.overallRating,progressPercentage:s.progressPercentage,timeSpent:s.timeSpent,status:"IN_REVIEW",updatedAt:new Date}});return l.NextResponse.json({success:!0,message:"Review updated successfully",review:n})}catch(e){return console.error("Failed to update review:",e),l.NextResponse.json({error:"Failed to update review"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/reviews/[id]/update/route",pathname:"/api/reviews/[id]/update",filename:"route",bundlePath:"app/api/reviews/[id]/update/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\update\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:w}=d;function g(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,t,r)=>{"use strict";r.d(t,{Y9:()=>l,j2:()=>p});var s=r(19443),a=r(16467),i=r(5956),n=r(10189),o=r(56056),u=r(85663);let{handlers:l,signIn:c,signOut:d,auth:p}=(0,s.Ay)({adapter:(0,a.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[o.A,(0,n.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let t=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!t||!t.password||!await u.Ay.compare(e.password,t.password))return null;return{id:t.id,email:t.email,name:t.name,image:t.image,role:t.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:t}}){let r=!!e?.user,s=t.pathname.startsWith("/dashboard"),a=t.pathname.startsWith("/manuscripts"),i=t.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",t.pathname),!s&&!a&&!i||r},session:async({session:e,token:t})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",t),t?.sub&&(e.user.id=t.sub,t.name&&(e.user.name=t.name),t.email&&(e.user.email=t.email),t.picture&&(e.user.image=t.picture),t.role&&(e.user.role=t.role)),e),jwt:async({token:e,user:t,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",t),console.log("\uD83D\uDD10 JWT callback - account:",r),t&&(e.sub=t.id,e.name=t.name,e.email=t.email,e.picture=t.image,e.role=t.role),e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,2190,5663,9404],()=>r(29459));module.exports=s})();