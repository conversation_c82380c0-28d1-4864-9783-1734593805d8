(()=>{var e={};e.id=2975,e.ids=[2975],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,s,r)=>{"use strict";r.d(s,{z:()=>a});var t=r(96330);let a=globalThis.prisma||new t.PrismaClient},8658:(e,s,r)=>{"use strict";r.d(s,{default:()=>d});var t=r(60687),a=r(85814),i=r.n(a),l=r(16189),n=r(43210),c=r(69587);function o({href:e,icon:s,label:r,onClick:a}){let n=(0,l.usePathname)(),c=n===e||"/dashboard"!==e&&n.startsWith(e);return(0,t.jsx)("li",{className:"mb-1",children:(0,t.jsxs)(i(),{href:e,onClick:a,className:`flex items-center px-5 py-3 text-gray-800 font-medium hover:bg-blue-50 hover:text-blue-900 hover:border-l-3 hover:border-blue-900 transition-colors ${c?"bg-blue-50 text-blue-900 border-l-3 border-blue-900":""}`,children:[(0,t.jsx)("span",{className:"mr-3 text-lg",children:s}),(0,t.jsx)("span",{children:r})]})})}function d(){let[e,s]=(0,n.useState)(!1),r=()=>{s(!1)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("button",{id:"mobile-sidebar-toggle",onClick:()=>s(!e),className:"lg:hidden fixed top-24 left-4 z-50 p-2 bg-white rounded-md shadow-md border border-gray-200","aria-label":"Toggle sidebar menu",children:e?(0,t.jsx)(c.QCr,{className:"text-gray-600"}):(0,t.jsx)(c.OXb,{className:"text-gray-600"})}),e&&(0,t.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 top-20"}),(0,t.jsx)("aside",{id:"mobile-sidebar",className:`
          fixed lg:sticky w-64 bg-white shadow-md top-20 h-[calc(100vh-5rem)] overflow-y-auto border-r border-gray-200 flex-shrink-0 z-40
          transition-transform duration-300 ease-in-out
          ${e?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:(0,t.jsxs)("div",{className:"py-5",children:[(0,t.jsxs)("ul",{children:[(0,t.jsx)(o,{href:"/dashboard",icon:(0,t.jsx)(c.v$b,{}),label:"Dashboard",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/publications",icon:(0,t.jsx)(c.vd0,{}),label:"My Publications",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/manuscripts",icon:(0,t.jsx)(c.t69,{}),label:"Manuscripts",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/reviews",icon:(0,t.jsx)(c.kkc,{}),label:"Reviews",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/analytics",icon:(0,t.jsx)(c.YYR,{}),label:"Analytics",onClick:r})]}),(0,t.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Collaboration"}),(0,t.jsxs)("ul",{children:[(0,t.jsx)(o,{href:"/dashboard/collaborators",icon:(0,t.jsx)(c.YXz,{}),label:"Co-authors",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/messages",icon:(0,t.jsx)(c.uN,{}),label:"Messages",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/notifications",icon:(0,t.jsx)(c.jNV,{}),label:"Notifications",onClick:r})]})]}),(0,t.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Repository"}),(0,t.jsxs)("ul",{children:[(0,t.jsx)(o,{href:"/repository",icon:(0,t.jsx)(c.vd0,{}),label:"All Publications",onClick:r}),(0,t.jsx)(o,{href:"/books",icon:(0,t.jsx)(c.hko,{}),label:"Books",onClick:r}),(0,t.jsx)(o,{href:"/journals",icon:(0,t.jsx)(c.Nhm,{}),label:"Journals",onClick:r}),(0,t.jsx)(o,{href:"/articles",icon:(0,t.jsx)(c.Gp9,{}),label:"Articles",onClick:r})]})]}),(0,t.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Resources"}),(0,t.jsxs)("ul",{children:[(0,t.jsx)(o,{href:"/dashboard/library",icon:(0,t.jsx)(c.U$b,{}),label:"My Library",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/tools",icon:(0,t.jsx)(c.xdT,{}),label:"Writing Tools",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/calendar",icon:(0,t.jsx)(c.bfZ,{}),label:"Calendar",onClick:r}),(0,t.jsx)(o,{href:"/dashboard/settings",icon:(0,t.jsx)(c.Pcn,{}),label:"Settings",onClick:r})]})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12890:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46055:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},49682:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},54741:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>o});var t=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let o={children:["",{children:["dashboard",{children:["manuscripts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83004)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\manuscripts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83249)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\manuscripts\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/manuscripts/page",pathname:"/dashboard/manuscripts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,s,r)=>{"use strict";r.d(s,{Y9:()=>o,j2:()=>x});var t=r(19443),a=r(16467),i=r(5956),l=r(10189),n=r(56056),c=r(85663);let{handlers:o,signIn:d,signOut:u,auth:x}=(0,t.Ay)({adapter:(0,a.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[n.A,(0,l.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let s=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!s||!s.password||!await c.Ay.compare(e.password,s.password))return null;return{id:s.id,email:s.email,name:s.name,image:s.image,role:s.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:s}}){let r=!!e?.user,t=s.pathname.startsWith("/dashboard"),a=s.pathname.startsWith("/manuscripts"),i=s.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",s.pathname),!t&&!a&&!i||r},session:async({session:e,token:s})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",s),s?.sub&&(e.user.id=s.sub,s.name&&(e.user.name=s.name),s.email&&(e.user.email=s.email),s.picture&&(e.user.image=s.picture),s.role&&(e.user.role=s.role)),e),jwt:async({token:e,user:s,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",s),console.log("\uD83D\uDD10 JWT callback - account:",r),s&&(e.sub=s.id,e.name=s.name,e.email=s.email,e.picture=s.image,e.role=s.role),e)}})},60358:(e,s,r)=>{Promise.resolve().then(r.bind(r,84168))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},83004:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(37413),a=r(56814),i=r(5956),l=r(4536),n=r.n(l),c=r(61120),o=r(17328);async function d(e){try{let[s,r,t,a,l,n,c,o]=await Promise.all([i.z.manuscript.count({where:{author_id:e}}),i.z.manuscript.count({where:{author_id:e,status:"DRAFT"}}),i.z.manuscript.count({where:{author_id:e,status:"UNDER_REVIEW"}}),i.z.manuscript.count({where:{author_id:e,status:"ACCEPTED"}}),i.z.manuscript.count({where:{author_id:e,status:"REJECTED"}}),i.z.manuscript.count({where:{author_id:e,type:"JOURNAL"}}),i.z.manuscript.count({where:{author_id:e,type:"ARTICLE"}}),i.z.manuscript.count({where:{author_id:e,type:"BOOK"}})]);return{totalManuscripts:s,draftManuscripts:r,underReviewManuscripts:t,acceptedManuscripts:a,rejectedManuscripts:l,journalManuscripts:n,articleManuscripts:c,bookManuscripts:o}}catch(e){return console.error("Failed to fetch manuscript stats:",e),{totalManuscripts:0,draftManuscripts:0,underReviewManuscripts:0,acceptedManuscripts:0,rejectedManuscripts:0,journalManuscripts:0,articleManuscripts:0,bookManuscripts:0}}}async function u(){let e=await (0,a.j2)();if(!e?.user?.id)return null;let s=await d(e.user.id),r=[{label:"Total Manuscripts",value:s.totalManuscripts,icon:"\uD83D\uDCDD",color:"bg-blue-50 text-blue-900 border-blue-200"},{label:"Drafts",value:s.draftManuscripts,icon:"✏️",color:"bg-gray-50 text-gray-900 border-gray-200"},{label:"Under Review",value:s.underReviewManuscripts,icon:"\uD83D\uDC40",color:"bg-amber-50 text-amber-900 border-amber-200"},{label:"Accepted",value:s.acceptedManuscripts,icon:"✅",color:"bg-green-50 text-green-900 border-green-200"},{label:"Rejected",value:s.rejectedManuscripts,icon:"❌",color:"bg-red-50 text-red-900 border-red-200"}];return(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8",children:r.map((e,s)=>(0,t.jsxs)("div",{className:`bg-white rounded-lg shadow-sm border p-4 ${e.color}`,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("div",{className:"text-2xl",children:e.icon}),(0,t.jsx)("div",{className:"text-2xl font-bold",children:e.value})]}),(0,t.jsx)("div",{className:"text-sm font-medium",children:e.label})]},s))})}async function x(e){try{return await i.z.manuscript.findMany({where:{author_id:e},orderBy:{createdAt:"desc"},include:{user:{select:{name:!0,email:!0}},reviews:{select:{status:!0,reviewer_id:!0,createdAt:!0}}}})}catch(e){return console.error("Failed to fetch manuscripts:",e),[]}}function m(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e)}async function b(){let e=await (0,a.j2)();if(!e?.user?.id)return(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD12"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Please log in to view your manuscripts."})]});let s=await x(e.user.id);return 0===s.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDD"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Manuscripts Yet"}),(0,t.jsx)("p",{className:"text-gray-400 mb-6",children:"Start your academic journey by creating your first manuscript."}),(0,t.jsxs)(n(),{href:"/manuscripts/new",className:"inline-flex items-center px-6 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors font-medium",children:[(0,t.jsx)("span",{className:"mr-2",children:"+"}),"Create New Manuscript"]})]}):(0,t.jsx)("div",{className:"space-y-6",children:s.map(e=>{let s,r=function(e){let s=e.reviews.filter(e=>"PENDING"===e.status).length,r=e.reviews.filter(e=>"ACCEPTED"===e.status).length,t=e.reviews.filter(e=>"DECLINED"===e.status).length;if("ACCEPTED"===e.status)return{label:"Accepted",color:"bg-green-100 text-green-800 border-green-200",icon:"✅",description:`Accepted with ${r} positive reviews`};if("UNDER_REVIEW"===e.status)return{label:"Under Review",color:"bg-blue-100 text-blue-800 border-blue-200",icon:"\uD83D\uDC40",description:`${s} pending, ${r} accepted reviews`};if("REJECTED"===e.status)return{label:"Rejected",color:"bg-red-100 text-red-800 border-red-200",icon:"❌",description:`Rejected after ${t} reviews`};if("SUBMITTED"===e.status)return{label:"Submitted",color:"bg-purple-100 text-purple-800 border-purple-200",icon:"\uD83D\uDCE4",description:"Submitted and awaiting review assignment"};if("DRAFT"===e.status)return{label:"Draft",color:"bg-gray-100 text-gray-800 border-gray-200",icon:"\uD83D\uDCDD",description:"Work in progress - not submitted"};else return{label:"Unknown",color:"bg-amber-100 text-amber-800 border-amber-200",icon:"⚠️",description:"Status unknown"}}(e),a=function(e){switch(e){case"JOURNAL":return{icon:(0,t.jsx)(o.Gp9,{}),label:"Journal Article",color:"bg-blue-50 text-blue-700"};case"ARTICLE":return{icon:(0,t.jsx)(o.t69,{}),label:"Article",color:"bg-green-50 text-green-700"};case"BOOK":return{icon:(0,t.jsx)(o.vd0,{}),label:"Book",color:"bg-purple-50 text-purple-700"};case"EBOOK":return{icon:(0,t.jsx)(o.W_G,{}),label:"E-book",color:"bg-orange-50 text-orange-700"};case"AUDIOBOOK":return{icon:(0,t.jsx)(o.tLl,{}),label:"Audiobook",color:"bg-indigo-50 text-indigo-700"};default:return{icon:(0,t.jsx)(o.Gp9,{}),label:"Document",color:"bg-gray-50 text-gray-700"}}}(e.type),i=(s=0,e.title&&e.abstract&&(s+=20),e.content&&e.content.length>100&&(s+=40),e.keywords&&(s+=20),"DRAFT"!==e.status&&(s+=20),Math.min(s,100));return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 lg:p-6 hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4 space-y-3 lg:space-y-0",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-3 mb-2 space-y-2 sm:space-y-0",children:[(0,t.jsx)("h3",{className:"text-lg lg:text-xl font-bold text-gray-900",children:e.title}),(0,t.jsxs)("span",{className:`px-2 py-1 rounded-full text-xs font-medium border ${r.color} self-start sm:self-auto`,children:[r.icon," ",r.label]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-gray-400 mb-3 space-y-1 sm:space-y-0",children:[(0,t.jsxs)("span",{className:`px-2 py-1 rounded ${a.color} self-start`,children:[a.icon," ",a.label]}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,t.jsxs)("span",{children:["Created ",m(e.createdAt)]}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,t.jsx)("span",{className:"text-xs sm:text-sm",children:r.description})]})]}),(0,t.jsxs)("div",{className:"text-left lg:text-right",children:[(0,t.jsx)("div",{className:"text-sm text-gray-400 mb-1",children:"Progress"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-16 lg:w-20 bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${i}%`}})}),(0,t.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[i,"%"]})]})]})]}),e.abstract&&(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsx)("p",{className:"text-gray-700 leading-relaxed line-clamp-2",children:e.abstract})}),e.keywords&&(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.keywords.split(",").slice(0,5).map((e,s)=>(0,t.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm",children:e.trim()},s)),e.keywords.split(",").length>5&&(0,t.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-400 rounded text-sm",children:["+",e.keywords.split(",").length-5," more"]})]})}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between pt-4 border-t border-gray-200 space-y-3 lg:space-y-0",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 text-sm text-gray-400 space-y-1 sm:space-y-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(o.v$b,{}),(0,t.jsxs)("span",{children:[e.reviews.length," reviews"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(o.bfZ,{}),(0,t.jsxs)("span",{children:["Updated ",m(e.updatedAt)]})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-3 space-y-2 sm:space-y-0",children:[(0,t.jsx)(n(),{href:`/manuscripts/${e.id}/edit`,className:"text-blue-900 hover:text-blue-700 font-medium text-sm text-center sm:text-left",children:"Edit"}),"DRAFT"!==e.status&&(0,t.jsx)(n(),{href:`/manuscripts/${e.id}`,className:"text-blue-900 hover:text-blue-700 font-medium text-sm text-center sm:text-left",children:"View Details"}),"DRAFT"===e.status&&(0,t.jsx)(n(),{href:`/manuscripts/${e.id}/submit`,className:"px-3 py-2 bg-blue-900 text-white rounded text-sm hover:bg-blue-800 transition-colors text-center",children:"Submit for Review"})]})]})]},e.id)})})}function h(){return(0,t.jsx)("div",{className:"space-y-6",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mb-2"}),(0,t.jsxs)("div",{className:"flex space-x-4 mb-3",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-40"})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mb-1"}),(0,t.jsx)("div",{className:"h-2 bg-gray-200 rounded w-24"})]})]}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded mb-4"}),(0,t.jsxs)("div",{className:"flex space-x-2 mb-4",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-20"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-18"})]}),(0,t.jsxs)("div",{className:"flex justify-between pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex space-x-6",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24"})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-12"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20"})]})]})]},s))})}function p(){return(0,t.jsxs)("div",{className:"p-4 lg:p-8 min-h-full",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl lg:text-3xl font-bold text-gray-900 mb-2",children:"My Manuscripts"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm lg:text-base",children:"Manage your academic manuscripts and track their progress"})]}),(0,t.jsxs)(n(),{href:"/manuscripts/new",className:"inline-flex items-center justify-center px-4 lg:px-6 py-2 lg:py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors font-medium text-sm lg:text-base",children:[(0,t.jsx)("span",{className:"mr-2",children:"+"}),"New Manuscript"]})]})}),(0,t.jsx)(c.Suspense,{fallback:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4 animate-pulse",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"})]}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20"})]},s))}),children:(0,t.jsx)(u,{})}),(0,t.jsx)(c.Suspense,{fallback:(0,t.jsx)(h,{}),children:(0,t.jsx)(b,{})})]})}},83249:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(37413),a=r(84168);async function i({children:e}){return(0,t.jsx)("div",{className:"bg-gray-100 pt-20",children:(0,t.jsxs)("div",{className:"flex min-h-[calc(100vh-5rem)]",children:[(0,t.jsx)(a.default,{}),(0,t.jsx)("div",{className:"flex-1 lg:ml-0 w-full lg:w-auto",children:e})]})})}},84168:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\Sidebar.tsx","default")},94862:(e,s,r)=>{Promise.resolve().then(r.bind(r,8658))},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,2190,5663,9404,7945,1658,7328,1795],()=>r(54741));module.exports=t})();