(()=>{var e={};e.id=1833,e.ids=[1833],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4769:(e,s,r)=>{"use strict";r.d(s,{default:()=>n});var t=r(60687),a=r(43210),i=r(16189);function n({userId:e,currentRole:s,userName:r}){let n=(0,i.useRouter)(),[l,o]=(0,a.useState)(!1),[d,c]=(0,a.useState)(""),x=async t=>{if(!l&&t!==s&&confirm(`Are you sure you want to change ${r}'s role from ${s} to ${t}?`)){o(!0),c("");try{let s=await fetch(`/api/admin/users/${e}/role`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({role:t})}),r=await s.json();if(!s.ok)throw Error(r.error||"Failed to update user role");n.refresh()}catch(e){c(e instanceof Error?e.message:"An error occurred")}finally{o(!1)}}};return(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[d&&(0,t.jsx)("span",{className:"text-xs text-red-600 mr-2",children:d}),"USER"!==s&&(0,t.jsx)("button",{onClick:()=>x("USER"),disabled:l,className:"px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50",title:"Make User",children:l?"...":"\uD83D\uDC64"}),"REVIEWER"!==s&&(0,t.jsx)("button",{onClick:()=>x("REVIEWER"),disabled:l,className:"px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50",title:"Make Reviewer",children:l?"...":"\uD83D\uDCD6"}),"ADMIN"!==s&&(0,t.jsx)("button",{onClick:()=>x("ADMIN"),disabled:l,className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50",title:"Make Admin",children:l?"...":"\uD83D\uDC51"})]})}},5956:(e,s,r)=>{"use strict";r.d(s,{z:()=>a});var t=r(96330);let a=globalThis.prisma||new t.PrismaClient},8658:(e,s,r)=>{"use strict";r.d(s,{default:()=>c});var t=r(60687),a=r(85814),i=r.n(a),n=r(16189),l=r(43210),o=r(69587);function d({href:e,icon:s,label:r,onClick:a}){let l=(0,n.usePathname)(),o=l===e||"/dashboard"!==e&&l.startsWith(e);return(0,t.jsx)("li",{className:"mb-1",children:(0,t.jsxs)(i(),{href:e,onClick:a,className:`flex items-center px-5 py-3 text-gray-800 font-medium hover:bg-blue-50 hover:text-blue-900 hover:border-l-3 hover:border-blue-900 transition-colors ${o?"bg-blue-50 text-blue-900 border-l-3 border-blue-900":""}`,children:[(0,t.jsx)("span",{className:"mr-3 text-lg",children:s}),(0,t.jsx)("span",{children:r})]})})}function c(){let[e,s]=(0,l.useState)(!1),r=()=>{s(!1)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("button",{id:"mobile-sidebar-toggle",onClick:()=>s(!e),className:"lg:hidden fixed top-24 left-4 z-50 p-2 bg-white rounded-md shadow-md border border-gray-200","aria-label":"Toggle sidebar menu",children:e?(0,t.jsx)(o.QCr,{className:"text-gray-600"}):(0,t.jsx)(o.OXb,{className:"text-gray-600"})}),e&&(0,t.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 top-20"}),(0,t.jsx)("aside",{id:"mobile-sidebar",className:`
          fixed lg:sticky w-64 bg-white shadow-md top-20 h-[calc(100vh-5rem)] overflow-y-auto border-r border-gray-200 flex-shrink-0 z-40
          transition-transform duration-300 ease-in-out
          ${e?"translate-x-0":"-translate-x-full lg:translate-x-0"}
        `,children:(0,t.jsxs)("div",{className:"py-5",children:[(0,t.jsxs)("ul",{children:[(0,t.jsx)(d,{href:"/dashboard",icon:(0,t.jsx)(o.v$b,{}),label:"Dashboard",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/publications",icon:(0,t.jsx)(o.vd0,{}),label:"My Publications",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/manuscripts",icon:(0,t.jsx)(o.t69,{}),label:"Manuscripts",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/reviews",icon:(0,t.jsx)(o.kkc,{}),label:"Reviews",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/analytics",icon:(0,t.jsx)(o.YYR,{}),label:"Analytics",onClick:r})]}),(0,t.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Collaboration"}),(0,t.jsxs)("ul",{children:[(0,t.jsx)(d,{href:"/dashboard/collaborators",icon:(0,t.jsx)(o.YXz,{}),label:"Co-authors",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/messages",icon:(0,t.jsx)(o.uN,{}),label:"Messages",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/notifications",icon:(0,t.jsx)(o.jNV,{}),label:"Notifications",onClick:r})]})]}),(0,t.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Repository"}),(0,t.jsxs)("ul",{children:[(0,t.jsx)(d,{href:"/repository",icon:(0,t.jsx)(o.vd0,{}),label:"All Publications",onClick:r}),(0,t.jsx)(d,{href:"/books",icon:(0,t.jsx)(o.hko,{}),label:"Books",onClick:r}),(0,t.jsx)(d,{href:"/journals",icon:(0,t.jsx)(o.Nhm,{}),label:"Journals",onClick:r}),(0,t.jsx)(d,{href:"/articles",icon:(0,t.jsx)(o.Gp9,{}),label:"Articles",onClick:r})]})]}),(0,t.jsxs)("div",{className:"mt-5 pt-5 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"px-5 mb-4 text-gray-400 text-sm uppercase",children:"Resources"}),(0,t.jsxs)("ul",{children:[(0,t.jsx)(d,{href:"/dashboard/library",icon:(0,t.jsx)(o.U$b,{}),label:"My Library",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/tools",icon:(0,t.jsx)(o.xdT,{}),label:"Writing Tools",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/calendar",icon:(0,t.jsx)(o.bfZ,{}),label:"Calendar",onClick:r}),(0,t.jsx)(d,{href:"/dashboard/settings",icon:(0,t.jsx)(o.Pcn,{}),label:"Settings",onClick:r})]})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39099:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\admin\\\\ReviewerApplicationActions.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\ReviewerApplicationActions.tsx","default")},44125:(e,s,r)=>{"use strict";r.d(s,{default:()=>n});var t=r(60687),a=r(43210),i=r(16189);function n({applicationId:e,currentStatus:s,applicantName:r}){let n=(0,i.useRouter)(),[l,o]=(0,a.useState)(!1),[d,c]=(0,a.useState)(""),x=async s=>{if(!l&&confirm("APPROVE"===s?`Are you sure you want to approve ${r}'s reviewer application?`:"REJECT"===s?`Are you sure you want to reject ${r}'s reviewer application?`:`Mark ${r}'s application as under review?`)){o(!0),c("");try{let r=await fetch(`/api/admin/reviewer-applications/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:s})}),t=await r.json();if(!r.ok)throw Error(t.error||"Failed to update application");n.refresh()}catch(e){c(e instanceof Error?e.message:"An error occurred")}finally{o(!1)}}};return"APPROVED"===s||"REJECTED"===s?(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"APPROVED"===s?"✅ Approved":"❌ Rejected"}):(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[d&&(0,t.jsx)("span",{className:"text-xs text-red-600",children:d}),"PENDING"===s&&(0,t.jsx)("button",{onClick:()=>x("UNDER_REVIEW"),disabled:l,className:"px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50",children:l?"...":"Review"}),(0,t.jsx)("button",{onClick:()=>x("APPROVE"),disabled:l,className:"px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:opacity-50",children:l?"...":"Approve"}),(0,t.jsx)("button",{onClick:()=>x("REJECT"),disabled:l,className:"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50",children:l?"...":"Reject"})]})}},46055:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},46653:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d={children:["",{children:["dashboard",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,77319)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83249)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,55529)),"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\dashboard\\admin\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/admin/page",pathname:"/dashboard/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},49207:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\admin\\\\UserRoleActions.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\admin\\UserRoleActions.tsx","default")},50689:(e,s,r)=>{Promise.resolve().then(r.bind(r,39099)),Promise.resolve().then(r.bind(r,49207)),Promise.resolve().then(r.t.bind(r,49603,23))},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,s,r)=>{"use strict";r.d(s,{Y9:()=>d,j2:()=>m});var t=r(19443),a=r(16467),i=r(5956),n=r(10189),l=r(56056),o=r(85663);let{handlers:d,signIn:c,signOut:x,auth:m}=(0,t.Ay)({adapter:(0,a.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[l.A,(0,n.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let s=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!s||!s.password||!await o.Ay.compare(e.password,s.password))return null;return{id:s.id,email:s.email,name:s.name,image:s.image,role:s.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:s}}){let r=!!e?.user,t=s.pathname.startsWith("/dashboard"),a=s.pathname.startsWith("/manuscripts"),i=s.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",s.pathname),!t&&!a&&!i||r},session:async({session:e,token:s})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",s),s?.sub&&(e.user.id=s.sub,s.name&&(e.user.name=s.name),s.email&&(e.user.email=s.email),s.picture&&(e.user.image=s.picture),s.role&&(e.user.role=s.role)),e),jwt:async({token:e,user:s,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",s),console.log("\uD83D\uDD10 JWT callback - account:",r),s&&(e.sub=s.id,e.name=s.name,e.email=s.email,e.picture=s.image,e.role=s.role),e)}})},60358:(e,s,r)=>{Promise.resolve().then(r.bind(r,84168))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77319:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>v});var t=r(37413),a=r(56814),i=r(5956),n=r(39916),l=r(61120),o=r(17328);async function d(){try{let[e,s,r,t,a,n,l]=await Promise.all([i.z.user.count(),i.z.user.count({where:{role:"REVIEWER"}}),i.z.manuscript.count(),i.z.review.count({where:{status:"PENDING"}}),i.z.reviewerApplication.count({where:{status:"PENDING"}}),i.z.manuscript.count({where:{createdAt:{gte:new Date(new Date().getFullYear(),new Date().getMonth(),1)}}}),i.z.review.count({where:{status:"REVIEW_SUBMITTED",updatedAt:{gte:new Date(new Date().getFullYear(),new Date().getMonth(),1)}}})]);return{totalUsers:e,totalReviewers:s,totalManuscripts:r,pendingReviews:t,pendingApplications:a,manuscriptsThisMonth:n,reviewsThisMonth:l}}catch(e){return console.error("Failed to fetch admin stats:",e),{totalUsers:0,totalReviewers:0,totalManuscripts:0,pendingReviews:0,pendingApplications:0,manuscriptsThisMonth:0,reviewsThisMonth:0}}}async function c(){let e=await d(),s=[{title:"Total Users",value:e.totalUsers,icon:(0,t.jsx)(o.YXz,{}),color:"bg-blue-50 text-blue-700",change:`+${e.totalUsers>0?Math.round(e.totalUsers/100*5):0} this month`},{title:"Active Reviewers",value:e.totalReviewers,icon:(0,t.jsx)(o.hko,{}),color:"bg-green-50 text-green-700",change:`${Math.round(e.totalReviewers/Math.max(e.totalUsers,1)*100)}% of users`},{title:"Total Manuscripts",value:e.totalManuscripts,icon:(0,t.jsx)(o.Gp9,{}),color:"bg-purple-50 text-purple-700",change:`+${e.manuscriptsThisMonth} this month`},{title:"Pending Reviews",value:e.pendingReviews,icon:(0,t.jsx)(o.w_X,{}),color:e.pendingReviews>0?"bg-yellow-50 text-yellow-700":"bg-gray-50 text-gray-700",change:`${e.reviewsThisMonth} completed this month`}];return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.map((e,s)=>(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.title}),(0,t.jsx)("p",{className:"text-3xl font-bold text-gray-900 mt-1",children:e.value.toLocaleString()}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.change})]}),(0,t.jsx)("div",{className:`p-3 rounded-full ${e.color}`,children:(0,t.jsx)("span",{className:"text-2xl",children:e.icon})})]})},s)),(0,t.jsxs)("div",{className:"md:col-span-2 lg:col-span-4 grid grid-cols-1 md:grid-cols-3 gap-6 mt-4",children:[e.pendingApplications>0&&(0,t.jsx)("div",{className:"bg-orange-50 border border-orange-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:(0,t.jsx)(o.kkc,{})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-medium text-orange-900",children:[e.pendingApplications," Pending Application",1!==e.pendingApplications?"s":""]}),(0,t.jsx)("p",{className:"text-sm text-orange-700",children:"Reviewer applications awaiting review"})]})]})}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:(0,t.jsx)(o.CMH,{})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-green-900",children:"System Healthy"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"All services operational"})]})]})}),(0,t.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:(0,t.jsx)(o.v$b,{})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:"Activity This Month"}),(0,t.jsxs)("p",{className:"text-sm text-gray-700",children:[e.manuscriptsThisMonth," submissions,"," ",e.reviewsThisMonth," reviews"]})]})]})})]})]})}var x=r(39099);async function m(){try{return await i.z.reviewerApplication.findMany({where:{status:{in:["PENDING","UNDER_REVIEW"]}},include:{user:{select:{id:!0,name:!0,email:!0,affiliation:!0}}},orderBy:{createdAt:"desc"},take:10})}catch(e){return console.error("Failed to fetch reviewer applications:",e),[]}}async function u(){let e=await m();return 0===e.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-3",children:"\uD83D\uDCCB"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Pending Applications"}),(0,t.jsx)("p",{className:"text-gray-500",children:"All reviewer applications have been processed."})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[e.map(e=>{var s;let r=function(e){switch(e){case"PENDING":return{label:"Pending",color:"bg-yellow-100 text-yellow-800 border-yellow-200",icon:(0,t.jsx)(o.w_X,{})};case"UNDER_REVIEW":return{label:"Under Review",color:"bg-blue-100 text-blue-800 border-blue-200",icon:(0,t.jsx)(o.KSO,{})};case"APPROVED":return{label:"Approved",color:"bg-green-100 text-green-800 border-green-200",icon:(0,t.jsx)(o.CMH,{})};case"REJECTED":return{label:"Rejected",color:"bg-red-100 text-red-800 border-red-200",icon:(0,t.jsx)(o.QCr,{})};default:return{label:"Unknown",color:"bg-gray-100 text-gray-800 border-gray-200",icon:(0,t.jsx)(o.qQC,{})}}}(e.status);return(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.user.name||"Unnamed User"}),(0,t.jsxs)("span",{className:`px-2 py-1 rounded-full text-xs font-medium border ${r.color}`,children:[r.icon," ",r.label]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:e.user.email}),e.user.affiliation&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.user.affiliation})]}),(0,t.jsxs)("div",{className:"text-right text-sm text-gray-500",children:[(0,t.jsx)("p",{children:"Applied"}),(0,t.jsx)("p",{children:(s=e.createdAt,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(s))})]})]}),(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-1",children:"Areas of Expertise:"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.expertise})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h5",{className:"text-sm font-medium text-gray-700 mb-1",children:"Motivation:"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.motivation})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between pt-3 border-t border-gray-200",children:[(0,t.jsx)("a",{href:`/dashboard/admin/applications/${e.id}`,className:"text-sm text-blue-600 hover:text-blue-500 font-medium",children:"View Full Application"}),(0,t.jsx)(x.default,{applicationId:e.id,currentStatus:e.status,applicantName:e.user.name||e.user.email})]})]},e.id)}),(0,t.jsx)("div",{className:"text-center pt-4 border-t border-gray-200",children:(0,t.jsx)("a",{href:"/dashboard/admin/applications",className:"text-sm text-blue-600 hover:text-blue-500 font-medium",children:"View All Applications →"})})]})}var h=r(49207),p=r(53384);async function b(){try{return await i.z.user.findMany({select:{id:!0,name:!0,email:!0,image:!0,role:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10})}catch(e){return console.error("Failed to fetch recent users:",e),[]}}async function g(){let e=await b();return 0===e.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-3",children:"\uD83D\uDC65"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Users Found"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No users have been registered yet."})]}):(0,t.jsxs)("div",{className:"space-y-3",children:[e.map(e=>{var s;let r=function(e){switch(e){case"ADMIN":return{label:"Admin",color:"bg-red-100 text-red-800",icon:"\uD83D\uDC51"};case"REVIEWER":return{label:"Reviewer",color:"bg-blue-100 text-blue-800",icon:"\uD83D\uDCD6"};case"USER":return{label:"User",color:"bg-gray-100 text-gray-800",icon:"\uD83D\uDC64"};default:return{label:"Unknown",color:"bg-gray-100 text-gray-800",icon:"❓"}}}(e.role);return(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:shadow-sm transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center",children:e.image?(0,t.jsx)(p.default,{src:e.image,alt:e.name||"User",width:32,height:32,className:"w-8 h-8 rounded-full object-cover"}):(0,t.jsx)("span",{className:"text-sm",children:r.icon})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.name||"Unnamed User"}),(0,t.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${r.color}`,children:r.label})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.email}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["Joined ",(s=e.createdAt,new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(s))]})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)(h.default,{userId:e.id,currentRole:e.role,userName:e.name||e.email})})]},e.id)}),(0,t.jsx)("div",{className:"text-center pt-4 border-t border-gray-200",children:(0,t.jsx)("a",{href:"/dashboard/admin/users",className:"text-sm text-blue-600 hover:text-blue-500 font-medium",children:"View All Users →"})})]})}async function j(e){try{let s=await i.z.user.findUnique({where:{id:e},select:{role:!0}});return s?.role==="ADMIN"}catch(e){return console.error("Failed to check admin access:",e),!1}}async function v(){let e=await (0,a.j2)();return(e?.user?.id||(0,n.redirect)("/auth/signin"),await j(e.user.id))?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Admin Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage users, review applications, and monitor system statistics"})]}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(l.Suspense,{fallback:(0,t.jsx)(f,{}),children:(0,t.jsx)(c,{})})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Reviewer Applications"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Review and approve pending applications"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(l.Suspense,{fallback:(0,t.jsx)(y,{}),children:(0,t.jsx)(u,{})})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,t.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"User Management"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Manage user roles and permissions"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)(l.Suspense,{fallback:(0,t.jsx)(N,{}),children:(0,t.jsx)(g,{})})})]})]}),(0,t.jsxs)("div",{className:"mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsxs)("a",{href:"/dashboard/admin/users",className:"flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDC65"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900",children:"All Users"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Manage all users"})]})]}),(0,t.jsxs)("a",{href:"/dashboard/admin/manuscripts",className:"flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCC4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-green-900",children:"Manuscripts"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"Review submissions"})]})]}),(0,t.jsxs)("a",{href:"/dashboard/admin/reviews",className:"flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCD6"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-purple-900",children:"Reviews"}),(0,t.jsx)("p",{className:"text-sm text-purple-700",children:"Monitor reviews"})]})]}),(0,t.jsxs)("a",{href:"/dashboard/admin/settings",className:"flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"⚙️"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:"Settings"}),(0,t.jsx)("p",{className:"text-sm text-gray-700",children:"System settings"})]})]})]})]})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD12"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-500 mb-6",children:"You don't have permission to access the admin dashboard."}),(0,t.jsx)("a",{href:"/dashboard",className:"inline-flex items-center px-6 py-3 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors font-medium",children:"Back to Dashboard"})]})})}function f(){return(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]}),(0,t.jsx)("div",{className:"h-12 w-12 bg-gray-200 rounded"})]})},s))})}function y(){return(0,t.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 animate-pulse",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-20"})]}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]},s))})}function N(){return(0,t.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded animate-pulse",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded-full"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32"})]})]}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16"})]},s))})}},79551:e=>{"use strict";e.exports=require("url")},83249:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(37413),a=r(84168);async function i({children:e}){return(0,t.jsx)("div",{className:"bg-gray-100 pt-20",children:(0,t.jsxs)("div",{className:"flex min-h-[calc(100vh-5rem)]",children:[(0,t.jsx)(a.default,{}),(0,t.jsx)("div",{className:"flex-1 lg:ml-0 w-full lg:w-auto",children:e})]})})}},84168:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\Sidebar.tsx","default")},87137:(e,s,r)=>{Promise.resolve().then(r.bind(r,44125)),Promise.resolve().then(r.bind(r,4769)),Promise.resolve().then(r.t.bind(r,46533,23))},94862:(e,s,r)=>{Promise.resolve().then(r.bind(r,8658))},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,2190,5663,9404,7945,1658,7328,3384,1795],()=>r(46653));module.exports=t})();