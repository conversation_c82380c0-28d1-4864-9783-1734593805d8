(()=>{var e={};e.id=4048,e.ids=[4048],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5956:(e,s,r)=>{"use strict";r.d(s,{z:()=>a});var t=r(96330);let a=globalThis.prisma||new t.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30371:(e,s,r)=>{"use strict";r.r(s),r.d(s,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var t={};r.r(t),r.d(t,{GET:()=>c,POST:()=>d});var a=r(96559),i=r(48088),o=r(37719),n=r(56814),u=r(5956),l=r(32190);async function d(e,{params:s}){let r=await (0,n.j2)();if(!r?.user?.id)return l.NextResponse.json({error:"Unauthorized"},{status:401});try{let{content:t,sender:a}=await e.json(),{id:i}=await s;if(!t||!t.trim())return l.NextResponse.json({error:"Message content is required"},{status:400});if(!["REVIEWER","EDITOR","AUTHOR"].includes(a))return l.NextResponse.json({error:"Invalid sender type"},{status:400});let o=await u.z.review.findUnique({where:{id:i},include:{manuscript:{select:{author_id:!0}}}});if(!o)return l.NextResponse.json({error:"Review not found"},{status:404});let n=o.reviewer_id===r.user.id,d=o.manuscript.author_id===r.user.id,c="ADMIN"===r.user.role;if(!n&&!d&&!c)return l.NextResponse.json({error:"Forbidden"},{status:403});if("REVIEWER"===a&&!n)return l.NextResponse.json({error:"Cannot send as reviewer"},{status:403});if("AUTHOR"===a&&!d)return l.NextResponse.json({error:"Cannot send as author"},{status:403});if("EDITOR"===a&&!c)return l.NextResponse.json({error:"Cannot send as editor"},{status:403});let p=await u.z.reviewMessage.create({data:{content:t.trim(),sender:a,reviewId:i,userId:r.user.id},include:{user:{select:{name:!0,email:!0}}}}),m=[];return"REVIEWER"!==a&&n&&m.push(u.z.notification.create({data:{userId:o.reviewer_id,title:"New Review Message",message:`New message from ${a.toLowerCase()} about review`,type:"REVIEW_MESSAGE",relatedId:i}})),"AUTHOR"!==a&&d&&m.push(u.z.notification.create({data:{userId:o.manuscript.author_id,title:"New Review Message",message:`New message from ${a.toLowerCase()} about your manuscript review`,type:"REVIEW_MESSAGE",relatedId:i}})),await Promise.all(m),l.NextResponse.json({success:!0,message:"Message sent successfully",data:p})}catch(e){return console.error("Failed to send message:",e),l.NextResponse.json({error:"Failed to send message"},{status:500})}}async function c(e,{params:s}){let r=await (0,n.j2)();if(!r?.user?.id)return l.NextResponse.json({error:"Unauthorized"},{status:401});try{let{id:e}=await s,t=await u.z.review.findUnique({where:{id:e},include:{manuscript:{select:{author_id:!0}}}});if(!t)return l.NextResponse.json({error:"Review not found"},{status:404});let a=t.reviewer_id===r.user.id,i=t.manuscript.author_id===r.user.id,o="ADMIN"===r.user.role;if(!a&&!i&&!o)return l.NextResponse.json({error:"Forbidden"},{status:403});let n=await u.z.reviewMessage.findMany({where:{reviewId:e},include:{user:{select:{name:!0,email:!0}}},orderBy:{createdAt:"asc"}});return l.NextResponse.json({success:!0,data:n})}catch(e){return console.error("Failed to fetch messages:",e),l.NextResponse.json({error:"Failed to fetch messages"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/reviews/[id]/messages/route",pathname:"/api/reviews/[id]/messages",filename:"route",bundlePath:"app/api/reviews/[id]/messages/route"},resolvedPagePath:"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\api\\reviews\\[id]\\messages\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:g}=p;function h(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56814:(e,s,r)=>{"use strict";r.d(s,{Y9:()=>l,j2:()=>p});var t=r(19443),a=r(16467),i=r(5956),o=r(10189),n=r(56056),u=r(85663);let{handlers:l,signIn:d,signOut:c,auth:p}=(0,t.Ay)({adapter:(0,a.y)(i.z),secret:process.env.AUTH_SECRET,trustHost:!0,session:{strategy:"jwt"},pages:{signIn:"/auth/signin"},providers:[n.A,(0,o.A)({credentials:{email:{label:"Email",type:"email",placeholder:"Enter your email"},password:{label:"Password",type:"password",placeholder:"Enter your password"}},async authorize(e){if(!e?.email||!e?.password)return null;try{let s=await i.z.user.findUnique({where:{email:e.email},select:{id:!0,email:!0,name:!0,image:!0,password:!0,role:!0}});if(!s||!s.password||!await u.Ay.compare(e.password,s.password))return null;return{id:s.id,email:s.email,name:s.name,image:s.image,role:s.role}}catch(e){return console.error("Authentication error:",e),null}}})],callbacks:{async authorized({auth:e,request:{nextUrl:s}}){let r=!!e?.user,t=s.pathname.startsWith("/dashboard"),a=s.pathname.startsWith("/manuscripts"),i=s.pathname.startsWith("/reviews");return console.log("\uD83D\uDD10 Authorized callback - auth:",e),console.log("\uD83D\uDD10 Authorized callback - isLoggedIn:",r),console.log("\uD83D\uDD10 Authorized callback - pathname:",s.pathname),!t&&!a&&!i||r},session:async({session:e,token:s})=>(console.log("\uD83D\uDD10 Session callback - session:",e),console.log("\uD83D\uDD10 Session callback - token:",s),s?.sub&&(e.user.id=s.sub,s.name&&(e.user.name=s.name),s.email&&(e.user.email=s.email),s.picture&&(e.user.image=s.picture),s.role&&(e.user.role=s.role)),e),jwt:async({token:e,user:s,account:r})=>(console.log("\uD83D\uDD10 JWT callback - token:",e),console.log("\uD83D\uDD10 JWT callback - user:",s),console.log("\uD83D\uDD10 JWT callback - account:",r),s&&(e.sub=s.id,e.name=s.name,e.email=s.email,e.picture=s.image,e.role=s.role),e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,s,r)=>{"use strict";e.exports=r(44870)}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,2190,5663,9404],()=>r(30371));module.exports=t})();